import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  <PERSON><PERSON><PERSON>,
  Button,
  Card,
  Icon,
  Input,
  Textarea,
  Switch
} from '@/shared/components/common';

interface MemoryItem {
  id: string;
  title: string;
  content: string;
  enabled: boolean;
  type: 'text' | 'file' | 'url';
}

interface MemoryConfigData {
  items: MemoryItem[];
  maxMemorySize: number;
  autoCleanup: boolean;
}

interface MemoryConfigProps {
  initialData?: MemoryConfigData;
  onSave?: (data: MemoryConfigData) => void;
}

const MemoryConfig: React.FC<MemoryConfigProps> = ({
  initialData,
  onSave
}) => {
  const { t } = useTranslation(['admin', 'common']);

  const [configData, setConfigData] = useState<MemoryConfigData>(initialData || {
    items: [],
    maxMemorySize: 1000,
    autoCleanup: true
  });

  const [showAddForm, setShowAddForm] = useState(false);
  const [editingItem, setEditingItem] = useState<MemoryItem | null>(null);
  const [formData, setFormData] = useState({
    title: '',
    content: '',
    type: 'text' as 'text' | 'file' | 'url'
  });

  const handleAddItem = () => {
    const newItem: MemoryItem = {
      id: Date.now().toString(),
      title: formData.title,
      content: formData.content,
      enabled: true,
      type: formData.type
    };

    const newConfigData = {
      ...configData,
      items: [...configData.items, newItem]
    };

    setConfigData(newConfigData);
    setShowAddForm(false);
    setFormData({ title: '', content: '', type: 'text' });
    
    if (onSave) onSave(newConfigData);
  };

  const handleEditItem = (item: MemoryItem) => {
    const updatedItems = configData.items.map(i => 
      i.id === item.id ? item : i
    );

    const newConfigData = {
      ...configData,
      items: updatedItems
    };

    setConfigData(newConfigData);
    setEditingItem(null);
    
    if (onSave) onSave(newConfigData);
  };

  const handleDeleteItem = (id: string) => {
    const newConfigData = {
      ...configData,
      items: configData.items.filter(item => item.id !== id)
    };

    setConfigData(newConfigData);
    if (onSave) onSave(newConfigData);
  };

  const handleToggleItem = (id: string) => {
    const updatedItems = configData.items.map(item =>
      item.id === id ? { ...item, enabled: !item.enabled } : item
    );

    const newConfigData = {
      ...configData,
      items: updatedItems
    };

    setConfigData(newConfigData);
    if (onSave) onSave(newConfigData);
  };

  const openEditForm = (item: MemoryItem) => {
    setEditingItem(item);
    setFormData({
      title: item.title,
      content: item.content,
      type: item.type
    });
  };

  const closeForm = () => {
    setShowAddForm(false);
    setEditingItem(null);
    setFormData({ title: '', content: '', type: 'text' });
  };

  return (
    <div className="space-y-6">
      {/* Memory Settings */}
      <Card>
        <div className="p-4">
          <Typography variant="h6" className="mb-4">
            Memory Settings
          </Typography>
          
          <div className="space-y-4">
            <div>
              <label className="block text-sm font-medium mb-2">
                Max Memory Size (MB)
              </label>
              <Input
                type="number"
                value={configData.maxMemorySize}
                onChange={(e) => {
                  const newConfigData = {
                    ...configData,
                    maxMemorySize: parseInt(e.target.value) || 1000
                  };
                  setConfigData(newConfigData);
                  if (onSave) onSave(newConfigData);
                }}
                fullWidth
              />
            </div>

            <div className="flex items-center justify-between">
              <div>
                <Typography variant="subtitle2">Auto Cleanup</Typography>
                <Typography variant="body2" className="text-gray-600">
                  Automatically remove old memories when limit is reached
                </Typography>
              </div>
              <Switch
                checked={configData.autoCleanup}
                onChange={(checked) => {
                  const newConfigData = {
                    ...configData,
                    autoCleanup: checked
                  };
                  setConfigData(newConfigData);
                  if (onSave) onSave(newConfigData);
                }}
              />
            </div>
          </div>
        </div>
      </Card>

      {/* Memory Items */}
      <Card>
        <div className="p-4">
          <div className="flex items-center justify-between mb-4">
            <Typography variant="h6">
              Memory Items ({configData.items.length})
            </Typography>
            <Button
              variant="outline"
              size="sm"
              icon="plus"
              onClick={() => setShowAddForm(true)}
            >
              Add Memory
            </Button>
          </div>

          {/* Items List */}
          <div className="space-y-3">
            {configData.items.map((item) => (
              <Card key={item.id} variant="outlined" className="border border-gray-200">
                <div className="p-4">
                  <div className="flex items-start justify-between">
                    <div className="flex-1">
                      <div className="flex items-center gap-2 mb-2">
                        <Typography variant="subtitle2" className="font-semibold">
                          {item.title}
                        </Typography>
                        <span className={`px-2 py-1 text-xs rounded-full ${
                          item.type === 'text' ? 'bg-blue-100 text-blue-800' :
                          item.type === 'file' ? 'bg-green-100 text-green-800' :
                          'bg-purple-100 text-purple-800'
                        }`}>
                          {item.type}
                        </span>
                      </div>
                      <Typography variant="body2" className="text-gray-600 mb-2">
                        {item.content.length > 100 ? `${item.content.substring(0, 100)}...` : item.content}
                      </Typography>
                    </div>

                    <div className="flex items-center gap-3 ml-4">
                      <Switch
                        checked={item.enabled}
                        onChange={() => handleToggleItem(item.id)}
                        size="sm"
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        icon="edit"
                        onClick={() => openEditForm(item)}
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        icon="trash"
                        onClick={() => handleDeleteItem(item.id)}
                        className="text-red-600 hover:text-red-700"
                      />
                    </div>
                  </div>
                </div>
              </Card>
            ))}

            {configData.items.length === 0 && (
              <div className="text-center py-8 text-gray-500 bg-gray-50 rounded-lg border border-dashed border-gray-300">
                No memory items configured
              </div>
            )}
          </div>
        </div>
      </Card>

      {/* Add/Edit Form */}
      {(showAddForm || editingItem) && (
        <Card>
          <div className="p-4">
            <Typography variant="h6" className="mb-4">
              {editingItem ? 'Edit Memory Item' : 'Add Memory Item'}
            </Typography>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">Title *</label>
                <Input
                  value={formData.title}
                  onChange={(e) => setFormData({ ...formData, title: e.target.value })}
                  placeholder="Enter memory title"
                  fullWidth
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Type</label>
                <div className="flex gap-2">
                  {(['text', 'file', 'url'] as const).map((type) => (
                    <button
                      key={type}
                      onClick={() => setFormData({ ...formData, type })}
                      className={`px-3 py-2 rounded-md text-sm capitalize ${
                        formData.type === type
                          ? 'bg-blue-500 text-white'
                          : 'bg-gray-100 text-gray-700 hover:bg-gray-200'
                      }`}
                    >
                      {type}
                    </button>
                  ))}
                </div>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">Content *</label>
                <Textarea
                  value={formData.content}
                  onChange={(e) => setFormData({ ...formData, content: e.target.value })}
                  placeholder="Enter memory content"
                  rows={4}
                  fullWidth
                />
              </div>

              <div className="flex justify-end gap-3">
                <Button variant="secondary" onClick={closeForm}>
                  Cancel
                </Button>
                <Button
                  variant="primary"
                  onClick={editingItem ? () => handleEditItem({
                    ...editingItem,
                    title: formData.title,
                    content: formData.content,
                    type: formData.type
                  }) : handleAddItem}
                  disabled={!formData.title.trim() || !formData.content.trim()}
                >
                  {editingItem ? 'Update' : 'Add'}
                </Button>
              </div>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default MemoryConfig;
