import React from 'react';
import { useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Typography, Button, Icon } from '@/shared/components/common';
import TypeAgentDetailView from '../components/TypeAgentDetailView';

interface TypeAgent {
  id: number;
  name: string;
  description: string;
  createdAt: string;
  status: string;
}

const SelectAgentTypePage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const navigate = useNavigate();

  const handleSelectType = (typeAgent: TypeAgent) => {
    // Chuyển đến trang tạo agent với type đã chọn
    navigate(`/admin/ai-agents/add?typeId=${typeAgent.id}&typeName=${encodeURIComponent(typeAgent.name)}`);
  };

  const handleBack = () => {
    navigate('/admin/agent/template');
  };

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                icon="arrow-left"
                onClick={handleBack}
                className="mr-4"
              >
                {t('common:back')}
              </Button>
              <Typography variant="h5" className="font-semibold">
                {t('admin:agentTemplate.selectType')}
              </Typography>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <div className="mb-6">
          <Typography variant="body1" className="text-gray-600 dark:text-gray-400">
            {t('admin:agentTemplate.selectTypeDescription')}
          </Typography>
        </div>

        <TypeAgentDetailView
          onSelectType={handleSelectType}
        />
      </div>
    </div>
  );
};

export default SelectAgentTypePage;
