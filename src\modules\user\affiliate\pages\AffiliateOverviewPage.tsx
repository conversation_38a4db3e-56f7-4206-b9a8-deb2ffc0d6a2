import React, { useMemo, useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Button,
  CollapsibleCard,
  Table,
  ResponsiveGrid,
  Input,
  EmptyState,
} from '@/shared/components/common';
import { ListOverviewCard } from '@/shared/components/widgets';
import { LineChart } from '@/shared/components/charts';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { MousePointer, Users, ShoppingCart, DollarSign, Copy } from 'lucide-react';
import {
  useAffiliateOverview,
  useAffiliateOrders,
  useWithdrawalHistory,
  useAffiliateCustomers,
  useAffiliateContracts,
  usePointConversions,
} from '../hooks';
import {
  ORDER_STATUS_MAP,
  WITHDRAWAL_STATUS_MAP,
  CONTRACT_STATUS_MAP,
  POINT_CONVERSION_STATUS_MAP,
} from '../constants';
import { formatCurrency } from '@/shared/utils/number-format.utils';
import { formatDate } from '@/shared/utils/date';
import { WithdrawalForm, ConvertPointsForm, ChartFilter } from '../components';
import type { OverviewCardProps } from '@/shared/components/widgets/OverviewCard/OverviewCard.types';
import type { ChartFilterData } from '../components/ChartFilter';
import type { AffiliateCommissionData, AffiliateOrder, AffiliateCustomer } from '../types';

// Wallet view states
type WalletViewState = 'wallet' | 'withdrawal' | 'convertPoints';

/**
 * Trang tổng quan Affiliate
 */
const AffiliateOverviewPage: React.FC = () => {
  const { t } = useTranslation(['userAffiliate', 'common']);
  const [walletViewState, setWalletViewState] = useState<WalletViewState>('wallet');

  // Chart filter state
  const [chartFilter, setChartFilter] = useState<ChartFilterData>({
    dateRange: [null, null],
    dataType: 'commission',
  });

  // Search and filter states
  const [ordersSearch, setOrdersSearch] = useState('');
  const [withdrawalsSearch, setWithdrawalsSearch] = useState('');
  const [customersSearch, setCustomersSearch] = useState('');
  const [contractsSearch, setContractsSearch] = useState('');
  const [pointConversionsSearch, setPointConversionsSearch] = useState('');

  // Hooks
  const { data: overviewData } = useAffiliateOverview();
  const { data: ordersData, isLoading: ordersLoading } = useAffiliateOrders({ limit: 5 });
  const { data: withdrawalsData, isLoading: withdrawalsLoading } = useWithdrawalHistory({
    limit: 5,
  });
  const { data: customersData, isLoading: customersLoading } = useAffiliateCustomers({ limit: 5 });
  const { data: contractsData, isLoading: contractsLoading } = useAffiliateContracts({ limit: 5 });
  const { data: pointConversionsData, isLoading: pointConversionsLoading } = usePointConversions({
    limit: 5,
  });

  // Overview cards data
  const overviewStats: OverviewCardProps[] = useMemo(() => {
    if (!overviewData?.stats) return [];

    const { stats } = overviewData;
    return [
      {
        title: t('userAffiliate:overview.stats.clicks'),
        value: stats.totalClicks.toLocaleString(),
        description: '+12%',
        icon: MousePointer,
        color: 'blue',
      },
      {
        title: t('userAffiliate:overview.stats.customers'),
        value: stats.totalCustomers.toLocaleString(),
        description: '+8%',
        icon: Users,
        color: 'green',
      },
      {
        title: t('userAffiliate:overview.stats.orders'),
        value: stats.totalOrders.toLocaleString(),
        description: '+15%',
        icon: ShoppingCart,
        color: 'orange',
      },
      {
        title: t('userAffiliate:overview.stats.revenue'),
        value: formatCurrency(stats.totalRevenue),
        description: '+22%',
        icon: DollarSign,
        color: 'purple',
      },
    ];
  }, [overviewData, t]);

  // Chart data based on filter
  const chartData = useMemo(() => {
    if (!overviewData) return [];

    // Lấy dữ liệu theo loại được chọn
    let data: (AffiliateCommissionData | AffiliateOrder | AffiliateCustomer)[] = [];
    switch (chartFilter.dataType) {
      case 'commission':
        data = overviewData.commissionChart || [];
        break;
      case 'orders':
        data = overviewData.recentOrders || [];
        break;
      case 'customers':
        data = overviewData.recentCustomers || [];
        break;
      default:
        data = overviewData.commissionChart || [];
    }

    // Lọc theo khoảng thời gian nếu có
    if (chartFilter.dateRange[0] && chartFilter.dateRange[1]) {
      const startDate = chartFilter.dateRange[0];
      const endDate = chartFilter.dateRange[1];

      data = data.filter(
        (item): item is AffiliateCommissionData | AffiliateOrder | AffiliateCustomer => {
          let dateToCheck: string | undefined;

          // Xác định trường ngày tùy theo loại dữ liệu
          if ('month' in item) {
            // AffiliateCommissionData - không có ngày cụ thể, bỏ qua filter
            return true;
          } else if ('createdAt' in item) {
            // AffiliateOrder
            dateToCheck = (item as AffiliateOrder).createdAt;
          } else if ('registeredAt' in item) {
            // AffiliateCustomer
            dateToCheck = (item as AffiliateCustomer).registeredAt;
          }

          if (!dateToCheck) return true;

          const itemDate = new Date(dateToCheck);
          return itemDate >= startDate && itemDate <= endDate;
        }
      );
    }

    return data;
  }, [overviewData, chartFilter]);

  // Chart line config based on data type
  const chartLineConfig = useMemo(() => {
    const configs = {
      commission: {
        dataKey: 'commission',
        name: t('userAffiliate:chartFilter.dataTypes.commission'),
        color: '#3B82F6',
      },
      orders: {
        dataKey: 'orders',
        name: t('userAffiliate:chartFilter.dataTypes.orders'),
        color: '#10B981',
      },
      customers: {
        dataKey: 'customers',
        name: t('userAffiliate:chartFilter.dataTypes.customers'),
        color: '#F59E0B',
      },
    };

    return configs[chartFilter.dataType as keyof typeof configs] || configs.commission;
  }, [chartFilter.dataType, t]);

  // Table columns for orders
  const orderColumns = useMemo(
    () => [
      {
        title: t('userAffiliate:orders.orderCode'),
        dataIndex: 'orderCode',
        key: 'orderCode',
      },
      {
        title: t('userAffiliate:orders.customer'),
        dataIndex: 'customerName',
        key: 'customerName',
      },
      {
        title: t('userAffiliate:orders.amount'),
        dataIndex: 'amount',
        key: 'amount',
        render: (value: unknown) => formatCurrency(value as number),
      },
      {
        title: t('userAffiliate:orders.commission'),
        dataIndex: 'commission',
        key: 'commission',
        render: (value: unknown) => formatCurrency(value as number),
      },
      {
        title: t('userAffiliate:orders.status'),
        dataIndex: 'status',
        key: 'status',
        render: (status: unknown) => (
          <span
            className={`badge badge-${ORDER_STATUS_MAP[status as keyof typeof ORDER_STATUS_MAP]?.color}`}
          >
            {ORDER_STATUS_MAP[status as keyof typeof ORDER_STATUS_MAP]?.label}
          </span>
        ),
      },
    ],
    [t]
  );

  // Table columns for withdrawals
  const withdrawalColumns = useMemo(
    () => [
      {
        title: t('userAffiliate:withdrawals.amount'),
        dataIndex: 'amount',
        key: 'amount',
        render: (value: unknown) => formatCurrency(value as number),
      },
      {
        title: t('userAffiliate:withdrawals.bankAccount'),
        dataIndex: 'bankAccount',
        key: 'bankAccount',
      },
      {
        title: t('userAffiliate:withdrawals.status'),
        dataIndex: 'status',
        key: 'status',
        render: (status: unknown) => (
          <span
            className={`badge badge-${WITHDRAWAL_STATUS_MAP[status as keyof typeof WITHDRAWAL_STATUS_MAP]?.color}`}
          >
            {WITHDRAWAL_STATUS_MAP[status as keyof typeof WITHDRAWAL_STATUS_MAP]?.label}
          </span>
        ),
      },
      {
        title: t('userAffiliate:withdrawals.requestedAt'),
        dataIndex: 'requestedAt',
        key: 'requestedAt',
        render: (date: unknown) => formatDate(date as string),
      },
    ],
    [t]
  );

  // Table columns for customers
  const customerColumns = useMemo(
    () => [
      {
        title: t('userAffiliate:customers.name'),
        dataIndex: 'name',
        key: 'name',
      },
      {
        title: t('userAffiliate:customers.email'),
        dataIndex: 'email',
        key: 'email',
      },
      {
        title: t('userAffiliate:customers.totalOrders'),
        dataIndex: 'totalOrders',
        key: 'totalOrders',
      },
      {
        title: t('userAffiliate:customers.totalSpent'),
        dataIndex: 'totalSpent',
        key: 'totalSpent',
        render: (value: unknown) => formatCurrency(value as number),
      },
      {
        title: t('userAffiliate:customers.registeredAt'),
        dataIndex: 'registeredAt',
        key: 'registeredAt',
        render: (date: unknown) => formatDate(date as string),
      },
    ],
    [t]
  );

  // Table columns for contracts
  const contractColumns = useMemo(
    () => [
      {
        title: t('userAffiliate:contracts.contractCode'),
        dataIndex: 'contractCode',
        key: 'contractCode',
      },
      {
        title: t('userAffiliate:contracts.contractType'),
        dataIndex: 'contractType',
        key: 'contractType',
      },
      {
        title: t('userAffiliate:contracts.commissionRate'),
        dataIndex: 'commissionRate',
        key: 'commissionRate',
        render: (value: unknown) => `${value}%`,
      },
      {
        title: t('userAffiliate:contracts.startDate'),
        dataIndex: 'startDate',
        key: 'startDate',
        render: (date: unknown) => formatDate(date as string),
      },
      {
        title: t('userAffiliate:contracts.endDate'),
        dataIndex: 'endDate',
        key: 'endDate',
        render: (date: unknown) => formatDate(date as string),
      },
      {
        title: t('userAffiliate:contracts.status'),
        dataIndex: 'status',
        key: 'status',
        render: (status: unknown) => (
          <span
            className={`badge badge-${CONTRACT_STATUS_MAP[status as keyof typeof CONTRACT_STATUS_MAP]?.color}`}
          >
            {CONTRACT_STATUS_MAP[status as keyof typeof CONTRACT_STATUS_MAP]?.label}
          </span>
        ),
      },
      {
        title: t('userAffiliate:contracts.signedAt'),
        dataIndex: 'signedAt',
        key: 'signedAt',
        render: (date: unknown) => (date ? formatDate(date as string) : '-'),
      },
    ],
    [t]
  );

  // Table columns for point conversions
  const pointConversionColumns = useMemo(
    () => [
      {
        title: t('userAffiliate:pointConversions.pointsConverted'),
        dataIndex: 'pointsConverted',
        key: 'pointsConverted',
        render: (value: unknown) => {
          const points = value as number;
          return points
            ? `${points.toLocaleString()} ${t('userAffiliate:pointConversions.pointsUnit')}`
            : `0 ${t('userAffiliate:pointConversions.pointsUnit')}`;
        },
      },
      {
        title: t('userAffiliate:pointConversions.amount'),
        dataIndex: 'amount',
        key: 'amount',
        render: (value: unknown) => {
          const amount = value as number;
          return amount ? formatCurrency(amount) : formatCurrency(0);
        },
      },
      {
        title: t('userAffiliate:pointConversions.conversionRate'),
        dataIndex: 'conversionRate',
        key: 'conversionRate',
        render: (value: unknown) => {
          const rate = value as number;
          return rate ? `1:${rate}` : '1:0';
        },
      },
      {
        title: t('userAffiliate:pointConversions.status'),
        dataIndex: 'status',
        key: 'status',
        render: (status: unknown) => {
          const statusKey = status as keyof typeof POINT_CONVERSION_STATUS_MAP;
          const statusInfo = POINT_CONVERSION_STATUS_MAP[statusKey];

          if (!statusInfo) {
            return <span className="badge badge-secondary">-</span>;
          }

          return <span className={`badge badge-${statusInfo.color}`}>{statusInfo.label}</span>;
        },
      },
      {
        title: t('userAffiliate:pointConversions.createdAt'),
        dataIndex: 'createdAt',
        key: 'createdAt',
        render: (timestamp: unknown) => {
          const ts = timestamp as number;
          if (!ts) return '-';
          // Convert Unix timestamp to Date and format
          const date = new Date(ts * 1000);
          return formatDate(date.toISOString());
        },
      },
    ],
    [t]
  );

  const handleWithdrawal = () => {
    setWalletViewState('withdrawal');
  };

  const handleConvertPoints = () => {
    setWalletViewState('convertPoints');
  };

  const handleBackToWallet = () => {
    setWalletViewState('wallet');
  };

  const handleCopyReferralLink = () => {
    if (overviewData?.referralLink?.url) {
      navigator.clipboard.writeText(overviewData.referralLink.url);
      // TODO: Show toast notification
    }
  };

  // Search handlers
  const handleOrdersSearch = (term: string) => {
    setOrdersSearch(term);
    // TODO: Implement search logic with ordersSearch
    console.log('Orders search:', term, ordersSearch);
  };

  const handleWithdrawalsSearch = (term: string) => {
    setWithdrawalsSearch(term);
    // TODO: Implement search logic with withdrawalsSearch
    console.log('Withdrawals search:', term, withdrawalsSearch);
  };

  const handleCustomersSearch = (term: string) => {
    setCustomersSearch(term);
    // TODO: Implement search logic with customersSearch
    console.log('Customers search:', term, customersSearch);
  };

  const handleContractsSearch = (term: string) => {
    setContractsSearch(term);
    // TODO: Implement search logic with contractsSearch
    console.log('Contracts search:', term, contractsSearch);
  };

  const handlePointConversionsSearch = (term: string) => {
    setPointConversionsSearch(term);
    // TODO: Implement search logic with pointConversionsSearch
    console.log('Point conversions search:', term, pointConversionsSearch);
  };

  return (
    <div className="w-full bg-background text-foreground space-y-6">
      {/* Overview Stats */}
      <ListOverviewCard
        items={overviewStats}
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 4, xl: 4 }}
        gap={4}
        isLoading={!overviewData}
        skeletonCount={4}
      />

      {/* Chart with Filter */}
      <Card className="p-6">
        <Typography variant="h5" className="mb-4">
          {t('userAffiliate:overview.commissionChart')}
        </Typography>

        {/* Chart Filter */}
        <ChartFilter value={chartFilter} onChange={setChartFilter} className="mb-4" />

        {/* Chart Content */}
        <div style={{ height: 300, width: '100%' }}>
          {chartData && chartData.length > 0 ? (
            <LineChart
              data={chartData as unknown as Record<string, unknown>[]}
              xAxisKey="month"
              lines={[
                {
                  dataKey: chartLineConfig.dataKey,
                  name: chartLineConfig.name,
                  color: chartLineConfig.color,
                  strokeWidth: 2,
                },
              ]}
              height={300}
              showGrid
              showTooltip
              showLegend
            />
          ) : (
            <div className="flex items-center justify-center h-full">
              <EmptyState
                icon="chart"
                title={t('userAffiliate:overview.noData')}
                description={t('userAffiliate:overview.noData')}
              />
            </div>
          )}
        </div>
      </Card>

      {/* Wallet and Actions */}
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 2 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 2, xl: 2 }}
        gap={6}
      >
        <Card className="p-6">
          {/* Wallet View State */}
          {walletViewState === 'wallet' && (
            <>
              <Typography variant="h5" className="mb-4">
                {t('userAffiliate:wallet.title')}
              </Typography>
              <div className="space-y-4">
                <div className="flex justify-between items-center">
                  <Typography variant="body1" color="muted">
                    {t('userAffiliate:wallet.balance')}
                  </Typography>
                  <Typography variant="h4" color="primary">
                    {formatCurrency(overviewData?.wallet?.balance || 0)}
                  </Typography>
                </div>
                <div className="flex justify-between items-center">
                  <Typography variant="body1" color="muted">
                    {t('userAffiliate:wallet.pending')}
                  </Typography>
                  <Typography variant="h5" color="warning">
                    {formatCurrency(overviewData?.wallet?.pendingAmount || 0)}
                  </Typography>
                </div>
                <div className="flex gap-3 mt-6">
                  <Button variant="primary" onClick={handleWithdrawal} className="flex-1">
                    {t('userAffiliate:wallet.withdraw')}
                  </Button>
                  <Button variant="outline" onClick={handleConvertPoints} className="flex-1">
                    {t('userAffiliate:wallet.convertPoints')}
                  </Button>
                </div>
              </div>
            </>
          )}

          {/* Withdrawal Form */}
          {walletViewState === 'withdrawal' && (
            <WithdrawalForm
              onBack={handleBackToWallet}
              currentBalance={overviewData?.wallet?.balance || 0}
            />
          )}

          {/* Convert Points Form */}
          {walletViewState === 'convertPoints' && (
            <ConvertPointsForm
              onBack={handleBackToWallet}
              currentPoints={1000} // TODO: Get from API
            />
          )}
        </Card>

        {/* Referral Link and Certificate */}
        <Card className="p-6">
          <Typography variant="h5" className="mb-4">
            {t('userAffiliate:referral.title')}
          </Typography>
          <div className="space-y-4">
            <div>
              <Typography variant="body2" color="muted" className="mb-2">
                {t('userAffiliate:referral.link')}
              </Typography>
              <div className="flex gap-2 w-full">
                <Input
                  type="text"
                  value={overviewData?.referralLink?.url || ''}
                  readOnly
                  className="flex-1"
                  fullWidth
                />
                <Button
                  variant="primary"
                  onClick={handleCopyReferralLink}
                  className="flex items-center gap-2 px-4 whitespace-nowrap"
                >
                  <Copy size={16} />
                  {t('userAffiliate:common.copy')}
                </Button>
              </div>
            </div>

            {overviewData?.certificate && (
              <div>
                <Typography variant="body2" color="muted" className="mb-2">
                  {t('userAffiliate:certificate.title')}
                </Typography>
                <img
                  src={overviewData.certificate.imageUrl}
                  alt={overviewData.certificate.title}
                  className="w-full h-32 object-cover rounded-md border"
                />
              </div>
            )}
          </div>
        </Card>
      </ResponsiveGrid>

      {/* Orders List */}
      <CollapsibleCard
        title={
          <Typography variant="h5">
            {t('userAffiliate:orders.title')} ({ordersData?.items?.length || 0})
          </Typography>
        }
        defaultOpen={false}
        lazyLoad={true}
      >
        <MenuIconBar
          onSearch={handleOrdersSearch}
          items={[]}
          showDateFilter={true}
          showColumnFilter={false}
        />
        <Table
          columns={orderColumns}
          data={ordersData?.items || []}
          loading={ordersLoading}
          pagination={false}
        />
        {ordersData?.items?.length === 0 && !ordersLoading && (
          <div className="text-center py-8">
            <Typography variant="body1" color="muted">
              {t('userAffiliate:orders.empty')}
            </Typography>
          </div>
        )}
      </CollapsibleCard>

      {/* Withdrawal History */}
      <CollapsibleCard
        title={
          <Typography variant="h5">
            {t('userAffiliate:withdrawals.title')} ({withdrawalsData?.items?.length || 0})
          </Typography>
        }
        defaultOpen={false}
        lazyLoad={true}
      >
        <MenuIconBar
          onSearch={handleWithdrawalsSearch}
          items={[]}
          showDateFilter={true}
          showColumnFilter={false}
        />
        <Table
          columns={withdrawalColumns}
          data={withdrawalsData?.items || []}
          loading={withdrawalsLoading}
          pagination={false}
        />
        {withdrawalsData?.items?.length === 0 && !withdrawalsLoading && (
          <div className="text-center py-8">
            <Typography variant="body1" color="muted">
              {t('userAffiliate:withdrawals.empty')}
            </Typography>
          </div>
        )}
      </CollapsibleCard>

      {/* Customers List */}
      <CollapsibleCard
        title={
          <Typography variant="h5">
            {t('userAffiliate:customers.title')} ({customersData?.items?.length || 0})
          </Typography>
        }
        defaultOpen={false}
        lazyLoad={true}
      >
        <MenuIconBar
          onSearch={handleCustomersSearch}
          items={[]}
          showDateFilter={true}
          showColumnFilter={false}
        />
        <Table
          columns={customerColumns}
          data={customersData?.items || []}
          loading={customersLoading}
          pagination={false}
        />
        {customersData?.items?.length === 0 && !customersLoading && (
          <div className="text-center py-8">
            <Typography variant="body1" color="muted">
              {t('userAffiliate:customers.empty')}
            </Typography>
          </div>
        )}
      </CollapsibleCard>

      {/* Contracts List */}
      <CollapsibleCard
        title={
          <Typography variant="h5">
            {t('userAffiliate:contracts.title')} ({contractsData?.items?.length || 0})
          </Typography>
        }
        defaultOpen={false}
        lazyLoad={true}
      >
        <MenuIconBar
          onSearch={handleContractsSearch}
          items={[]}
          showDateFilter={true}
          showColumnFilter={false}
        />
        <Table
          columns={contractColumns}
          data={contractsData?.items || []}
          loading={contractsLoading}
          pagination={false}
        />
        {contractsData?.items?.length === 0 && !contractsLoading && (
          <div className="text-center py-8">
            <Typography variant="body1" color="muted">
              {t('userAffiliate:contracts.empty')}
            </Typography>
          </div>
        )}
      </CollapsibleCard>

      {/* Point Conversions List */}
      <CollapsibleCard
        title={
          <Typography variant="h5">
            {t('userAffiliate:pointConversions.title')} (
            {pointConversionsData?.meta?.totalItems || 0})
          </Typography>
        }
        defaultOpen={false}
        lazyLoad={true}
      >
        <MenuIconBar
          onSearch={handlePointConversionsSearch}
          items={[]}
          showDateFilter={true}
          showColumnFilter={false}
        />
        <Table
          columns={pointConversionColumns}
          data={pointConversionsData?.items || []}
          loading={pointConversionsLoading}
          pagination={false}
        />
        {pointConversionsData?.items?.length === 0 && !pointConversionsLoading && (
          <div className="text-center py-8">
            <Typography variant="body1" color="muted">
              {t('userAffiliate:pointConversions.empty')}
            </Typography>
          </div>
        )}
      </CollapsibleCard>
    </div>
  );
};

export default AffiliateOverviewPage;
