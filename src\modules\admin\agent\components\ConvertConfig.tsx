import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Switch,
  FormControlLabel,
  Button,
  IconButton,
  Dialog,
  DialogTitle,
  DialogContent,
  DialogActions
} from '@mui/material';
import { Add as AddIcon, Edit as EditIcon, Delete as DeleteIcon } from '@mui/icons-material';

interface ConvertField {
  id: string;
  name: string;
  type: string;
  required: boolean;
  description: string;
  enabled: boolean;
}

interface ConvertConfigData {
  fields: ConvertField[];
}

interface ConvertConfigProps {
  initialData?: ConvertConfigData;
  onSave?: (data: ConvertConfigData) => void;
}

const ConvertConfig: React.FC<ConvertConfigProps> = ({
  initialData,
  onSave
}) => {
  const { t } = useTranslation(['admin', 'common']);

  const [configData, setConfigData] = useState<ConvertConfigData>(initialData || {
    fields: [
      {
        id: 'field-1',
        name: 'email',
        description: 'Địa chỉ email người dùng',
        enabled: true,
        type: 'string',
        required: true
      },
      {
        id: 'field-2',
        name: 'phone',
        description: 'Số điện thoại người dùng',
        enabled: true,
        type: 'string',
        required: true
      },
      {
        id: 'field-3',
        name: 'name',
        description: 'Họ tên đầy đủ của người dùng',
        enabled: true,
        type: 'string',
        required: true
      },
      {
        id: 'field-4',
        name: 'address',
        description: 'Địa chỉ của người dùng',
        enabled: false,
        type: 'string',
        required: false
      },
      {
        id: 'field-5',
        name: 'birthday',
        description: 'Ngày sinh của người dùng',
        enabled: false,
        type: 'string',
        required: false
      }
    ]
  });

  const [showAddForm, setShowAddForm] = useState(false);
  const [editingField, setEditingField] = useState<ConvertField | null>(null);
  const [formData, setFormData] = useState<Partial<ConvertField>>({});

  const handleToggleField = (id: string) => {
    const updatedFields = configData.fields.map(field => {
      if (field.id === id) {
        return { ...field, enabled: !field.enabled };
      }
      return field;
    });
    const newData = { ...configData, fields: updatedFields };
    setConfigData(newData);
    if (onSave) onSave(newData);
  };

  const handleAddField = () => {
    if (formData.name && formData.type && formData.description) {
      const newField: ConvertField = {
        id: `field-${Date.now()}`,
        name: formData.name,
        type: formData.type,
        description: formData.description,
        required: formData.required || false,
        enabled: true
      };
      
      const newData = {
        ...configData,
        fields: [...configData.fields, newField]
      };
      setConfigData(newData);
      if (onSave) onSave(newData);
      
      setShowAddForm(false);
      setFormData({});
    }
  };

  const handleEditField = () => {
    if (editingField && formData.name && formData.type && formData.description) {
      const updatedFields = configData.fields.map(field => {
        if (field.id === editingField.id) {
          return {
            ...field,
            name: formData.name!,
            type: formData.type!,
            description: formData.description!,
            required: formData.required || false
          };
        }
        return field;
      });
      
      const newData = { ...configData, fields: updatedFields };
      setConfigData(newData);
      if (onSave) onSave(newData);
      
      setEditingField(null);
      setFormData({});
    }
  };

  const handleDeleteField = (id: string) => {
    const updatedFields = configData.fields.filter(field => field.id !== id);
    const newData = { ...configData, fields: updatedFields };
    setConfigData(newData);
    if (onSave) onSave(newData);
  };

  const openEditForm = (field: ConvertField) => {
    setEditingField(field);
    setFormData({
      name: field.name,
      type: field.type,
      description: field.description,
      required: field.required
    });
  };

  const closeForm = () => {
    setShowAddForm(false);
    setEditingField(null);
    setFormData({});
  };

  useEffect(() => {
    if (onSave) onSave(configData);
  }, []);

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        {t('admin:agentTemplate.convertConfig')}
      </Typography>

      <Card>
        <CardContent>
          <Box sx={{ mb: 3 }}>
            <Typography variant="subtitle1" gutterBottom>
              {t('admin:agentTemplate.configureFields')}
            </Typography>
            <Typography variant="body2" color="text.secondary">
              {t('admin:agentTemplate.configureFieldsDescription')}
            </Typography>
          </Box>

          {/* Fields List */}
          <Grid container spacing={2}>
            {configData.fields.map((field) => (
              <Grid item xs={12} key={field.id}>
                <Card variant="outlined">
                  <CardContent sx={{ py: 2 }}>
                    <Box sx={{ display: 'flex', alignItems: 'center', justifyContent: 'space-between' }}>
                      <Box sx={{ flex: 1 }}>
                        <Typography variant="subtitle2" sx={{ fontWeight: 'bold' }}>
                          {field.name}
                        </Typography>
                        <Typography variant="body2" color="text.secondary">
                          {field.description}
                        </Typography>
                        <Typography variant="caption" color="text.secondary">
                          {t('admin:agentTemplate.type')}: {field.type} | {t('admin:agentTemplate.required')}: {field.required ? t('common:yes') : t('common:no')}
                        </Typography>
                      </Box>
                      
                      <Box sx={{ display: 'flex', alignItems: 'center', gap: 1 }}>
                        <FormControlLabel
                          control={
                            <Switch
                              checked={field.enabled}
                              onChange={() => handleToggleField(field.id)}
                              size="small"
                            />
                          }
                          label=""
                        />
                        <IconButton size="small" onClick={() => openEditForm(field)}>
                          <EditIcon fontSize="small" />
                        </IconButton>
                        <IconButton size="small" onClick={() => handleDeleteField(field.id)} color="error">
                          <DeleteIcon fontSize="small" />
                        </IconButton>
                      </Box>
                    </Box>
                  </CardContent>
                </Card>
              </Grid>
            ))}
          </Grid>

          {/* Add Field Button */}
          <Box sx={{ mt: 3, textAlign: 'center' }}>
            <Button
              variant="outlined"
              startIcon={<AddIcon />}
              onClick={() => setShowAddForm(true)}
            >
              {t('admin:agentTemplate.addField')}
            </Button>
          </Box>
        </CardContent>
      </Card>

      {/* Add/Edit Field Dialog */}
      <Dialog open={showAddForm || !!editingField} onClose={closeForm} maxWidth="sm" fullWidth>
        <DialogTitle>
          {editingField ? t('admin:agentTemplate.editField') : t('admin:agentTemplate.addField')}
        </DialogTitle>
        <DialogContent>
          <Grid container spacing={2} sx={{ mt: 1 }}>
            <Grid item xs={12}>
              <TextField
                fullWidth
                label={t('admin:agentTemplate.fieldName')}
                value={formData.name || ''}
                onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControl fullWidth>
                <InputLabel>{t('admin:agentTemplate.fieldType')}</InputLabel>
                <Select
                  value={formData.type || ''}
                  onChange={(e) => setFormData({ ...formData, type: e.target.value })}
                  label={t('admin:agentTemplate.fieldType')}
                >
                  <MenuItem value="string">String</MenuItem>
                  <MenuItem value="number">Number</MenuItem>
                  <MenuItem value="boolean">Boolean</MenuItem>
                </Select>
              </FormControl>
            </Grid>
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label={t('admin:agentTemplate.fieldDescription')}
                value={formData.description || ''}
                onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              />
            </Grid>
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Switch
                    checked={formData.required || false}
                    onChange={(e) => setFormData({ ...formData, required: e.target.checked })}
                  />
                }
                label={t('admin:agentTemplate.required')}
              />
            </Grid>
          </Grid>
        </DialogContent>
        <DialogActions>
          <Button onClick={closeForm}>
            {t('common:cancel')}
          </Button>
          <Button onClick={editingField ? handleEditField : handleAddField} variant="contained">
            {editingField ? t('common:update') : t('common:add')}
          </Button>
        </DialogActions>
      </Dialog>
    </Box>
  );
};

export default ConvertConfig;
