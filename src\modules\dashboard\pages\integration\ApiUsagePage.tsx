import React from 'react';
import { DashboardCard } from '../../components';
import { DashboardWidget } from '../../types';

const ApiUsagePage: React.FC = () => {
  const widgets: DashboardWidget[] = [
    {
      id: 'api-stats',
      title: 'Thống kê API',
      type: 'metric',
      x: 0,
      y: 0,
      w: 12,
      h: 2,
      minW: 6,
      minH: 2,
      isEmpty: true
    },
    {
      id: 'api-usage-chart',
      title: 'Biểu đồ sử dụng API',
      type: 'chart',
      x: 0,
      y: 2,
      w: 8,
      h: 4,
      minW: 6,
      minH: 3,
      isEmpty: true
    },
    {
      id: 'api-endpoints',
      title: 'Top Endpoints',
      type: 'table',
      x: 8,
      y: 2,
      w: 4,
      h: 4,
      minW: 4,
      minH: 3,
      isEmpty: true
    }
  ];

  return (
    <div className="h-full">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-foreground">Sử dụng API</h1>
        <p className="text-muted-foreground mt-1">
          Theo <PERSON>õi vi<PERSON> sử dụng API và hiệu suất các endpoints
        </p>
      </div>
      
      <DashboardCard
        widgets={widgets}
        isDraggable={true}
        isResizable={true}
      />
    </div>
  );
};

export default ApiUsagePage;
