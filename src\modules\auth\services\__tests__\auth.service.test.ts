import { AuthService } from '../auth.service';
import { apiClient } from '@/shared/api';

// Mock the API client
jest.mock('@/shared/api', () => ({
  apiClient: {
    get: jest.fn(),
    post: jest.fn(),
  },
}));

const mockedApiClient = apiClient as jest.Mocked<typeof apiClient>;

describe('AuthService', () => {
  beforeEach(() => {
    jest.clearAllMocks();
  });

  describe('getGoogleAuthUrl', () => {
    it('should call correct endpoint with redirectUri parameter', async () => {
      const redirectUri = 'https://v2.redai.vn/auth/callback';
      const mockResponse = {
        code: 200,
        message: 'Success',
        result: {
          url: 'https://accounts.google.com/o/oauth2/v2/auth?client_id=test&redirect_uri=https%3A%2F%2Fv2.redai.vn%2Fauth%2Fcallback',
        },
      };

      mockedApiClient.get.mockResolvedValue(mockResponse);

      const result = await AuthService.getGoogleAuthUrl(redirectUri);

      expect(mockedApiClient.get).toHaveBeenCalledWith(
        `/v1/auth/google/auth-url?redirectUri=${encodeURIComponent(redirectUri)}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call correct endpoint without redirectUri parameter', async () => {
      const mockResponse = {
        code: 200,
        message: 'Success',
        result: {
          url: 'https://accounts.google.com/o/oauth2/v2/auth?client_id=test',
        },
      };

      mockedApiClient.get.mockResolvedValue(mockResponse);

      const result = await AuthService.getGoogleAuthUrl();

      expect(mockedApiClient.get).toHaveBeenCalledWith('/v1/auth/google/auth-url');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getFacebookAuthUrl', () => {
    it('should call correct endpoint with redirectUri parameter', async () => {
      const redirectUri = 'https://v2.redai.vn/auth/callback';
      const mockResponse = {
        code: 200,
        message: 'Success',
        result: {
          url: 'https://www.facebook.com/v18.0/dialog/oauth?client_id=test&redirect_uri=https%3A%2F%2Fv2.redai.vn%2Fauth%2Fcallback',
          scope: 'email,public_profile,pages_show_list',
          state: 'facebook',
        },
      };

      mockedApiClient.get.mockResolvedValue(mockResponse);

      const result = await AuthService.getFacebookAuthUrl(redirectUri);

      expect(mockedApiClient.get).toHaveBeenCalledWith(
        `/v1/auth/facebook/auth-url?redirectUri=${encodeURIComponent(redirectUri)}`
      );
      expect(result).toEqual(mockResponse);
    });

    it('should call correct endpoint without redirectUri parameter', async () => {
      const mockResponse = {
        code: 200,
        message: 'Success',
        result: {
          url: 'https://www.facebook.com/v18.0/dialog/oauth?client_id=test',
          scope: 'email,public_profile,pages_show_list',
          state: 'facebook',
        },
      };

      mockedApiClient.get.mockResolvedValue(mockResponse);

      const result = await AuthService.getFacebookAuthUrl();

      expect(mockedApiClient.get).toHaveBeenCalledWith('/v1/auth/facebook/auth-url');
      expect(result).toEqual(mockResponse);
    });
  });

  describe('getZaloAuthUrl', () => {
    it('should call correct endpoint with redirectUri parameter', async () => {
      const redirectUri = 'https://v2.redai.vn/auth/callback';
      const mockResponse = {
        code: 200,
        message: 'Success',
        result: {
          url: 'https://oauth.zaloapp.com/v4/permission?client_id=test&redirect_uri=https%3A%2F%2Fv2.redai.vn%2Fauth%2Fcallback',
        },
      };

      mockedApiClient.get.mockResolvedValue(mockResponse);

      const result = await AuthService.getZaloAuthUrl(redirectUri);

      expect(mockedApiClient.get).toHaveBeenCalledWith(
        `/v1/auth/zalo/auth-url?redirectUri=${encodeURIComponent(redirectUri)}`
      );
      expect(result).toEqual(mockResponse);
    });
  });

  describe('loginWithGoogle', () => {
    it('should call correct endpoint with Google auth data', async () => {
      const authData = { code: 'google_auth_code' };
      const mockResponse = {
        code: 200,
        message: 'Success',
        result: {
          user: { id: 1, email: '<EMAIL>' },
          token: 'jwt_token',
        },
      };

      mockedApiClient.post.mockResolvedValue(mockResponse);

      const result = await AuthService.loginWithGoogle(authData);

      expect(mockedApiClient.post).toHaveBeenCalledWith('/auth/google/login', authData);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('loginWithFacebook', () => {
    it('should call correct endpoint with Facebook auth data', async () => {
      const authData = { code: 'facebook_auth_code' };
      const mockResponse = {
        code: 200,
        message: 'Success',
        result: {
          user: { id: 1, email: '<EMAIL>' },
          token: 'jwt_token',
        },
      };

      mockedApiClient.post.mockResolvedValue(mockResponse);

      const result = await AuthService.loginWithFacebook(authData);

      expect(mockedApiClient.post).toHaveBeenCalledWith('/auth/facebook/login', authData);
      expect(result).toEqual(mockResponse);
    });
  });

  describe('loginWithZalo', () => {
    it('should call correct endpoint with Zalo auth data', async () => {
      const authData = { code: 'zalo_auth_code' };
      const mockResponse = {
        code: 200,
        message: 'Success',
        result: {
          user: { id: 1, email: '<EMAIL>' },
          token: 'jwt_token',
        },
      };

      mockedApiClient.post.mockResolvedValue(mockResponse);

      const result = await AuthService.loginWithZalo(authData);

      expect(mockedApiClient.post).toHaveBeenCalledWith('/auth/zalo/login', authData);
      expect(result).toEqual(mockResponse);
    });
  });
});
