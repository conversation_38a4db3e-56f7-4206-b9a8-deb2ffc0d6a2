import { QueryDto } from '@/shared/dto/request/query.dto';

/**
 * Enum giới tính
 */
export enum GenderEnum {
  MALE = 'MALE',
  FEMALE = 'FEMALE',
  OTHER = 'OTHER',
}

/**
 * Enum loại tài kho<PERSON>n
 */
export enum UserTypeEnum {
  INDIVIDUAL = 'INDIVIDUAL',
  BUSINESS = 'BUSINESS',
}

/**
 * Enum trạng thái doanh nghiệp
 */
export enum BusinessStatusEnum {
  PENDING = 'PENDING',
  APPROVED = 'APPROVED',
  REJECTED = 'REJECTED',
}

/**
 * Interface thông tin người dùng
 */
export interface UserDto {
  id: number;
  fullName: string;
  email: string;
  phoneNumber: string;
  isActive: boolean;
  isVerifyEmail: boolean;
  isVerifyPhone: boolean;
  createdAt: number;
  updatedAt: number;
  address: string;
  taxCode: string;
  pointsBalance: number;
  type: UserTypeEnum;
  platform: string;
  citizenId: string;
  citizenIssuePlace: string;
  citizenIssueDate: Date;
  avatar: string;
  avatarUrl?: string;
  dateOfBirth: Date;
  gender: GenderEnum;
  bankCode: string;
  accountNumber: string;
  accountHolder: string;
  bankBranch: string;
}

/**
 * Interface thông tin doanh nghiệp
 */
export interface BusinessInfoDto {
  id: number;
  userId: number;
  businessName: string;
  businessEmail: string;
  businessPhone: string;
  businessRegistrationCertificate: string;
  taxCode: string;
  businessAddress: string;
  representativeName: string;
  representativePosition: string;
  status: BusinessStatusEnum;
  createdAt: number;
  updatedAt: number;
}

/**
 * Interface thông tin ngân hàng
 */
export interface BankInfoDto {
  id: number;
  bankCode: string;
  accountNumber: string;
  accountHolder: string;
  bankBranch?: string;
}

/**
 * Interface thông tin ngân hàng
 */
export interface BankDto {
  bankCode: string;
  bankName: string;
  logoPath: string;
  fullName: string;
  iconPath: string;
  bin: string;
}

/**
 * Interface thông tin ngân hàng từ API /banks/all
 */
export interface BankResponseDto {
  bankCode: string;
  bankName: string;
  logoPath: string;
  fullName: string;
  iconPath: string;
  bin: string;
}

/**
 * Interface cài đặt xác thực hai yếu tố
 */
export interface TwoFactorAuthDto {
  otpSmsEnabled: boolean;
  otpEmailEnabled: boolean;
  googleAuthenticatorEnabled: boolean;
  isGoogleAuthenticatorConfirmed: boolean;
}

/**
 * Interface cài đặt thông báo
 */
export interface UserManageNotificationDto {
  id: number;
  userId: number;
  receiveAccountSystemEmails: boolean;
  receiveBillingEmails: boolean;
  receiveNewFeatureEmails: boolean;
  receiveAffiliateEmails: boolean;
  receiveDocumentationEmails: boolean;
  receivePromotionalEmails: boolean;
}

/**
 * Interface request cập nhật thông tin cá nhân
 */
export interface UpdatePersonalInfoDto {
  fullName?: string;
  gender?: GenderEnum;
  dateOfBirth?: Date | string;
  address?: string;
  phoneNumber?: string;
}

/**
 * Interface request cập nhật thông tin ngân hàng
 */
export interface UpdateBankInfoDto {
  bankCode: string;
  accountNumber: string;
  accountHolder: string;
  bankBranch?: string;
}

/**
 * Interface request cập nhật thông tin doanh nghiệp
 */
export interface UpdateBusinessInfoDto {
  businessName?: string;
  businessEmail?: string;
  businessPhone?: string;
  businessRegistrationCertificate?: string;
  taxCode?: string;
  businessAddress?: string;
  representativeName?: string;
  representativePosition?: string;
}

/**
 * Interface request tạo URL tải lên avatar
 */
export interface AvatarUploadDto {
  imageType: string;
  maxSize: number;
}

/**
 * Interface response URL tải lên avatar
 */
export interface AvatarUploadResponseDto {
  uploadUrl: string;
  avatarKey: string;
  expiresIn: number;
}

/**
 * Interface request cập nhật avatar
 */
export interface UpdateAvatarDto {
  avatarKey: string;
}

/**
 * Interface request cập nhật cài đặt thông báo
 */
export interface UpdateNotificationSettingsDto {
  receiveAccountSystemEmails?: boolean;
  receiveBillingEmails?: boolean;
  receiveNewFeatureEmails?: boolean;
  receiveAffiliateEmails?: boolean;
  receiveDocumentationEmails?: boolean;
  receivePromotionalEmails?: boolean;
}

/**
 * Interface request bật/tắt xác thực hai lớp qua SMS
 */
export interface ToggleSmsAuthDto {
  enabled: boolean;
}

/**
 * Interface request bật/tắt xác thực hai lớp qua email
 */
export interface ToggleEmailAuthDto {
  enabled: boolean;
}

/**
 * Interface request xác nhận cài đặt Google Authenticator
 */
export interface VerifyGoogleAuthDto {
  token: string;
}

/**
 * Interface response thiết lập Google Authenticator
 */
export interface GoogleAuthSetupDto {
  secretKey: string;
  qrCodeUrl: string;
}

/**
 * Interface query lấy danh sách ngân hàng
 */
export interface BankQueryDto extends QueryDto {
  isActive?: boolean;
  code?: string;
}

/**
 * Interface request đổi mật khẩu
 */
export interface ChangePasswordDto {
  currentPassword: string;
  newPassword: string;
  confirmPassword: string;
}

/**
 * Interface response phân trang
 */
export interface PaginatedResult<T> {
  items: T[];
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
  };
}
