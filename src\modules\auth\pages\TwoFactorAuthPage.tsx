import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate, useLocation } from 'react-router-dom';
import {
  Card,
  Typography,
  Button,
  Icon,
  Alert,
  OTPInput,
  Divider,
} from '@/shared/components/common';
import { IconName } from '@/shared/components/common/Icon/Icon';
import { useAuthCommon } from '@/shared/hooks/useAuthCommon';
import { useVerifyTwoFactor, useSelectTwoFactorMethod } from '../hooks/useAuthQuery';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { SelectTwoFactorMethodResponse, VerifyTwoFactorResponse } from '../types/auth.types';
import CountdownTimer from '../components/CountdownTimer';

/**
 * Trang xác thực hai lớp (2FA)
 * Hi<PERSON><PERSON> thị danh sách các phương thức xác thực và cho phép người dùng chọn một phương thức
 */
const TwoFactorAuthPage: React.FC = () => {
  const { t } = useTranslation();
  const navigate = useNavigate();
  const location = useLocation();
  const { twoFactorVerifyToken, twoFactorExpiresAt, enabledMethods, clearAuth, saveTwoFactorInfo: saveTwoFactorInfoCallback } =
    useAuthCommon();
  const { mutate: verifyTwoFactor, isPending: isVerifying } = useVerifyTwoFactor();
  const { mutate: selectTwoFactorMethod, isPending: isSelectingMethod } = useSelectTwoFactorMethod();

  // State
  const [selectedMethod, setSelectedMethod] = useState<string | null>(null);
  const [otp, setOtp] = useState<string>('');
  const [error, setError] = useState<string | null>(null);
  const [isOtpExpired, setIsOtpExpired] = useState<boolean>(false);
  const [isResendingOtp, setIsResendingOtp] = useState<boolean>(false);

  // Kiểm tra token và chuyển hướng nếu không có
  useEffect(() => {
    // Kiểm tra xem có thông tin từ state khi chuyển hướng từ trang đăng nhập xã hội không
    const locationState = location.state as
      | {
          twoFactorToken?: string;
          expiresAt?: number;
          enabledMethods?: Array<{ type: string; value: string }>;
        }
      | undefined;

    if (locationState?.twoFactorToken && locationState?.enabledMethods) {
      // Lưu thông tin vào Redux thông qua hook useAuthCommon
      saveTwoFactorInfoCallback({
        verifyToken: locationState.twoFactorToken,
        expiresAt: locationState.expiresAt || Date.now() + 300000, // Mặc định 5 phút nếu không có
        enabledMethods: locationState.enabledMethods,
      });
    } else if (!twoFactorVerifyToken || !enabledMethods || enabledMethods.length === 0) {
      navigate('/auth');
    }
  }, [twoFactorVerifyToken, enabledMethods, navigate, location.state, saveTwoFactorInfoCallback]);

  // Xử lý khi chọn phương thức xác thực
  const handleSelectMethod = (type: string) => {
    if (!twoFactorVerifyToken) {
      setError(t('auth.sessionExpired', 'Phiên xác thực đã hết hạn. Vui lòng đăng nhập lại.'));
      return;
    }

    // Gọi API select-2fa-method
    selectTwoFactorMethod(
      {
        token: twoFactorVerifyToken,
        platform: type as 'EMAIL' | 'SMS',
      },
      {
        onSuccess: (response: ApiResponseDto<SelectTwoFactorMethodResponse>) => {
          if (response.code === 200) {
            setSelectedMethod(type);
            setOtp('');
            setError(null);
            setIsOtpExpired(false);

            // Cập nhật thời gian hết hạn nếu API trả về
            if (response.result?.expiresAt) {
              saveTwoFactorInfoCallback({
                verifyToken: twoFactorVerifyToken,
                expiresAt: response.result.expiresAt,
                enabledMethods: enabledMethods || [],
              });
            }
          } else {
            setError(response.message || t('auth.selectMethodFailed', 'Không thể chọn phương thức xác thực. Vui lòng thử lại.'));
          }
        },
        onError: (error) => {
          console.error('Select method failed:', error);
          setError(t('auth.selectMethodFailed', 'Không thể chọn phương thức xác thực. Vui lòng thử lại.'));
        },
      }
    );
  };

  // Xử lý khi nhập OTP
  const handleOtpChange = (value: string) => {
    setOtp(value);
    setError(null);
  };

  // Xử lý khi OTP đã nhập đủ
  const handleOtpComplete = (value: string) => {
    setOtp(value);
    // Tự động xác thực khi đã nhập đủ OTP
    if (value.length === 6 && selectedMethod) {
      handleVerify();
    }
  };

  // Xử lý khi nhấn nút xác thực
  const handleVerify = () => {
    if (!selectedMethod || !otp || otp.length !== 6) {
      setError(t('auth.invalidCode', 'Vui lòng nhập mã xác thực 6 số.'));
      return;
    }

    if (!twoFactorVerifyToken) {
      setError(t('auth.sessionExpired', 'Phiên xác thực đã hết hạn. Vui lòng đăng nhập lại.'));
      return;
    }

    verifyTwoFactor(
      {
        verifyToken: twoFactorVerifyToken,
        method: selectedMethod,
        code: otp,
      },
      {
        onSuccess: (response: ApiResponseDto<VerifyTwoFactorResponse>) => {
          // Xử lý khi xác thực thành công
          if (response.code === 200 && response.result) {
            // Lưu token và thông tin người dùng
            if (response.result.accessToken) {
              // Lưu thông tin đăng nhập vào Redux
              // setAuth({
              //   accessToken: response.result.accessToken,
              //   expiresIn: response.result.expiresIn || 0,
              //   user: response.result.user,
              // });

              // Chuyển hướng đến trang chủ
              navigate('/');
            }
          } else {
            setError(t('auth.verificationFailed', 'Xác thực thất bại. Vui lòng thử lại.'));
          }
        },
        onError: error => {
          console.error('Verification failed:', error);
          setError(t('auth.invalidCode', 'Mã xác thực không hợp lệ hoặc đã hết hạn.'));
        },
      }
    );
  };

  // Xử lý khi OTP hết hạn
  const handleOtpExpired = () => {
    setIsOtpExpired(true);
    setError(t('auth.otpExpired', 'Mã xác thực đã hết hạn. Vui lòng gửi lại mã mới.'));
  };

  // Xử lý gửi lại OTP
  const handleResendOtp = () => {
    if (!twoFactorVerifyToken || !selectedMethod) {
      setError(t('auth.sessionExpired', 'Phiên xác thực đã hết hạn. Vui lòng đăng nhập lại.'));
      return;
    }

    setIsResendingOtp(true);
    setError(null);

    // Gọi lại API select-2fa-method để gửi lại OTP
    selectTwoFactorMethod(
      {
        token: twoFactorVerifyToken,
        platform: selectedMethod as 'EMAIL' | 'SMS',
      },
      {
        onSuccess: (response: ApiResponseDto<SelectTwoFactorMethodResponse>) => {
          setIsResendingOtp(false);
          if (response.code === 200) {
            setIsOtpExpired(false);
            setOtp('');

            // Cập nhật thời gian hết hạn nếu API trả về
            if (response.result?.expiresAt) {
              saveTwoFactorInfoCallback({
                verifyToken: twoFactorVerifyToken,
                expiresAt: response.result.expiresAt,
                enabledMethods: enabledMethods || [],
              });
            }
          } else {
            setError(response.message || t('auth.resendOtpError', 'Không thể gửi lại mã xác thực. Vui lòng thử lại.'));
          }
        },
        onError: (error) => {
          setIsResendingOtp(false);
          console.error('Resend OTP failed:', error);
          setError(t('auth.resendOtpError', 'Không thể gửi lại mã xác thực. Vui lòng thử lại.'));
        },
      }
    );
  };

  // Xử lý khi quay lại trang đăng nhập
  const handleBackToLogin = () => {
    clearAuth();
    navigate('/auth');
  };

  // Hiển thị icon tương ứng với phương thức xác thực
  const getMethodIcon = (type: string): IconName => {
    switch (type) {
      case 'EMAIL':
        return 'mail';
      case 'SMS':
        return 'phone';
      case 'GOOGLE_AUTHENTICATOR':
        return 'shield';
      default:
        return 'shield';
    }
  };

  // Nếu không có phương thức xác thực, hiển thị thông báo
  if (!enabledMethods || enabledMethods.length === 0) {
    return (
      <div className="flex justify-center items-center h-full">
        <Card className="w-full max-w-md p-6">
          <Typography variant="h5" className="text-center mb-4">
            {t('auth.twoFactorAuth', 'Xác thực hai lớp')}
          </Typography>
          <Alert
            type="error"
            message={t(
              'auth.noMethodsAvailable',
              'Không có phương thức xác thực nào được cấu hình.'
            )}
            className="mb-4"
          />
          <Button variant="primary" fullWidth onClick={handleBackToLogin}>
            {t('auth.backToLogin', 'Quay lại đăng nhập')}
          </Button>
        </Card>
      </div>
    );
  }

  return (
    <Card className="w-full max-w-md p-6">
      <div className="text-center mb-6">
        <Typography variant="h5" className="mb-2">
          {t('auth.twoFactorAuth', 'Xác thực hai lớp')}
        </Typography>
        <Typography variant="body2" color="muted">
          {selectedMethod
            ? t('auth.enterVerificationCode', 'Vui lòng nhập mã xác thực để tiếp tục.')
            : t(
                'auth.selectVerificationMethod',
                'Vui lòng chọn một phương thức xác thực để tiếp tục.'
              )}
        </Typography>
      </div>

      {error && <Alert type="error" message={error} className="mb-4" />}

      {isSelectingMethod && (
        <Alert type="info" message={t('auth.selectingMethod', 'Đang chọn phương thức xác thực...')} className="mb-4" />
      )}

      {twoFactorExpiresAt && !isOtpExpired && (
        <div className="mb-4 text-center">
          <CountdownTimer
            expiresAt={twoFactorExpiresAt}
            onExpire={handleOtpExpired}
            className="text-sm text-muted"
          />
        </div>
      )}

      {!selectedMethod ? (
        // Hiển thị danh sách các phương thức xác thực
        <div className="space-y-3">
          {enabledMethods?.map((method: { type: string; value: string }, index: number) => (
            <div
              key={index}
              className={`p-4 border border-border rounded-lg cursor-pointer hover:bg-muted/10 transition-colors ${
                isSelectingMethod ? 'opacity-50 pointer-events-none' : ''
              }`}
              onClick={() => !isSelectingMethod && handleSelectMethod(method.type)}
            >
              <div className="flex items-center">
                <div className="mr-4 p-2 bg-primary/10 rounded-full">
                  <Icon name={getMethodIcon(method.type)} size="md" color="primary" />
                </div>
                <div className="flex-1">
                  <Typography variant="subtitle1">
                    {method.type === 'EMAIL'
                      ? t('auth.emailVerification', 'Xác thực qua Email')
                      : method.type === 'SMS'
                        ? t('auth.smsVerification', 'Xác thực qua SMS')
                        : t('auth.appVerification', 'Xác thực qua Ứng dụng')}
                  </Typography>
                  <Typography variant="body2" color="muted">
                    {method.value}
                  </Typography>
                </div>
                <Icon name="chevron-right" size="sm" />
              </div>
            </div>
          ))}

          <div className="mt-6">
            <Button variant="outline" fullWidth onClick={handleBackToLogin}>
              {t('auth.backToLogin', 'Quay lại đăng nhập')}
            </Button>
          </div>
        </div>
      ) : (
        // Hiển thị form nhập OTP
        <div className="space-y-6">
          <div className="flex items-center mb-4">
            <Button
              variant="ghost"
              size="sm"
              className="p-1"
              onClick={() => setSelectedMethod(null)}
            >
              <Icon name="arrow-left" size="sm" />
            </Button>
            <Typography variant="subtitle1" className="ml-2">
              {selectedMethod === 'EMAIL'
                ? t('auth.emailVerification', 'Xác thực qua Email')
                : selectedMethod === 'SMS'
                  ? t('auth.smsVerification', 'Xác thực qua SMS')
                  : t('auth.appVerification', 'Xác thực qua Ứng dụng')}
            </Typography>
          </div>

          <Divider />

          <Typography variant="body2" className="text-center">
            {selectedMethod === 'EMAIL'
              ? t('auth.emailCodeSent', 'Mã xác thực đã được gửi đến email của bạn.')
              : selectedMethod === 'SMS'
                ? t('auth.smsCodeSent', 'Mã xác thực đã được gửi đến số điện thoại của bạn.')
                : t(
                    'auth.useAuthenticatorApp',
                    'Sử dụng ứng dụng Google Authenticator để lấy mã xác thực.'
                  )}
          </Typography>

          <OTPInput
            length={6}
            onChange={handleOtpChange}
            onComplete={handleOtpComplete}
            autoFocus
            disabled={isOtpExpired || isVerifying || isResendingOtp}
          />

          <div className="space-y-3">
            <Button
              variant="primary"
              fullWidth
              onClick={handleVerify}
              isLoading={isVerifying}
              disabled={otp.length !== 6 || isOtpExpired || isResendingOtp}
            >
              {t('auth.verify', 'Xác thực')}
            </Button>

            {(selectedMethod === 'EMAIL' || selectedMethod === 'SMS') && (
              <Button
                variant="outline"
                fullWidth
                onClick={handleResendOtp}
                isLoading={isResendingOtp}
                disabled={isVerifying || isResendingOtp}
              >
                {t('auth.resendCode', 'Gửi lại mã')}
              </Button>
            )}
          </div>
        </div>
      )}
    </Card>
  );
};

export default TwoFactorAuthPage;
