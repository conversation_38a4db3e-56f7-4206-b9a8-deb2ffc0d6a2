import React from 'react';
import { Typography, Button, Icon } from '@/shared/components/common';

interface EmptyDashboardProps {
  title?: string;
  description?: string;
  actionText?: string;
  onAction?: () => void;
  className?: string;
}

const EmptyDashboard: React.FC<EmptyDashboardProps> = ({
  title = 'Không có dữ liệu',
  description = 'Chọn một mục từ menu bên trái để xem báo cáo',
  actionText = 'Trợ giúp về Bảng phân tích',
  onAction,
  className = ''
}) => {
  return (
    <div className={`flex-1 flex flex-col items-center justify-center p-8 ${className}`}>
      <div className="text-center max-w-md">
        {/* Icon */}
        <div className="w-24 h-24 mx-auto mb-6 rounded-full bg-muted flex items-center justify-center">
          <Icon name="bar-chart-3" className="w-12 h-12 text-muted-foreground" />
        </div>

        {/* Title */}
        <Typography variant="h4" className="mb-4 text-foreground">
          {title}
        </Typography>

        {/* Description */}
        <Typography variant="body1" className="text-muted-foreground mb-8 leading-relaxed">
          {description}
        </Typography>

        {/* Action Button */}
        {actionText && (
          <Button
            variant="outline"
            onClick={onAction}
            className="px-6 py-2"
          >
            <Icon name="help-circle" className="w-4 h-4 mr-2" />
            {actionText}
          </Button>
        )}
      </div>
    </div>
  );
};

export default EmptyDashboard;
