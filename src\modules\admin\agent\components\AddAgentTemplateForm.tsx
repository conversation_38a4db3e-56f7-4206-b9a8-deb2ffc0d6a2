import React, { useState, useRef, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Button,
  Input,
  Typography,
  Card,
  Accordion,
  Grid,
  Di<PERSON>r,
  <PERSON><PERSON>,
  AvatarImageUploader
} from '@/shared/components/common';
import { useSmartNotification } from '@/shared/hooks/common/useSmartNotification';
import { apiClient } from '@/shared/api/axios';

// Import components
import TypeAgentDetailView from './TypeAgentDetailView';
import ModelConfig from './ModelConfig';
import ProfileConfig from './ProfileConfig';
import ConvertConfig from './ConvertConfig';

// Types
interface TypeAgent {
  id: number;
  name: string;
  description: string;
  createdAt: string;
  status: string;
}

interface AgentTemplateFormData {
  name: string;
  avatar: string;
  typeId: number | null;
  selectedType: TypeAgent | null;
  modelConfig: {
    provider: string;
    modelId: string;
    instruction: string;
    temperature: number;
    topP: number;
    topK: number;
    maxTokens: number;
    showAdvancedConfig: boolean;
  };
  profile: {
    birthDate: string;
    gender: string;
    language: string;
    education: string;
    country: string;
    position: string;
    skills: string[];
    personality: string;
  };
  conversion: {
    fields: Array<{
      id: string;
      name: string;
      type: string;
      required: boolean;
      description: string;
      enabled: boolean;
    }>;
  };
}

interface AddAgentTemplateFormProps {
  onCancel: () => void;
  onSuccess: (agentId: string) => void;
}

const AddAgentTemplateForm: React.FC<AddAgentTemplateFormProps> = ({ onCancel, onSuccess }) => {
  const { t } = useTranslation(['admin', 'common']);
  const navigate = useNavigate();
  const { showSuccess, showError } = useSmartNotification();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [activeStep, setActiveStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);

  const [formData, setFormData] = useState<AgentTemplateFormData>({
    name: 'Agent mới',
    avatar: '/default-avatar.png',
    typeId: null,
    selectedType: null,
    modelConfig: {
      provider: 'OpenAI',
      modelId: 'gpt-4',
      instruction: '',
      temperature: 1,
      topP: 0.5,
      topK: 20,
      maxTokens: 1000,
      showAdvancedConfig: false
    },
    profile: {
      birthDate: '2000-08-01',
      gender: 'Nữ',
      language: 'English',
      education: 'Cao đẳng',
      country: 'United States',
      position: 'Agent chatbot',
      skills: ['Tư vấn', 'Hỗ trợ khách hàng', 'dev'],
      personality: 'Chuyên nghiệp, hiệu quả, chính xác, khó'
    },
    conversion: {
      fields: []
    }
  });

  const steps = [
    { label: t('admin:agentTemplate.selectType'), icon: 'user-check' },
    { label: t('admin:agentTemplate.basicInfo'), icon: 'user' },
    { label: t('admin:agentTemplate.configuration'), icon: 'settings' }
  ];

  const handleTypeSelect = (typeAgent: TypeAgent) => {
    setFormData(prev => ({
      ...prev,
      typeId: typeAgent.id,
      selectedType: typeAgent
    }));
  };

  const handleNext = () => {
    if (activeStep === 0 && !formData.typeId) {
      showError(t('admin:agentTemplate.pleaseSelectType'));
      return;
    }
    setActiveStep(prev => prev + 1);
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const handleAvatarUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleAvatarFileChange = useCallback((file: File) => {
    const imageUrl = URL.createObjectURL(file);
    setFormData(prev => ({ ...prev, avatar: imageUrl }));
    setAvatarFile(file);
  }, []);

  const handleNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, name: event.target.value }));
  };

  const handleModelConfigSave = (modelConfig: any) => {
    setFormData(prev => ({ ...prev, modelConfig }));
  };

  const handleProfileSave = (profile: any) => {
    setFormData(prev => ({ ...prev, profile }));
  };

  const handleConversionSave = (conversion: any) => {
    setFormData(prev => ({ ...prev, conversion }));
  };

  const handleCancel = () => {
    onCancel();
  };

  const handleSubmit = async () => {
    if (!formData.typeId) {
      showError(t('admin:agentTemplate.pleaseSelectType'));
      return;
    }

    setIsSubmitting(true);
    try {
      // Prepare data for API
      const submitData = {
        name: formData.name,
        typeId: formData.typeId,
        avatarMimeType: avatarFile?.type || null,
        instruction: formData.modelConfig.instruction,
        vectorStoreId: null,
        modelConfig: {
          temperature: formData.modelConfig.temperature,
          top_p: formData.modelConfig.topP,
          top_k: formData.modelConfig.topK,
          max_tokens: formData.modelConfig.maxTokens
        },
        content: [], // Will be populated based on profile
        exampleDefault: [], // Will be populated based on conversion
        systemModelId: formData.modelConfig.modelId
      };

      // Call API to create agent template
      const response = await apiClient.post('/admin/agent-strategy', submitData, {
        tokenType: 'admin'
      });

      if (response.result?.id) {
        // Upload avatar if provided
        if (avatarFile && response.result.avatarUploadUrl) {
          try {
            await fetch(response.result.avatarUploadUrl, {
              method: 'PUT',
              body: avatarFile,
              headers: {
                'Content-Type': avatarFile.type,
              },
            });
          } catch (uploadError) {
            console.error('Avatar upload failed:', uploadError);
          }
        }

        showSuccess(t('admin:agentTemplate.createSuccess'));
        onSuccess(response.result.id);
      }
    } catch (error: any) {
      console.error('Error creating agent template:', error);
      showError(error.response?.data?.message || t('admin:agentTemplate.createError'));
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <TypeAgentDetailView
            onSelectType={handleTypeSelect}
            selectedTypeId={formData.typeId || undefined}
          />
        );
      case 1:
        return (
          <div>
            <Typography variant="h6" className="mb-4">
              {t('admin:agentTemplate.basicInfo')}
            </Typography>

            <Card className="mb-6">
              <div className="p-6">
                <Grid container spacing={6} alignItems="center">
                  <Grid item>
                    <div className="relative">
                      <AvatarImageUploader
                        currentImage={formData.avatar}
                        onImageChange={handleAvatarFileChange}
                        size="lg"
                        className="w-20 h-20"
                      />
                    </div>
                  </Grid>
                  <Grid item xs>
                    <Input
                      label={t('admin:agentTemplate.agentName')}
                      value={formData.name}
                      onChange={handleNameChange}
                      placeholder={t('admin:agentTemplate.agentNamePlaceholder')}
                      className="w-full"
                    />
                    {formData.selectedType && (
                      <Typography variant="body2" className="mt-2 text-gray-600">
                        {t('admin:agentTemplate.selectedType')}: {formData.selectedType.name}
                      </Typography>
                    )}
                  </Grid>
                </Grid>
              </div>
            </Card>
          </div>
        );
      case 2:
        return (
          <div>
            <Typography variant="h6" className="mb-4">
              {t('admin:agentTemplate.configuration')}
            </Typography>

            <Accordion defaultExpanded title={t('admin:agentTemplate.modelConfig')} className="mb-4">
              <ModelConfig
                initialData={formData.modelConfig}
                onSave={handleModelConfigSave}
              />
            </Accordion>

            <Accordion title={t('admin:agentTemplate.profileConfig')} className="mb-4">
              <ProfileConfig
                initialData={formData.profile}
                onSave={handleProfileSave}
              />
            </Accordion>

            <Accordion title={t('admin:agentTemplate.convertConfig')} className="mb-4">
              <ConvertConfig
                initialData={formData.conversion}
                onSave={handleConversionSave}
              />
            </Accordion>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <Typography variant="h4" className="mb-6">
        {t('admin:agentTemplate.addAgentTemplate')}
      </Typography>

      <Stepper
        steps={steps}
        activeStep={activeStep}
        className="mb-8"
      />

      {renderStepContent()}

      <Divider className="my-6" />

      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={activeStep === 0 ? handleCancel : handleBack}
          icon="x"
        >
          {activeStep === 0 ? t('common:cancel') : t('common:back')}
        </Button>

        <div className="flex gap-4">
          {activeStep < steps.length - 1 ? (
            <Button variant="primary" onClick={handleNext} icon="arrow-right">
              {t('common:next')}
            </Button>
          ) : (
            <Button
              variant="primary"
              onClick={handleSubmit}
              disabled={isSubmitting}
              icon="save"
              loading={isSubmitting}
            >
              {isSubmitting ? t('common:saving') : t('common:save')}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default AddAgentTemplateForm;
