/**
 * Types for segment API
 */

import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
/**
 * Segment status enum
 */
export enum SegmentStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
}

/**
 * Condition operator enum
 */
export enum ConditionOperator {
  EQUALS = 'equals',
  NOT_EQUALS = 'not_equals',
  CONTAINS = 'contains',
  NOT_CONTAINS = 'not_contains',
  GREATER_THAN = 'greater_than',
  LESS_THAN = 'less_than',
  IN = 'in',
  NOT_IN = 'not_in',
  EXISTS = 'exists',
  NOT_EXISTS = 'not_exists',
}

/**
 * Segment condition
 */
export interface SegmentCondition {
  id: string;
  field: string;
  operator: ConditionOperator;
  value: string | string[] | number | number[];
}

/**
 * Segment group
 */
export interface SegmentGroup {
  id: string;
  conditions: SegmentCondition[];
  logicalOperator: 'AND' | 'OR';
}

/**
 * Segment entity - cập nhật theo SegmentResponseDto từ API
 */
export interface Segment {
  id: string; // API trả về id dạng string
  name: string;
  description: string;
  criteria: SegmentCriteria;
  audienceCount: number;
  createdAt: string; // API trả về timestamp dạng string
  updatedAt: string; // API trả về timestamp dạng string
}

/**
 * Segment criteria structure for API requests (without IDs)
 */
export interface SegmentCriteria {
  groups: {
    logicalOperator: 'AND' | 'OR';
    conditions: Omit<SegmentCondition, 'id'>[];
  }[];
}

/**
 * Create segment request - API yêu cầu name, description (optional), và criteria
 */
export interface CreateSegmentRequest {
  name: string;
  description?: string;
  criteria: SegmentCriteria;
}

/**
 * Update segment request - API chấp nhận name, description, và criteria
 */
export interface UpdateSegmentRequest {
  name?: string;
  description?: string;
  criteria?: SegmentCriteria;
}

/**
 * Segment stats
 */
export interface SegmentStats {
  segmentId: string;
  segmentName: string;
  totalAudiences: number;
  percentageOfTotal: number;
  updatedAt: number;
  // Legacy fields for backward compatibility
  totalContacts?: number;
  contactsInSegment?: number;
  percentage?: number;
  recentChanges?: {
    date: string;
    count: number;
  }[];
}

/**
 * Segment response
 */
export type SegmentResponse = Segment;

/**
 * Raw API response structure - API trả về { data: [...], meta: {...} }
 */
export type SegmentListApiResponse = ApiResponseDto<{
  data: SegmentResponse[];
  meta: {
    total: number;
    page: number;
    limit: number;
    totalPages: number;
    hasPreviousPage: boolean;
    hasNextPage: boolean;
  };
}>;

/**
 * Transformed response structure - Hook transform thành { items: [...], meta: {...} }
 */
export type SegmentListResponse = {
  items: SegmentResponse[];
  meta: {
    currentPage: number;
    itemsPerPage: number;
    totalItems: number;
    totalPages: number;
    itemCount: number;
    hasItems: boolean;
    hasPreviousPage: boolean;
    hasNextPage: boolean;
  };
};

/**
 * Segment detail response
 */
export type SegmentDetailResponse = ApiResponseDto<SegmentResponse>;

/**
 * Segment stats response
 */
export type SegmentStatsResponse = ApiResponseDto<SegmentStats>;

/**
 * Bulk delete segments response
 */
export interface BulkDeleteSegmentsResult {
  deletedCount: number;
  failedCount: number;
  deletedIds: (string | number)[];
  failedIds: (string | number)[];
  message: string;
}

export type BulkDeleteSegmentsResponse = ApiResponseDto<BulkDeleteSegmentsResult>;

/**
 * Segment query params
 */
export interface SegmentQueryParams {
  search?: string;
  status?: SegmentStatus;
  audienceId?: number;
  page?: number;
  limit?: number;
  sortBy?: string;
  sortDirection?: string;
}

/**
 * Helper function để transform Segment từ API thành SegmentFormValues
 */
export const transformSegmentToFormValues = (segment: Segment): { name: string; description?: string; groups: SegmentGroup[] } => {
  return {
    name: segment.name,
    description: segment.description,
    groups: segment.criteria.groups.map((group, index) => ({
      id: `group-${Date.now()}-${index}`,
      logicalOperator: group.logicalOperator,
      conditions: group.conditions.map((condition, condIndex) => ({
        id: `condition-${Date.now()}-${condIndex}`,
        field: condition.field,
        operator: condition.operator,
        value: condition.value,
      })),
    })),
  };
};
