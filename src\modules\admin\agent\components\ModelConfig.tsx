import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Grid,
  Select,
  SelectOption,
  Slider,
  Checkbox,
  Input,
  Textarea
} from '@/shared/components/common';

interface ModelConfigData {
  provider: string;
  modelId: string;
  instruction: string;
  temperature: number;
  topP: number;
  topK: number;
  maxTokens: number;
  showAdvancedConfig: boolean;
}

interface ModelConfigProps {
  initialData?: ModelConfigData;
  onSave?: (data: ModelConfigData) => void;
}

const ModelConfig: React.FC<ModelConfigProps> = ({
  initialData,
  onSave
}) => {
  const { t } = useTranslation(['admin', 'common']);

  const [configData, setConfigData] = useState<ModelConfigData>(initialData || {
    provider: 'OpenAI',
    modelId: 'gpt-4',
    instruction: '',
    temperature: 1,
    topP: 0.5,
    topK: 20,
    maxTokens: 1000,
    showAdvancedConfig: false
  });

  // Providers available
  const providers = [
    { value: 'OpenAI', label: 'OpenAI' },
    { value: 'Anthropic', label: 'Anthropic' },
    { value: 'Google', label: 'Google' },
    { value: 'Meta', label: 'Meta' },
    { value: 'DeepSeek', label: 'DeepSeek' },
    { value: 'XAI', label: 'XAI' }
  ];

  // Models based on provider
  const getModelsForProvider = (provider: string) => {
    switch (provider) {
      case 'OpenAI':
        return [
          { value: 'gpt-4', label: 'GPT-4' },
          { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' }
        ];
      case 'Anthropic':
        return [
          { value: 'claude-3-opus', label: 'Claude 3 Opus' },
          { value: 'claude-3-sonnet', label: 'Claude 3 Sonnet' }
        ];
      default:
        return [{ value: 'default', label: 'Default Model' }];
    }
  };

  const handleChange = (field: keyof ModelConfigData, value: any) => {
    const newData = { ...configData, [field]: value };
    setConfigData(newData);
    if (onSave) onSave(newData);
  };

  const handleAdvancedConfigToggle = (checked: boolean) => {
    const newData = {
      ...configData,
      showAdvancedConfig: checked,
      // Reset to defaults when unchecked
      temperature: checked ? configData.temperature : 1,
      topP: checked ? configData.topP : 0.5,
      topK: checked ? configData.topK : 20,
      maxTokens: checked ? configData.maxTokens : 1000
    };
    setConfigData(newData);
    if (onSave) onSave(newData);
  };

  useEffect(() => {
    if (onSave) onSave(configData);
  }, []);

  return (
    <div>
      <Typography variant="h6" className="mb-4">
        {t('admin:agentTemplate.modelConfig')}
      </Typography>

      <Card className="mb-6">
        <div className="p-6">
          {/* Provider Selection */}
          <div className="space-y-6">
            <div>
              <Typography variant="subtitle1" className="mb-3">
                {t('admin:agentTemplate.provider')}
              </Typography>
              <Grid columns={{ xs: 2, sm: 3, md: 6 }} columnGap="sm" rowGap="sm">
                {providers.map((provider) => (
                  <Card
                    key={provider.value}
                    className={`cursor-pointer transition-all duration-200 text-center py-3 ${
                      configData.provider === provider.value
                        ? 'border-2 border-blue-500 bg-blue-50'
                        : 'border border-gray-200 hover:border-blue-300'
                    }`}
                    onClick={() => handleChange('provider', provider.value)}
                  >
                    <Typography variant="body2" className="font-medium">
                      {provider.label}
                    </Typography>
                  </Card>
                ))}
              </Grid>
            </div>

            {/* Model Selection */}
            <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
              <div>
                <Select
                  label={t('admin:agentTemplate.model')}
                  value={configData.modelId}
                  onChange={(value) => handleChange('modelId', value)}
                  className="w-full"
                >
                  {getModelsForProvider(configData.provider).map((model) => (
                    <SelectOption key={model.value} value={model.value}>
                      {model.label}
                    </SelectOption>
                  ))}
                </Select>
              </div>
            </div>

            {/* Instruction */}
            <div>
              <Textarea
                label={t('admin:agentTemplate.instruction')}
                placeholder={t('admin:agentTemplate.instructionPlaceholder')}
                value={configData.instruction}
                onChange={(e) => handleChange('instruction', e.target.value)}
                rows={4}
                className="w-full"
              />
            </div>

            {/* Advanced Config Toggle */}
            <div>
              <Checkbox
                checked={configData.showAdvancedConfig}
                onChange={(checked) => handleAdvancedConfigToggle(checked)}
                label={t('admin:agentTemplate.advancedConfig')}
              />
            </div>
          </div>
        </div>
      </Card>

      {/* Advanced Configuration */}
      {configData.showAdvancedConfig && (
        <Card>
          <div className="p-6">
            <Typography variant="h6" className="mb-4 text-blue-600">
              {t('admin:agentTemplate.advancedSettings')}
            </Typography>

            <div className="space-y-6">
              {/* Max Tokens */}
              <div>
                <Typography className="mb-2">
                  {t('admin:agentTemplate.maxTokens')}: {configData.maxTokens}
                </Typography>
                <Slider
                  value={configData.maxTokens}
                  onChange={(value) => handleChange('maxTokens', value)}
                  min={100}
                  max={4096}
                  step={100}
                  className="w-full"
                />
              </div>

              {/* Temperature */}
              <div>
                <Typography className="mb-2">
                  {t('admin:agentTemplate.temperature')}: {configData.temperature}
                </Typography>
                <Slider
                  value={configData.temperature}
                  onChange={(value) => handleChange('temperature', value)}
                  min={0}
                  max={2}
                  step={0.1}
                  className="w-full"
                />
              </div>

              {/* Top P */}
              <div>
                <Typography className="mb-2">
                  {t('admin:agentTemplate.topP')}: {configData.topP}
                </Typography>
                <Slider
                  value={configData.topP}
                  onChange={(value) => handleChange('topP', value)}
                  min={0}
                  max={1}
                  step={0.1}
                  className="w-full"
                />
              </div>

              {/* Top K */}
              <div>
                <Typography className="mb-2">
                  {t('admin:agentTemplate.topK')}: {configData.topK}
                </Typography>
                <Slider
                  value={configData.topK}
                  onChange={(value) => handleChange('topK', value)}
                  min={1}
                  max={50}
                  step={1}
                  className="w-full"
                />
              </div>
            </div>
          </div>
        </Card>
      )}
    </div>
  );
};

export default ModelConfig;
