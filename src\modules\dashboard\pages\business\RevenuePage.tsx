import React from 'react';
import { DashboardCard } from '../../components';
import { DashboardWidget } from '../../types';

const RevenuePage: React.FC = () => {
  const widgets: DashboardWidget[] = [
    {
      id: 'revenue-overview',
      title: 'Tổng quan doanh thu',
      type: 'metric',
      x: 0,
      y: 0,
      w: 12,
      h: 2,
      minW: 6,
      minH: 2,
      isEmpty: true
    },
    {
      id: 'revenue-trend',
      title: 'Xu hướng doanh thu',
      type: 'chart',
      x: 0,
      y: 2,
      w: 8,
      h: 4,
      minW: 6,
      minH: 3,
      isEmpty: true
    },
    {
      id: 'revenue-breakdown',
      title: 'Phân tích doanh thu',
      type: 'chart',
      x: 8,
      y: 2,
      w: 4,
      h: 4,
      minW: 4,
      minH: 3,
      isEmpty: true
    }
  ];

  return (
    <div className="h-full">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-foreground"><PERSON><PERSON><PERSON> thu</h1>
        <p className="text-muted-foreground mt-1">
          <PERSON><PERSON> tích chi tiết doanh thu theo thời gian và kênh bán hàng
        </p>
      </div>
      
      <DashboardCard
        widgets={widgets}
        isDraggable={true}
        isResizable={true}
      />
    </div>
  );
};

export default RevenuePage;
