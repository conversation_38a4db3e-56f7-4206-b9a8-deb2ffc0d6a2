import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  Button,
  Typography,
  Select,
  Card,
  FormGrid,
  Divider,
  Icon,
  Slider,
} from '@/shared/components/common';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { z } from 'zod';
import { apiClient } from '@/shared/api/axios';
import { useAdminAgentNotification } from '../hooks/useAdminAgentNotification';
import { TypeProviderEnum } from '@/modules/ai-agents/types/enums';
import { getProviderIcon } from '@/modules/admin/integration/provider-model/types';

// Import the correct types from service
import {  StrategyContent, StrategyExample, CreateAgentStrategyResponse } from '../agent-strategy/types/agent-strategy.types';

// Types - sử dụng interface từ types file

interface SystemModel {
  id: string;
  modelId: string;
  provider: string;
  name?: string;
}

interface SystemModelsResponse {
  items?: SystemModel[];
  meta?: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
    hasItems: boolean;
  };
}

// Component hiển thị card cho provider
interface ProviderCardProps {
  provider: TypeProviderEnum;
  name: string;
  isSelected: boolean;
  onClick: (provider: TypeProviderEnum) => void;
  disabled?: boolean;
}

const ProviderCard: React.FC<ProviderCardProps> = ({
  provider,
  name,
  isSelected,
  onClick,
  disabled = false
}) => {
  return (
    <Card
      variant={isSelected ? 'elevated' : 'default'}
      className={`cursor-pointer transition-all duration-200 ${
        isSelected
          ? 'border-2 border-primary-500 shadow-md bg-primary-50 dark:bg-primary-900/20'
          : 'hover:border-gray-300 hover:shadow-sm'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={() => !disabled && onClick(provider)}
      noPadding={false}
    >
      <div className="flex items-center p-3">
        <div className="mr-3">
          <Icon name={getProviderIcon(provider)} size="md" />
        </div>
        <div className="font-medium text-foreground">{name}</div>
      </div>
    </Card>
  );
};

interface AddAgentStrategyFormProps {
  onCancel: () => void;
  onSuccess?: () => void;
}

// Schema validation
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const createAgentStrategySchema = (t: any) => z.object({
  name: z.string()
    .min(1, t('admin:agent.strategy.validation.nameRequired', 'Tên chiến lược là bắt buộc'))
    .trim(),
  instruction: z.string()
    .min(1, t('admin:agent.strategy.validation.instructionRequired', 'Hướng dẫn là bắt buộc'))
    .trim(),
  systemModelId: z.string()
    .min(1, t('admin:agent.strategy.validation.systemModelRequired', 'System model là bắt buộc'))
    .uuid(t('admin:agent.strategy.validation.systemModelRequired', 'System Model ID phải là UUID hợp lệ')),
});

const AddAgentStrategyForm: React.FC<AddAgentStrategyFormProps> = ({ onCancel, onSuccess }) => {
  const { t } = useTranslation(['admin', 'common']);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const {
    createSuccess,
    createError,
    uploadSuccess,
    uploadError,
    loadError,
    validationError,
    processing
  } = useAdminAgentNotification();
  
  // Avatar upload states
  const [avatarFiles, setAvatarFiles] = useState<FileWithMetadata[]>([]);
  
  // Model config states
  const [modelConfig, setModelConfig] = useState({
    temperature: 1,
    top_p: 1,
    top_k: 1,
    max_tokens: 1000,
  });

  // Content and example states
  const [contentSteps, setContentSteps] = useState<StrategyContent[]>([
    { stepOrder: 1, content: '' }
  ]);
  const [exampleSteps, setExampleSteps] = useState<StrategyExample[]>([
    { stepOrder: 1, content: '' }
  ]);

  // System models states
  const [systemModels, setSystemModels] = useState<SystemModel[]>([]);
  const [loadingSystemModels, setLoadingSystemModels] = useState(false);
  const [selectedProvider, setSelectedProvider] = useState<TypeProviderEnum>(TypeProviderEnum.OPENAI);

  // Default form values
  const defaultValues = {
    name: '',
    instruction: '',
    systemModelId: '',
  };

  // Load system models - sử dụng useCallback để tránh re-render
  const loadSystemModels = useCallback(async (provider: string) => {
    try {
      setLoadingSystemModels(true);
      // Add required parameters as per API documentation
      const params = new URLSearchParams({
        page: '1',
        limit: '40',
        sortBy: 'systemModels.modelId',
        provider: provider
      });

      const response = await apiClient.get<SystemModelsResponse>(`/admin/system-models?${params.toString()}`);
      console.log('🔍 [AddAgentStrategyForm] System models response:', response);

      // Handle ApiResponseDto structure
      const result = response.result as SystemModelsResponse;
      if (result?.items && Array.isArray(result.items)) {
        setSystemModels(result.items);
      } else if (Array.isArray(result)) {
        // Trường hợp response.result là array trực tiếp
        setSystemModels(result);
      } else {
        console.warn('Unexpected response structure for system models:', response);
        setSystemModels([]);
      }
    } catch (err) {
      console.error('Error loading system models:', err);
      setSystemModels([]);
      loadError(t('admin:agent.strategy.pageTitle', 'Chiến lược Agent'));
    } finally {
      setLoadingSystemModels(false);
    }
  }, [loadError, t]);

  // Load system models khi component mount với provider mặc định
  useEffect(() => {
    loadSystemModels(selectedProvider);
  }, [loadSystemModels, selectedProvider]); // Thêm dependencies

  // Handle provider selection
  const handleProviderSelect = (provider: TypeProviderEnum) => {
    setSelectedProvider(provider);
    // Reset system model selection khi đổi provider
    setSystemModels([]);
  };

  // Handle model config changes
  const handleModelConfigChange = (key: keyof typeof modelConfig, value: number) => {
    setModelConfig(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // Danh sách providers
  const providers = [
    { type: TypeProviderEnum.OPENAI, name: 'OpenAI' },
    { type: TypeProviderEnum.ANTHROPIC, name: 'Anthropic' },
    { type: TypeProviderEnum.GOOGLE, name: 'Google' },
    { type: TypeProviderEnum.META, name: 'Meta' },
    { type: TypeProviderEnum.DEEPSEEK, name: 'DeepSeek' },
    { type: TypeProviderEnum.XAI, name: 'XAI' },
  ];

  // Handle avatar file selection - chỉ cho phép 1 ảnh
  const handleAvatarChange = useCallback((files: FileWithMetadata[]) => {
    // Chỉ lấy file đầu tiên nếu có nhiều file
    if (files.length > 0 && files[0]) {
      setAvatarFiles([files[0]]);
    } else {
      setAvatarFiles([]);
    }
  }, []);

  // Handle content steps
  const addContentStep = () => {
    const newStep: StrategyContent = {
      stepOrder: contentSteps.length + 1,
      content: ''
    };
    setContentSteps([...contentSteps, newStep]);
  };

  const removeContentStep = (index: number) => {
    if (contentSteps.length > 1) {
      const newSteps = contentSteps.filter((_, i) => i !== index);
      // Reorder step numbers
      const reorderedSteps = newSteps.map((step, i) => ({
        ...step,
        stepOrder: i + 1
      }));
      setContentSteps(reorderedSteps);
    }
  };

  const updateContentStep = (index: number, content: string) => {
    const newSteps = [...contentSteps];
    if (newSteps[index]) {
      newSteps[index] = {
        ...newSteps[index],
        content,
        stepOrder: newSteps[index].stepOrder || index + 1
      };
      setContentSteps(newSteps);
    }
  };

  // Handle example steps
  const addExampleStep = () => {
    const newStep: StrategyExample = {
      stepOrder: exampleSteps.length + 1,
      content: ''
    };
    setExampleSteps([...exampleSteps, newStep]);
  };

  const removeExampleStep = (index: number) => {
    if (exampleSteps.length > 1) {
      const newSteps = exampleSteps.filter((_, i) => i !== index);
      // Reorder step numbers
      const reorderedSteps = newSteps.map((step, i) => ({
        ...step,
        stepOrder: i + 1
      }));
      setExampleSteps(reorderedSteps);
    }
  };

  const updateExampleStep = (index: number, content: string) => {
    const newSteps = [...exampleSteps];
    if (newSteps[index]) {
      newSteps[index] = {
        ...newSteps[index],
        content,
        stepOrder: newSteps[index].stepOrder || index + 1
      };
      setExampleSteps(newSteps);
    }
  };

  // Upload image file to S3 using presigned URL
  const uploadImageFile = async (file: File, presignedUrl: string) => {
    console.log(`🔍 [uploadImageFile] Starting upload:`, {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      uploadUrl: presignedUrl
    });

    const response = await fetch(presignedUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': file.type,
      },
      body: file,
    });

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
    }

    console.log('✅ [uploadImageFile] Upload successful');
  };

  // Handle form submission
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleFormSubmit = async (values: Record<string, any>) => {
    console.log('🔍 [AddAgentStrategyForm] Form submitted with values:', values);
    console.log('🔍 [AddAgentStrategyForm] Model config:', modelConfig);
    console.log('🔍 [AddAgentStrategyForm] Avatar files:', avatarFiles);
    console.log('🔍 [AddAgentStrategyForm] Content steps:', contentSteps);
    console.log('🔍 [AddAgentStrategyForm] Example steps:', exampleSteps);

    // Provider đã có default value, không cần validate

    // Validate content and examples
    const validContentSteps = contentSteps.filter(step => step.content.trim() !== '');
    const validExampleSteps = exampleSteps.filter(step => step.content.trim() !== '');

    if (validContentSteps.length === 0) {
      validationError(t('admin:agent.strategy.validation.contentRequired', 'Nội dung các bước là bắt buộc'));
      return;
    }

    if (validExampleSteps.length === 0) {
      validationError(t('admin:agent.strategy.validation.exampleRequired', 'Ví dụ mặc định là bắt buộc'));
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare form data
      // eslint-disable-next-line @typescript-eslint/no-explicit-any
      const strategyData: any = {
        name: values['name'] as string,
        avatarMimeType: avatarFiles.length > 0 && avatarFiles[0] ? avatarFiles[0].file.type : undefined,
        modelConfig,
        instruction: values['instruction'] as string,
        vectorStoreId: null, // Always null as per requirement
        content: validContentSteps,
        exampleDefault: validExampleSteps,
        systemModelId: values['systemModelId'] as string,
      };

      console.log('🔍 [AddAgentStrategyForm] Submitting strategy data:', strategyData);

      // Submit form directly to API
      const response = await apiClient.post<CreateAgentStrategyResponse>('/admin/agent-strategy', strategyData);
      console.log('🔍 [AddAgentStrategyForm] Create response:', response);

      // Upload avatar if provided and we have upload URL
      if (avatarFiles.length > 0 && avatarFiles[0] && response.result?.avatarUploadUrl) {
        try {
          processing(t('admin:agent.strategy.form.uploadingAvatar', 'tải lên avatar'));

          await uploadImageFile(avatarFiles[0].file, response.result.avatarUploadUrl);

          uploadSuccess('Avatar');
        } catch (uploadErr) {
          console.error('Error uploading avatar:', uploadErr);
          uploadError(uploadErr instanceof Error ? uploadErr.message : undefined);
        }
      }

      // Show success notification
      createSuccess(t('admin:agent.strategy.pageTitle', 'Chiến lược Agent'));

      // Call success callback
      if (onSuccess) {
        onSuccess();
      }

    } catch (err) {
      console.error('Error creating strategy:', err);
      createError(
        t('admin:agent.strategy.pageTitle', 'Chiến lược Agent'),
        err instanceof Error ? err.message : undefined
      );
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <Typography variant="h5" className="font-semibold">
            {t('admin:agent.strategy.addStrategy', 'Thêm Chiến lược mới')}
          </Typography>
          <Button
            variant="ghost"
            size="sm"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            <Icon name="x" size="sm" />
          </Button>
        </div>
      </div>

      <Form
        schema={createAgentStrategySchema(t)}
        onSubmit={handleFormSubmit}
        defaultValues={defaultValues}
        className="space-y-6"
      >
        {/* Basic Information */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.strategy.form.basicInfo', 'Thông tin cơ bản')}
          </Typography>

          <FormGrid columns={1} gap="md">
            <FormItem
              name="name"
              label={t('admin:agent.strategy.form.name', 'Tên Chiến lược')}
              required
            >
              <Input
                fullWidth
                placeholder={t('admin:agent.strategy.form.namePlaceholder', 'Nhập tên chiến lược')}
              />
            </FormItem>

            <FormItem
              name="instruction"
              label={t('admin:agent.strategy.form.instruction', 'Hướng dẫn')}
              required
            >
              <Textarea
                fullWidth
                rows={4}
                placeholder={t('admin:agent.strategy.form.instructionPlaceholder', 'Nhập hướng dẫn cho chiến lược')}
              />
            </FormItem>
          </FormGrid>
        </div>

        <Divider />

        {/* Avatar Upload */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.strategy.form.avatar', 'Avatar')}
          </Typography>

          <MultiFileUpload
            label={t('admin:agent.strategy.form.avatarUpload', 'Tải lên avatar')}
            accept="image/jpeg,image/png"
            placeholder={t('admin:agent.strategy.form.avatarHelp', 'Hỗ trợ định dạng: JPG, PNG (chỉ 1 ảnh)')}
            value={avatarFiles}
            onChange={handleAvatarChange}
            mediaOnly={true}
            showPreview={true}
            height="h-32"
          />
        </div>

        <Divider />

        {/* Model Configuration */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.strategy.form.modelConfig', 'Cấu hình Model')}
          </Typography>

          <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
            <div className="space-y-3">
              <label className="block text-sm font-medium text-foreground">
                {t('admin:agent.form.temperature', 'Temperature')}
              </label>
              <Slider
                value={modelConfig.temperature}
                min={0}
                max={2}
                step={0.1}
                onValueChange={(value) => handleModelConfigChange('temperature', value)}
                valueSuffix=""
              />
            </div>

            <div className="space-y-3">
              <label className="block text-sm font-medium text-foreground">
                {t('admin:agent.form.topP', 'Top P')}
              </label>
              <Slider
                value={modelConfig.top_p}
                min={0}
                max={1}
                step={0.1}
                onValueChange={(value) => handleModelConfigChange('top_p', value)}
                valueSuffix=""
              />
            </div>

            <div className="space-y-3">
              <label className="block text-sm font-medium text-foreground">
                {t('admin:agent.form.topK', 'Top K')}
              </label>
              <Slider
                value={modelConfig.top_k}
                min={1}
                max={100}
                step={1}
                onValueChange={(value) => handleModelConfigChange('top_k', value)}
                valueSuffix=""
              />
            </div>

            <div className="space-y-3">
              <label className="block text-sm font-medium text-foreground">
                {t('admin:agent.form.maxTokens', 'Max Tokens')}
              </label>
              <Slider
                value={modelConfig.max_tokens}
                min={1}
                max={4096}
                step={1}
                onValueChange={(value) => handleModelConfigChange('max_tokens', value)}
                valueSuffix=""
              />
            </div>
          </FormGrid>
        </div>

        <Divider />

        {/* Provider Selection */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.provider', 'Loại Provider')}
          </Typography>

          <div className="flex flex-nowrap gap-3 overflow-x-auto pb-2">
            {providers.map((provider) => (
              <ProviderCard
                key={provider.type}
                provider={provider.type}
                name={provider.name}
                isSelected={selectedProvider === provider.type}
                onClick={handleProviderSelect}
                disabled={isSubmitting}
              />
            ))}
          </div>
        </div>

        <Divider />

        {/* Model Selection */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.resources', 'Tài nguyên')}
          </Typography>

          <FormGrid columns={1} gap="md">
            <FormItem
              name="systemModelId"
              label={t('admin:agent.strategy.form.model', 'Model')}
              required
            >
              <Select
                fullWidth
                loading={loadingSystemModels}
                placeholder={t('admin:agent.strategy.form.selectModel', 'Chọn model')}
                options={systemModels.map(model => ({
                  value: model.id,
                  label: model.modelId,
                }))}
              />
            </FormItem>
          </FormGrid>
        </div>

        <Divider />

        {/* Content Steps */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Typography variant="subtitle1" className="font-medium">
              {t('admin:agent.strategy.form.content', 'Nội dung các bước')}
            </Typography>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addContentStep}
              disabled={isSubmitting}
            >
              <Icon name="plus" size="sm" className="mr-2" />
              {t('admin:agent.strategy.form.addStep', 'Thêm bước')}
            </Button>
          </div>

          <div className="space-y-3">
            {contentSteps.map((step, index) => (
              <div key={index} className="flex items-start gap-3">
                <div className="flex-1">
                  <Input
                    fullWidth
                    placeholder={t('admin:agent.strategy.form.contentPlaceholder', 'Nhập nội dung bước {step}', { step: step.stepOrder })}
                    value={step.content}
                    onChange={(e) => updateContentStep(index, e.target.value)}
                  />
                </div>
                {contentSteps.length > 1 && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeContentStep(index)}
                    disabled={isSubmitting}
                    className="text-red-500 hover:text-red-600"
                  >
                    <Icon name="trash" size="sm" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>

        <Divider />

        {/* Example Steps */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Typography variant="subtitle1" className="font-medium">
              {t('admin:agent.strategy.form.exampleDefault', 'Ví dụ mặc định')}
            </Typography>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addExampleStep}
              disabled={isSubmitting}
            >
              <Icon name="plus" size="sm" className="mr-2" />
              {t('admin:agent.strategy.form.addExample', 'Thêm ví dụ')}
            </Button>
          </div>

          <div className="space-y-3">
            {exampleSteps.map((step, index) => (
              <div key={index} className="flex items-start gap-3">
                <div className="flex-1">
                  <Input
                    fullWidth
                    placeholder={t('admin:agent.strategy.form.examplePlaceholder', 'Nhập ví dụ cho bước {step}', { step: step.stepOrder })}
                    value={step.content}
                    onChange={(e) => updateExampleStep(index, e.target.value)}
                  />
                </div>
                {exampleSteps.length > 1 && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeExampleStep(index)}
                    disabled={isSubmitting}
                    className="text-red-500 hover:text-red-600"
                  >
                    <Icon name="trash" size="sm" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-border">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            {t('admin:agent.strategy.cancel', 'Hủy')}
          </Button>
          <Button
            type="submit"
            variant="primary"
            isLoading={isSubmitting}
          >
            {t('admin:agent.strategy.form.create', 'Tạo Chiến lược')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default AddAgentStrategyForm;
