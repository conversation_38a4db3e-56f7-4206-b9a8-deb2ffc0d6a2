import { apiClient } from '@/shared/api/axios';
import {
  AgentTemplateDetail,
  CreateAgentTemplateParams,
  UpdateAgentTemplateParams,
  UpdateAgentTemplateStatusParams,
  AgentTemplateQueryParams,
  RestoreAgentTemplateParams,
  CreateAgentTemplateResponse,
  UpdateAgentTemplateResponse,
  RestoreAgentTemplateResponse,
} from '../types/agent-template.types';

/**
 * Service để tương tác với API agent template của admin
 */
export class AdminAgentTemplateService {
  private baseUrl = '/admin/agent-templates';

  /**
   * <PERSON><PERSON><PERSON> danh sách agent template
   */
  async getAgentTemplates(params: AgentTemplateQueryParams) {
    try {
      const response = await apiClient.get(this.baseUrl, { params, tokenType: 'admin' });
      return response.result;
    } catch (error) {
      console.error('Error fetching agent templates:', error);
      throw error;
    }
  }

  /**
   * <PERSON><PERSON><PERSON> danh sách agent template đã xóa
   */
  async getDeletedAgentTemplates(params: AgentTemplateQueryParams) {
    try {
      const response = await apiClient.get(`${this.baseUrl}/trash`, { params, tokenType: 'admin' });
      return response.result;
    } catch (error) {
      console.error('Error fetching deleted agent templates:', error);
      throw error;
    }
  }

  /**
   * Lấy thông tin chi tiết agent template
   */
  async getAgentTemplateById(id: string): Promise<AgentTemplateDetail> {
    try {
      const response = await apiClient.get<AgentTemplateDetail>(`${this.baseUrl}/${id}`, {
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error(`Error fetching agent template ${id}:`, error);
      throw error;
    }
  }

  /**
   * Lấy thông tin chi tiết agent template đã xóa
   */
  async getDeletedAgentTemplateById(id: string): Promise<AgentTemplateDetail> {
    try {
      const response = await apiClient.get<AgentTemplateDetail>(`${this.baseUrl}/trash/${id}`, {
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error(`Error fetching deleted agent template ${id}:`, error);
      throw error;
    }
  }

  /**
   * Tạo agent template mới
   */
  async createAgentTemplate(data: CreateAgentTemplateParams): Promise<CreateAgentTemplateResponse> {
    try {
      const response = await apiClient.post<CreateAgentTemplateResponse>(this.baseUrl, data, {
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error('Error creating agent template:', error);
      throw error;
    }
  }

  /**
   * Cập nhật agent template
   */
  async updateAgentTemplate(
    id: string,
    data: UpdateAgentTemplateParams
  ): Promise<UpdateAgentTemplateResponse> {
    try {
      const response = await apiClient.patch<UpdateAgentTemplateResponse>(
        `${this.baseUrl}/${id}`,
        data,
        { tokenType: 'admin' }
      );
      return response.result;
    } catch (error) {
      console.error(`Error updating agent template ${id}:`, error);
      throw error;
    }
  }

  /**
   * Cập nhật trạng thái agent template
   */
  async updateAgentTemplateStatus(
    id: string,
    data: UpdateAgentTemplateStatusParams
  ): Promise<boolean> {
    try {
      await apiClient.patch(`${this.baseUrl}/${id}/status`, data, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error updating agent template status ${id}:`, error);
      throw error;
    }
  }

  /**
   * Xóa agent template
   */
  async deleteAgentTemplate(id: string): Promise<boolean> {
    try {
      await apiClient.delete(`${this.baseUrl}/${id}`, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error deleting agent template ${id}:`, error);
      throw error;
    }
  }

  /**
   * Khôi phục agent template
   */
  async restoreAgentTemplate(id: string): Promise<boolean> {
    try {
      await apiClient.put(`${this.baseUrl}/${id}/restore`, {}, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error restoring agent template ${id}:`, error);
      throw error;
    }
  }
}

export const adminAgentTemplateService = new AdminAgentTemplateService();
