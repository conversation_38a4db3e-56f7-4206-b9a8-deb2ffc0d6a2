/**
 * Types for audience API
 */

import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';

/**
 * Audience status enum
 */
export enum AudienceStatus {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
  DRAFT = 'draft',
}

/**
 * Audience type enum
 */
export enum AudienceType {
  CUSTOMER = 'customer',
  LEAD = 'lead',
  SUBSCRIBER = 'subscriber',
  CUSTOM = 'custom',
}

/**
 * Audience attribute
 */
export interface AudienceAttribute {
  id: string;
  name: string;
  value: string;
}

/**
 * Custom field từ API response
 */
export interface AudienceCustomField {
  id: string;
  audienceId: number;
  fieldName: string;
  fieldValue: string;
  fieldType: 'TEXT' | 'NUMBER' | 'DATE' | 'BOOLEAN';
  createdAt: string;
  updatedAt: string;
}

/**
 * Audience entity
 */
export interface Audience {
  id: number;
  name: string;
  email?: string;
  phone?: string;
  avatar?: string; // Thêm avatar URL
  tagIds?: number[];
  tags?: Array<{ id: string; name: string; color?: string; createdBy?: number; updatedBy?: number; createdAt?: string; updatedAt?: string }>; // Thêm tags từ API
  type: AudienceType;
  status: AudienceStatus;
  totalContacts: number;
  attributes?: AudienceAttribute[];
  customFields?: AudienceCustomField[]; // Thêm custom fields từ API
  createdAt: string;
  updatedAt: string;
}

/**
 * Create audience request
 */
export interface CreateAudienceRequest {
  name: string;
  email?: string;
  phone?: string;
  tagIds?: number[];
  attributes?: Omit<AudienceAttribute, 'id'>[];
}

/**
 * Custom field config for audience update
 */
export interface AudienceCustomFieldConfig {
  configId: string;
  label: string;
  type: string;
  required: boolean;
  configJson: {
    placeholder?: string;
    maxLength?: number;
    description?: string;
    [key: string]: unknown;
  };
}

/**
 * Update audience request - theo API format
 */
export interface UpdateAudienceRequest {
  name?: string;
  email?: string;
  phone?: string;
  avatarMediaType?: string; // Thay avatar thành avatarMediaType
  customFields?: AudienceCustomFieldConfig[];
  tagIds?: number[];
  attributes?: Omit<AudienceAttribute, 'id'>[];
}

/**
 * Audience response
 */
export type AudienceResponse = Audience;

/**
 * Contact data structure from API (actual response)
 */
export interface ContactData {
  id: string;
  name?: string;
  email?: string;
  phone?: string;
  avatar?: string;
  customFields?: Array<{
    name?: string;
    value?: string;
  }>;
  tags?: Array<{
    id: string;
    name: string;
    color?: string;
    createdBy?: number;
    updatedBy?: number;
    createdAt?: string;
    updatedAt?: string;
  }>;
  createdAt: string;
  updatedAt: string;
}

/**
 * Actual API response structure for audience list (returns contact data)
 */
export interface ActualAudienceListResult {
  data: ContactData[];
  meta?: {
    totalItems?: number;
    itemCount?: number;
    itemsPerPage?: number;
    totalPages?: number;
    currentPage?: number;
  };
}

/**
 * Audience list response (actual API structure)
 */
export type AudienceListResponse = ApiResponseDto<ActualAudienceListResult>;

/**
 * Audience detail response
 */
export type AudienceDetailResponse = ApiResponseDto<AudienceResponse>;

/**
 * Update audience response với avatar upload URLs
 */
export interface UpdateAudienceResponse {
  id: number;
  employeeId: number;
  name: string;
  email: string;
  phone: string;
  countryCode?: string;
  avatar?: string;
  customFields?: Array<{
    id: number;
    configId: string;
    label: string;
    type: string;
    configJson: Record<string, unknown>;
    employeeId: number;
    userId: number;
    createAt: number;
    status: string;
  }>;
  tags?: Array<{
    id: number;
    name: string;
    color: string;
    createdAt: number;
    updatedAt: number;
  }>;
  createdAt: number;
  updatedAt: number;
  avatarUploadUrl?: string;
  avatarS3Key?: string;
  avatarUploadExpiresAt?: number;
}

/**
 * Update audience API response
 */
export type UpdateAudienceApiResponse = ApiResponseDto<UpdateAudienceResponse>;

/**
 * DTO cho việc tạo trường tùy chỉnh - theo API requirement
 */
export interface CreateCustomFieldDto {
  /**
   * Tên trường tùy chỉnh
   */
  fieldName: string;

  /**
   * Loại trường tùy chỉnh: TEXT, NUMBER, DATE, BOOLEAN
   */
  fieldType: 'TEXT' | 'NUMBER' | 'DATE' | 'BOOLEAN';

  /**
   * Giá trị của trường tùy chỉnh
   */
  fieldValue: unknown;
}

/**
 * DTO cho việc cập nhật hàng loạt các trường tùy chỉnh của audience
 * Sẽ thay thế hoàn toàn các giá trị hiện có
 */
export interface BulkUpdateCustomFieldsDto {
  /**
   * Danh sách các trường tùy chỉnh mới
   */
  fields: CreateCustomFieldDto[];
}

/**
 * Audience query params
 */
export interface AudienceQueryParams {
  search?: string | undefined;
  status?: AudienceStatus;
  type?: AudienceType;
  page?: number;
  limit?: number;
  sortBy?: string | undefined;
  sortDirection?: string | undefined;
}
