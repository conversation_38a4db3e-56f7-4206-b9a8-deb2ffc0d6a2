# Config Router & MultiFileUpload Summary

## ✅ Đã tạo router và cập nhật SystemConfigPage

### 📁 **Files Created/Updated:**

#### **1. Router File:**
- `src/modules/admin/config/routers/configRouter.ts`

#### **2. Updated Page:**
- `src/modules/admin/config/pages/SystemConfigPage.tsx`
- `src/modules/admin/config/pages/index.ts`

### 🛣️ **Router Configuration:**

#### **configRouter.ts:**
```typescript
import { Loading } from '@/shared/components';
import AdminLayout from '@/shared/layouts/AdminLayout';
import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';

// Lazy load config pages
const SystemConfigPage = lazy(() => import('../pages/SystemConfigPage'));

const configRouter: RouteObject[] = [
  {
    path: '/admin/config/system',
    element: (
      <AdminLayout title="Cấu hình hệ thống">
        <Suspense fallback={<Loading />}>
          <SystemConfigPage />
        </Suspense>
      </AdminLayout>
    ),
  },
];

export default configRouter;
```

#### **Features:**
- ✅ **Lazy Loading**: Component được lazy load để optimize performance
- ✅ **AdminLayout**: Sử dụng AdminLayout với title "Cấu hình hệ thống"
- ✅ **Suspense**: Loading fallback khi component đang load
- ✅ **Route Path**: `/admin/config/system`

### 📤 **MultiFileUpload Integration:**

#### **Replaced Input Files with MultiFileUpload:**

**Before (Input File):**
```typescript
<input
  type="file"
  accept=".pdf,.doc,.docx"
  onChange={e => handleFileUpload('personalPrinciple', e.target.files?.[0] || null)}
  className="hidden"
  id="personal-principle-upload"
/>
<label htmlFor="personal-principle-upload">
  Chọn file
</label>
```

**After (MultiFileUpload):**
```typescript
<MultiFileUpload
  label="Mẫu hợp đồng nguyên tắc dành cho cá nhân"
  value={contractFiles.personalPrinciple}
  onChange={files => handleFileUpload('personalPrinciple', files)}
  accept=".pdf,.doc,.docx"
  placeholder="Kéo thả hoặc click để tải lên mẫu hợp đồng nguyên tắc cá nhân"
  height="h-32"
/>
```

#### **5 MultiFileUpload Components:**

**1. Personal Principle Contract:**
- **Label**: "Mẫu hợp đồng nguyên tắc dành cho cá nhân"
- **Accept**: `.pdf,.doc,.docx`
- **Height**: `h-32`

**2. Business Principle Contract:**
- **Label**: "Mẫu hợp đồng nguyên tắc dành cho doanh nghiệp"
- **Accept**: `.pdf,.doc,.docx`
- **Height**: `h-32`

**3. Personal Affiliate Contract:**
- **Label**: "Mẫu hợp đồng affiliate dành cho cá nhân"
- **Accept**: `.pdf,.doc,.docx`
- **Height**: `h-32`

**4. Business Affiliate Contract:**
- **Label**: "Mẫu hợp đồng affiliate dành cho doanh nghiệp"
- **Accept**: `.pdf,.doc,.docx`
- **Height**: `h-32`

**5. Invoice Template:**
- **Label**: "Mẫu hóa đơn đầu vào"
- **Accept**: `.pdf` (PDF only)
- **Height**: `h-32`
- **Special**: Mentions 10MB limit in placeholder

### 🔧 **State Management Updates:**

#### **Updated State Type:**
```typescript
// Before
const [contractFiles, setContractFiles] = useState({
  personalPrinciple: null as File | null,
  // ...
});

// After
const [contractFiles, setContractFiles] = useState({
  personalPrinciple: [] as FileWithMetadata[],
  // ...
});
```

#### **Updated Handler:**
```typescript
// Before
const handleFileUpload = (type: keyof typeof contractFiles, file: File | null) => {
  setContractFiles(prev => ({
    ...prev,
    [type]: file,
  }));
};

// After
const handleFileUpload = (type: keyof typeof contractFiles, files: FileWithMetadata[]) => {
  setContractFiles(prev => ({
    ...prev,
    [type]: files,
  }));
};
```

### 🎨 **UI Improvements:**

#### **MultiFileUpload Benefits:**
- ✅ **Drag & Drop**: Kéo thả files vào upload area
- ✅ **Multiple Files**: Có thể upload nhiều files cùng lúc
- ✅ **File Preview**: Hiển thị danh sách files đã upload
- ✅ **Progress**: Upload progress indicator
- ✅ **Validation**: File type và size validation
- ✅ **Better UX**: Professional upload interface

#### **Consistent Design:**
- ✅ **Height**: Tất cả upload areas có height `h-32`
- ✅ **Placeholders**: Descriptive placeholders cho từng loại file
- ✅ **Accept Types**: Proper file type restrictions
- ✅ **Labels**: Clear labels cho từng upload section

### 🚀 **Usage:**

#### **Add to Main Router:**
```typescript
import configRouter from '@/modules/admin/config/routers/configRouter';

// In main router config
{
  path: '/admin/config/*',
  children: configRouter,
}
```

#### **Navigation:**
```typescript
// Navigate to system config page
navigate('/admin/config/system');
```

### 📋 **Page Structure:**

**3 CollapsibleCard sections:**
1. **📄 Mẫu hợp đồng** (4 MultiFileUpload components)
2. **💳 Cấu hình cổng thanh toán** (3 FormItem + Input)
3. **🧾 Cấu hình mẫu hóa đơn** (1 MultiFileUpload component)

### 🔍 **File Types Supported:**

**Contract Files:**
- `.pdf` - PDF documents
- `.doc` - Word documents (legacy)
- `.docx` - Word documents (modern)

**Invoice Template:**
- `.pdf` - PDF only (with 10MB limit)

## Status: ✅ Completed
Router đã được tạo và SystemConfigPage đã được cập nhật để sử dụng MultiFileUpload component!
