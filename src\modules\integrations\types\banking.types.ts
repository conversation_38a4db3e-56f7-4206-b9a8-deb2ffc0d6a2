/**
 * Types for banking integration
 */

/**
 * ACB Bank Account Form Values
 */
export interface ACBBankAccountFormValues {
  /**
   * Tên chủ tài khoản ngân hàng ACB dành cho cá nhân
   */
  account_holder_name: string;

  /**
   * Số tài khoản ngân hàng ACB cần liên kết
   */
  account_number: string;

  /**
   * Số điện thoại đã dùng để đăng ký tài khoản ngân hàng ACB
   */
  phone_number: string;

  /**
   * Tên gợi nhớ
   */
  label?: string;
}

/**
 * ACB Bank Account Request
 */
export interface ACBBankAccountRequest {
  account_holder_name: string;
  account_number: string;
  phone_number: string;
  label?: string;
}

/**
 * ACB Bank Account Response
 */
export interface ACBBankAccountResponse {
  id: number;
  account_holder_name: string;
  account_number: string;
  phone_number: string;
  label?: string;
  status: 'active' | 'inactive' | 'pending';
  created_at: string;
  updated_at: string;
}

/**
 * MB Bank Account Form Values
 */
export interface MBBankAccountFormValues {
  /**
   * Tên chủ tài khoản ngân hàng MB dành cho cá nhân
   */
  account_holder_name: string;

  /**
   * Số tài khoản ngân hàng MB cần liên kết
   */
  account_number: string;

  /**
   * Số CMND/CCCD đã dùng để đăng ký tài khoản ngân hàng MB
   */
  identification_number: string;

  /**
   * Số điện thoại đã dùng để đăng ký tài khoản ngân hàng MB
   */
  phone_number: string;

  /**
   * Tên gợi nhớ
   */
  label?: string;
}

/**
 * MB Bank Account Request
 */
export interface MBBankAccountRequest {
  account_holder_name: string;
  account_number: string;
  identification_number: string;
  phone_number: string;
  label?: string;
}

/**
 * MB Bank Account Response
 */
export interface MBBankAccountResponse {
  id: number;
  account_holder_name: string;
  account_number: string;
  identification_number: string;
  phone_number: string;
  label?: string;
  status: 'active' | 'inactive' | 'pending';
  created_at: string;
  updated_at: string;
}

/**
 * OCB Bank Account Form Values
 */
export interface OCBBankAccountFormValues {
  /**
   * Tên chủ tài khoản ngân hàng OCB dành cho cá nhân
   */
  account_holder_name: string;

  /**
   * Số tài khoản ngân hàng OCB cần liên kết
   */
  account_number: string;

  /**
   * Số CMND/CCCD đã dùng để đăng ký tài khoản ngân hàng OCB
   */
  identification_number: string;

  /**
   * Số điện thoại đã dùng để đăng ký tài khoản ngân hàng OCB
   */
  phone_number: string;

  /**
   * Tên gợi nhớ
   */
  label?: string;
}

/**
 * OCB Bank Account Request
 */
export interface OCBBankAccountRequest {
  account_holder_name: string;
  account_number: string;
  identification_number: string;
  phone_number: string;
  label?: string;
}

/**
 * OCB Bank Account Response
 */
export interface OCBBankAccountResponse {
  id: number;
  account_holder_name: string;
  account_number: string;
  identification_number: string;
  phone_number: string;
  label?: string;
  status: 'active' | 'inactive' | 'pending';
  created_at: string;
  updated_at: string;
}

/**
 * Kiên Long Bank Account Form Values
 */
export interface KienLongBankAccountFormValues {
  /**
   * Tên chủ tài khoản ngân hàng Kiên Long dành cho cá nhân
   */
  account_holder_name: string;

  /**
   * Số tài khoản ngân hàng Kiên Long cần liên kết
   */
  account_number: string;

  /**
   * Số CMND/CCCD đã dùng để đăng ký tài khoản ngân hàng Kiên Long
   */
  identification_number: string;

  /**
   * Số điện thoại đã dùng để đăng ký tài khoản ngân hàng Kiên Long
   */
  phone_number: string;

  /**
   * Tên gợi nhớ
   */
  label?: string;
}

/**
 * Kiên Long Bank Account Request
 */
export interface KienLongBankAccountRequest {
  account_holder_name: string;
  account_number: string;
  identification_number: string;
  phone_number: string;
  label?: string;
}

/**
 * Kiên Long Bank Account Response
 */
export interface KienLongBankAccountResponse {
  id: number;
  account_holder_name: string;
  account_number: string;
  identification_number: string;
  phone_number: string;
  label?: string;
  status: 'active' | 'inactive' | 'pending';
  created_at: string;
  updated_at: string;
}
