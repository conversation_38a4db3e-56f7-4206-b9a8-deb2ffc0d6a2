import React, { useState, useRef, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import {
  Box,
  Button,
  TextField,
  Typography,
  Avatar,
  Card,
  CardContent,
  Accordion,
  AccordionSummary,
  AccordionDetails,
  Grid,
  <PERSON><PERSON>r,
  <PERSON><PERSON>,
  <PERSON>,
  Stepper,
  StepLabel
} from '@mui/material';
import {
  ExpandMore as ExpandMoreIcon,
  CloudUpload as CloudUploadIcon,
  Save as SaveIcon,
  Cancel as CancelIcon
} from '@mui/icons-material';
import { useSmartNotification } from '@/shared/hooks/common/useSmartNotification';
import { apiClient } from '@/shared/api/axios';

// Import components
import TypeAgentDetailView from './TypeAgentDetailView';
import ModelConfig from './ModelConfig';
import ProfileConfig from './ProfileConfig';
import ConvertConfig from './ConvertConfig';

// Types
interface TypeAgent {
  id: number;
  name: string;
  description: string;
  createdAt: string;
  status: string;
}

interface AgentTemplateFormData {
  name: string;
  avatar: string;
  typeId: number | null;
  selectedType: TypeAgent | null;
  modelConfig: {
    provider: string;
    modelId: string;
    instruction: string;
    temperature: number;
    topP: number;
    topK: number;
    maxTokens: number;
    showAdvancedConfig: boolean;
  };
  profile: {
    birthDate: string;
    gender: string;
    language: string;
    education: string;
    country: string;
    position: string;
    skills: string[];
    personality: string;
  };
  conversion: {
    fields: Array<{
      id: string;
      name: string;
      type: string;
      required: boolean;
      description: string;
      enabled: boolean;
    }>;
  };
}

interface AddAgentTemplateFormProps {
  onCancel: () => void;
  onSuccess: (agentId: string) => void;
}

const AddAgentTemplateForm: React.FC<AddAgentTemplateFormProps> = ({ onCancel, onSuccess }) => {
  const { t } = useTranslation(['admin', 'common']);
  const navigate = useNavigate();
  const { showSuccess, showError } = useSmartNotification();
  const fileInputRef = useRef<HTMLInputElement>(null);

  const [activeStep, setActiveStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);

  const [formData, setFormData] = useState<AgentTemplateFormData>({
    name: 'Agent mới',
    avatar: '/default-avatar.png',
    typeId: null,
    selectedType: null,
    modelConfig: {
      provider: 'OpenAI',
      modelId: 'gpt-4',
      instruction: '',
      temperature: 1,
      topP: 0.5,
      topK: 20,
      maxTokens: 1000,
      showAdvancedConfig: false
    },
    profile: {
      birthDate: '2000-08-01',
      gender: 'Nữ',
      language: 'English',
      education: 'Cao đẳng',
      country: 'United States',
      position: 'Agent chatbot',
      skills: ['Tư vấn', 'Hỗ trợ khách hàng', 'dev'],
      personality: 'Chuyên nghiệp, hiệu quả, chính xác, khó'
    },
    conversion: {
      fields: []
    }
  });

  const steps = [
    t('admin:agentTemplate.selectType'),
    t('admin:agentTemplate.basicInfo'),
    t('admin:agentTemplate.configuration')
  ];

  const handleTypeSelect = (typeAgent: TypeAgent) => {
    setFormData(prev => ({
      ...prev,
      typeId: typeAgent.id,
      selectedType: typeAgent
    }));
  };

  const handleNext = () => {
    if (activeStep === 0 && !formData.typeId) {
      showError(t('admin:agentTemplate.pleaseSelectType'));
      return;
    }
    setActiveStep(prev => prev + 1);
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };

  const handleAvatarUpload = () => {
    if (fileInputRef.current) {
      fileInputRef.current.click();
    }
  };

  const handleAvatarFileChange = useCallback((event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0];
    if (file) {
      const imageUrl = URL.createObjectURL(file);
      setFormData(prev => ({ ...prev, avatar: imageUrl }));
      setAvatarFile(file);
    }
  }, []);

  const handleNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, name: event.target.value }));
  };

  const handleModelConfigSave = (modelConfig: any) => {
    setFormData(prev => ({ ...prev, modelConfig }));
  };

  const handleProfileSave = (profile: any) => {
    setFormData(prev => ({ ...prev, profile }));
  };

  const handleConversionSave = (conversion: any) => {
    setFormData(prev => ({ ...prev, conversion }));
  };

  const handleCancel = () => {
    onCancel();
  };

  const handleSubmit = async () => {
    if (!formData.typeId) {
      showError(t('admin:agentTemplate.pleaseSelectType'));
      return;
    }

    setIsSubmitting(true);
    try {
      // Prepare data for API
      const submitData = {
        name: formData.name,
        typeId: formData.typeId,
        avatarMimeType: avatarFile?.type || null,
        instruction: formData.modelConfig.instruction,
        vectorStoreId: null,
        modelConfig: {
          temperature: formData.modelConfig.temperature,
          top_p: formData.modelConfig.topP,
          top_k: formData.modelConfig.topK,
          max_tokens: formData.modelConfig.maxTokens
        },
        content: [], // Will be populated based on profile
        exampleDefault: [], // Will be populated based on conversion
        systemModelId: formData.modelConfig.modelId
      };

      // Call API to create agent template
      const response = await apiClient.post('/admin/agent-strategy', submitData);

      if (response.result?.id) {
        // Upload avatar if provided
        if (avatarFile && response.result.avatarUploadUrl) {
          try {
            await fetch(response.result.avatarUploadUrl, {
              method: 'PUT',
              body: avatarFile,
              headers: {
                'Content-Type': avatarFile.type,
              },
            });
          } catch (uploadError) {
            console.error('Avatar upload failed:', uploadError);
          }
        }

        showSuccess(t('admin:agentTemplate.createSuccess'));
        onSuccess(response.result.id);
      }
    } catch (error: any) {
      console.error('Error creating agent template:', error);
      showError(error.response?.data?.message || t('admin:agentTemplate.createError'));
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <TypeAgentDetailView
            onSelectType={handleTypeSelect}
            selectedTypeId={formData.typeId || undefined}
          />
        );
      case 1:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              {t('admin:agentTemplate.basicInfo')}
            </Typography>
            
            <Card sx={{ mb: 3 }}>
              <CardContent>
                <Grid container spacing={3} alignItems="center">
                  <Grid item>
                    <Box sx={{ position: 'relative' }}>
                      <Avatar
                        src={formData.avatar}
                        sx={{ width: 80, height: 80, cursor: 'pointer' }}
                        onClick={handleAvatarUpload}
                      />
                      <Box
                        sx={{
                          position: 'absolute',
                          top: 0,
                          left: 0,
                          right: 0,
                          bottom: 0,
                          display: 'flex',
                          alignItems: 'center',
                          justifyContent: 'center',
                          backgroundColor: 'rgba(0,0,0,0.5)',
                          borderRadius: '50%',
                          opacity: 0,
                          cursor: 'pointer',
                          '&:hover': { opacity: 1 }
                        }}
                        onClick={handleAvatarUpload}
                      >
                        <CloudUploadIcon sx={{ color: 'white' }} />
                      </Box>
                    </Box>
                    <input
                      ref={fileInputRef}
                      type="file"
                      accept="image/*"
                      style={{ display: 'none' }}
                      onChange={handleAvatarFileChange}
                    />
                  </Grid>
                  <Grid item xs>
                    <TextField
                      fullWidth
                      label={t('admin:agentTemplate.agentName')}
                      value={formData.name}
                      onChange={handleNameChange}
                      placeholder={t('admin:agentTemplate.agentNamePlaceholder')}
                    />
                    {formData.selectedType && (
                      <Typography variant="body2" color="text.secondary" sx={{ mt: 1 }}>
                        {t('admin:agentTemplate.selectedType')}: {formData.selectedType.name}
                      </Typography>
                    )}
                  </Grid>
                </Grid>
              </CardContent>
            </Card>
          </Box>
        );
      case 2:
        return (
          <Box>
            <Typography variant="h6" gutterBottom>
              {t('admin:agentTemplate.configuration')}
            </Typography>
            
            <Accordion defaultExpanded>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="subtitle1">{t('admin:agentTemplate.modelConfig')}</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <ModelConfig
                  initialData={formData.modelConfig}
                  onSave={handleModelConfigSave}
                />
              </AccordionDetails>
            </Accordion>

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="subtitle1">{t('admin:agentTemplate.profileConfig')}</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <ProfileConfig
                  initialData={formData.profile}
                  onSave={handleProfileSave}
                />
              </AccordionDetails>
            </Accordion>

            <Accordion>
              <AccordionSummary expandIcon={<ExpandMoreIcon />}>
                <Typography variant="subtitle1">{t('admin:agentTemplate.convertConfig')}</Typography>
              </AccordionSummary>
              <AccordionDetails>
                <ConvertConfig
                  initialData={formData.conversion}
                  onSave={handleConversionSave}
                />
              </AccordionDetails>
            </Accordion>
          </Box>
        );
      default:
        return null;
    }
  };

  return (
    <Box sx={{ maxWidth: 1200, mx: 'auto', p: 3 }}>
      <Typography variant="h4" gutterBottom>
        {t('admin:agentTemplate.addAgentTemplate')}
      </Typography>

      <Stepper activeStep={activeStep} sx={{ mb: 4 }}>
        {steps.map((label) => (
          <Step key={label}>
            <StepLabel>{label}</StepLabel>
          </Step>
        ))}
      </Stepper>

      {renderStepContent()}

      <Divider sx={{ my: 3 }} />

      <Box sx={{ display: 'flex', justifyContent: 'space-between' }}>
        <Button
          variant="outlined"
          onClick={activeStep === 0 ? handleCancel : handleBack}
          startIcon={<CancelIcon />}
        >
          {activeStep === 0 ? t('common:cancel') : t('common:back')}
        </Button>

        <Box sx={{ display: 'flex', gap: 2 }}>
          {activeStep < steps.length - 1 ? (
            <Button variant="contained" onClick={handleNext}>
              {t('common:next')}
            </Button>
          ) : (
            <Button
              variant="contained"
              onClick={handleSubmit}
              disabled={isSubmitting}
              startIcon={<SaveIcon />}
            >
              {isSubmitting ? t('common:saving') : t('common:save')}
            </Button>
          )}
        </Box>
      </Box>
    </Box>
  );
};

export default AddAgentTemplateForm;
