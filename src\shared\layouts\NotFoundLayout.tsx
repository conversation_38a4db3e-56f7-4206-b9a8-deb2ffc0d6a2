import { ReactNode } from 'react';
import { SEOWrapper } from '@/shared/components/common';

interface NotFoundLayoutProps {
  children: ReactNode;
  title?: string;
}

const NotFoundLayout = ({ children, title }: NotFoundLayoutProps) => {
  const pageTitle = title || '404 - Not Found';

  return (
    <SEOWrapper title={pageTitle}>
      <div className="min-h-screen flex flex-col items-center justify-center px-4">
        {children}
      </div>
    </SEOWrapper>
  );
};

export default NotFoundLayout;
