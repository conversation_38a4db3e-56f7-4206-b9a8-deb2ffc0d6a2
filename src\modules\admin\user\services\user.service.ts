/**
 * Service cho quản lý người dùng
 */
import { apiClient } from '@/shared/api/axios';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { PaginatedResult } from '@/shared/dto/response/paginated-result.dto';
import {
  User,
  UserQueryDto,
  CreateUserDto,
  UpdateUserDto,
  UpdateUserPasswordDto,
  BlockUserDto,
  UnblockUserDto,
} from '../types/user.types';

const API_BASE_URL = '/admin/users';

/**
 * Lấy danh sách người dùng
 * @param params Tham số truy vấn
 * @returns Danh sách người dùng phân trang
 */
export const getUsers = async (
  params: UserQueryDto
): Promise<ApiResponseDto<PaginatedResult<User>>> => {
  return apiClient.get<PaginatedResult<User>>(API_BASE_URL, { params });
};

/**
 * Lấy thông tin chi tiết người dùng
 * @param id ID người dùng
 * @returns Thông tin chi tiết người dùng
 */
export const getUserById = async (id: number): Promise<ApiResponseDto<User>> => {
  return apiClient.get<User>(`${API_BASE_URL}/${id}`);
};

/**
 * Tạo người dùng mới
 * @param data Dữ liệu tạo người dùng
 * @returns Thông tin người dùng đã tạo
 */
export const createUser = async (data: CreateUserDto): Promise<ApiResponseDto<User>> => {
  return apiClient.post<User>(API_BASE_URL, data);
};

/**
 * Cập nhật thông tin người dùng
 * @param id ID người dùng
 * @param data Dữ liệu cập nhật
 * @returns Thông tin người dùng đã cập nhật
 */
export const updateUser = async (
  id: number,
  data: UpdateUserDto
): Promise<ApiResponseDto<User>> => {
  return apiClient.patch<User>(`${API_BASE_URL}/${id}`, data);
};

/**
 * Cập nhật mật khẩu người dùng
 * @param id ID người dùng
 * @param data Dữ liệu mật khẩu mới
 * @returns Kết quả cập nhật
 */
export const updateUserPassword = async (
  id: number,
  data: UpdateUserPasswordDto
): Promise<ApiResponseDto<void>> => {
  return apiClient.patch<void>(`${API_BASE_URL}/${id}/password`, data);
};

/**
 * Xóa người dùng
 * @param id ID người dùng
 * @returns Kết quả xóa
 */
export const deleteUser = async (id: number): Promise<ApiResponseDto<void>> => {
  return apiClient.delete<void>(`${API_BASE_URL}/${id}`);
};

/**
 * Block người dùng
 * @param id ID người dùng
 * @param data Dữ liệu block
 * @returns Kết quả block
 */
export const blockUser = async (id: number, data: BlockUserDto): Promise<ApiResponseDto<void>> => {
  return apiClient.post<void>(`${API_BASE_URL}/${id}/block`, data);
};

/**
 * Unblock người dùng
 * @param id ID người dùng
 * @param data Dữ liệu unblock
 * @returns Kết quả unblock
 */
export const unblockUser = async (
  id: number,
  data: UnblockUserDto
): Promise<ApiResponseDto<void>> => {
  return apiClient.post<void>(`${API_BASE_URL}/${id}/unblock`, data);
};
