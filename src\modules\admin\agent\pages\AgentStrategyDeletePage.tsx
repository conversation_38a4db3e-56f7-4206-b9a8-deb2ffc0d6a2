import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Button, EmptyState, Loading, Pagination } from '@/shared/components/common';
import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import { useAdminAgentStrategiesTrash } from '../agent-strategy/hooks/useAgentStrategy';
import { AgentStrategyListItem } from '../agent-strategy/types/agent-strategy.types';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';
import AdminAgentStrategyCard from '../components/AdminAgentStrategyCard';

/**
 * Trang hiển thị danh sách Agent Strategies đã xóa (trash)
 */
const AgentStrategyDeletePage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const navigate = useNavigate();

  // Filter states
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [search, setSearch] = useState('');

  // Query params
  const queryParams = {
    page,
    limit,
    search: search || '',
  };

  // Lấy danh sách agent strategies đã xóa
  const { data: strategiesResponse, isLoading, error, refetch } = useAdminAgentStrategiesTrash(queryParams);

  const handleSearch = (term: string) => {
    setSearch(term);
    setPage(1); // Reset về trang đầu khi search
  };

  const handleBackToMain = () => {
    navigate('/admin/agent/strategy');
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset về trang đầu khi thay đổi limit
  };

  // Transform API data to component format
  const strategies: AgentStrategyListItem[] = useMemo(() => {
    console.log('🔍 [AgentStrategyDeletePage] Raw strategiesResponse:', strategiesResponse);

    if (!strategiesResponse) {
      console.log('🔍 [AgentStrategyDeletePage] No data available');
      return [];
    }

    const responseData = strategiesResponse;
    console.log('🔍 [AgentStrategyDeletePage] Response data:', responseData);

    if (!responseData?.items) {
      console.log('🔍 [AgentStrategyDeletePage] No items in response data');
      return [];
    }

    const mappedStrategies = responseData.items.map((item: AgentStrategyListItem) => {
      const mapped = {
        id: item.id,
        name: item.name,
        avatar: item.avatar,
        systemModelId: item.systemModelId || '',
        modelId: item.modelId || '',
      };
      console.log('🔍 [AgentStrategyDeletePage] Original item:', item);
      console.log('🔍 [AgentStrategyDeletePage] Mapped strategy:', mapped);
      return mapped;
    });

    console.log('🔍 [AgentStrategyDeletePage] Final strategies:', mappedStrategies);
    return mappedStrategies;
  }, [strategiesResponse]);

  const totalItems = useMemo(() => {
    if (!strategiesResponse) return 0;
    const responseData = strategiesResponse;
    return responseData?.meta?.totalItems || 0;
  }, [strategiesResponse]);

  // Hiển thị loading
  if (isLoading) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleBackToMain}
          addLabel={t('admin:agent.strategy.backToMain', 'Quay lại danh sách chính')}
          addIcon="arrow-left"
          items={[]}
        />
        <div className="flex justify-center items-center h-64">
          <Loading size="lg" />
        </div>
      </div>
    );
  }

  // Hiển thị lỗi
  if (error) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleBackToMain}
          addLabel={t('admin:agent.strategy.backToMain', 'Quay lại danh sách chính')}
          addIcon="arrow-left"
          items={[]}
        />
        <EmptyState
          icon="alert-triangle"
          title={t('admin:agent.strategy.trash.loadError', 'Lỗi tải dữ liệu')}
          description={error.message || t('admin:agent.strategy.trash.loadError', 'Không thể tải danh sách chiến lược đã xóa')}
          actions={
            <Button
              variant="primary"
              onClick={() => refetch()}
            >
              {t('common:retry', 'Thử lại')}
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleBackToMain}
        addLabel={t('admin:agent.strategy.backToMain', 'Quay lại danh sách chính')}
        addIcon="arrow-left"
        items={[]}
      />

      {strategies.length > 0 ? (
        <>
          <ResponsiveGrid
            maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3 }}
            maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 2 }}
            gap={{ xs: 4, md: 5, lg: 6 }}
          >
            {strategies.map(strategy => (
              <div key={strategy.id} className="h-full">
                <AdminAgentStrategyCard
                  strategy={strategy}
                  allStrategies={strategies}
                  isTrashPage={true}
                  onSuccess={refetch}
                />
              </div>
            ))}
          </ResponsiveGrid>

          {/* Pagination */}
          {totalItems > limit && (
            <div className="mt-6 flex justify-end">
              <Pagination
                currentPage={page}
                totalItems={totalItems}
                itemsPerPage={limit}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleLimitChange}
                itemsPerPageOptions={[10, 20, 50, 100]}
                showItemsPerPageSelector={true}
                showPageInfo={true}
                variant="compact"
                borderless={true}
              />
            </div>
          )}
        </>
      ) : (
        <EmptyState
          icon="trash"
          title={t('admin:agent.strategy.trash.noStrategies', 'Không có chiến lược nào trong thùng rác')}
          description={
            search
              ? t('admin:agent.strategy.trash.noSearchResults', 'Không tìm thấy chiến lược nào phù hợp')
              : t('admin:agent.strategy.trash.noStrategiesDescription', 'Thùng rác trống')
          }
          actions={
            <Button
              variant="primary"
              onClick={handleBackToMain}
            >
              {t('admin:agent.strategy.backToMain', 'Quay lại danh sách chính')}
            </Button>
          }
        />
      )}
    </div>
  );
};

export default AgentStrategyDeletePage;
