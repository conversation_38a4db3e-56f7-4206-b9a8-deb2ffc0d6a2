import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Slider,
  Checkbox,
  FormControlLabel,
  TextField,
  Accordion,
  AccordionSummary,
  AccordionDetails
} from '@mui/material';
import { ExpandMore as ExpandMoreIcon } from '@mui/icons-material';

interface ModelConfigData {
  provider: string;
  modelId: string;
  instruction: string;
  temperature: number;
  topP: number;
  topK: number;
  maxTokens: number;
  showAdvancedConfig: boolean;
}

interface ModelConfigProps {
  initialData?: ModelConfigData;
  onSave?: (data: ModelConfigData) => void;
}

const ModelConfig: React.FC<ModelConfigProps> = ({
  initialData,
  onSave
}) => {
  const { t } = useTranslation(['admin', 'common']);

  const [configData, setConfigData] = useState<ModelConfigData>(initialData || {
    provider: 'OpenAI',
    modelId: 'gpt-4',
    instruction: '',
    temperature: 1,
    topP: 0.5,
    topK: 20,
    maxTokens: 1000,
    showAdvancedConfig: false
  });

  // Providers available
  const providers = [
    { value: 'OpenAI', label: 'OpenAI' },
    { value: 'Anthropic', label: 'Anthropic' },
    { value: 'Google', label: 'Google' },
    { value: 'Meta', label: 'Meta' },
    { value: 'DeepSeek', label: 'DeepSeek' },
    { value: 'XAI', label: 'XAI' }
  ];

  // Models based on provider
  const getModelsForProvider = (provider: string) => {
    switch (provider) {
      case 'OpenAI':
        return [
          { value: 'gpt-4', label: 'GPT-4' },
          { value: 'gpt-3.5-turbo', label: 'GPT-3.5 Turbo' }
        ];
      case 'Anthropic':
        return [
          { value: 'claude-3-opus', label: 'Claude 3 Opus' },
          { value: 'claude-3-sonnet', label: 'Claude 3 Sonnet' }
        ];
      default:
        return [{ value: 'default', label: 'Default Model' }];
    }
  };

  const handleChange = (field: keyof ModelConfigData, value: any) => {
    const newData = { ...configData, [field]: value };
    setConfigData(newData);
    if (onSave) onSave(newData);
  };

  const handleAdvancedConfigToggle = (checked: boolean) => {
    const newData = {
      ...configData,
      showAdvancedConfig: checked,
      // Reset to defaults when unchecked
      temperature: checked ? configData.temperature : 1,
      topP: checked ? configData.topP : 0.5,
      topK: checked ? configData.topK : 20,
      maxTokens: checked ? configData.maxTokens : 1000
    };
    setConfigData(newData);
    if (onSave) onSave(newData);
  };

  useEffect(() => {
    if (onSave) onSave(configData);
  }, []);

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        {t('admin:agentTemplate.modelConfig')}
      </Typography>

      <Card sx={{ mb: 3 }}>
        <CardContent>
          {/* Provider Selection */}
          <Grid container spacing={3}>
            <Grid item xs={12}>
              <Typography variant="subtitle1" gutterBottom>
                {t('admin:agentTemplate.provider')}
              </Typography>
              <Grid container spacing={2}>
                {providers.map((provider) => (
                  <Grid item xs={6} sm={4} md={2} key={provider.value}>
                    <Card
                      sx={{
                        cursor: 'pointer',
                        border: configData.provider === provider.value ? 2 : 1,
                        borderColor: configData.provider === provider.value ? 'primary.main' : 'divider',
                        '&:hover': { borderColor: 'primary.main' }
                      }}
                      onClick={() => handleChange('provider', provider.value)}
                    >
                      <CardContent sx={{ textAlign: 'center', py: 2 }}>
                        <Typography variant="body2">{provider.label}</Typography>
                      </CardContent>
                    </Card>
                  </Grid>
                ))}
              </Grid>
            </Grid>

            {/* Model Selection */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>{t('admin:agentTemplate.model')}</InputLabel>
                <Select
                  value={configData.modelId}
                  onChange={(e) => handleChange('modelId', e.target.value)}
                  label={t('admin:agentTemplate.model')}
                >
                  {getModelsForProvider(configData.provider).map((model) => (
                    <MenuItem key={model.value} value={model.value}>
                      {model.label}
                    </MenuItem>
                  ))}
                </Select>
              </FormControl>
            </Grid>

            {/* Instruction */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={4}
                label={t('admin:agentTemplate.instruction')}
                placeholder={t('admin:agentTemplate.instructionPlaceholder')}
                value={configData.instruction}
                onChange={(e) => handleChange('instruction', e.target.value)}
              />
            </Grid>

            {/* Advanced Config Toggle */}
            <Grid item xs={12}>
              <FormControlLabel
                control={
                  <Checkbox
                    checked={configData.showAdvancedConfig}
                    onChange={(e) => handleAdvancedConfigToggle(e.target.checked)}
                  />
                }
                label={t('admin:agentTemplate.advancedConfig')}
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>

      {/* Advanced Configuration */}
      {configData.showAdvancedConfig && (
        <Card>
          <CardContent>
            <Typography variant="h6" gutterBottom color="primary">
              {t('admin:agentTemplate.advancedSettings')}
            </Typography>
            
            <Grid container spacing={3}>
              {/* Max Tokens */}
              <Grid item xs={12}>
                <Typography gutterBottom>
                  {t('admin:agentTemplate.maxTokens')}: {configData.maxTokens}
                </Typography>
                <Slider
                  value={configData.maxTokens}
                  onChange={(_, value) => handleChange('maxTokens', value)}
                  min={100}
                  max={4096}
                  step={100}
                  marks={[
                    { value: 100, label: '100' },
                    { value: 1000, label: '1000' },
                    { value: 4096, label: '4096' }
                  ]}
                />
              </Grid>

              {/* Temperature */}
              <Grid item xs={12}>
                <Typography gutterBottom>
                  {t('admin:agentTemplate.temperature')}: {configData.temperature}
                </Typography>
                <Slider
                  value={configData.temperature}
                  onChange={(_, value) => handleChange('temperature', value)}
                  min={0}
                  max={2}
                  step={0.1}
                  marks={[
                    { value: 0, label: '0' },
                    { value: 1, label: '1' },
                    { value: 2, label: '2' }
                  ]}
                />
              </Grid>

              {/* Top P */}
              <Grid item xs={12}>
                <Typography gutterBottom>
                  {t('admin:agentTemplate.topP')}: {configData.topP}
                </Typography>
                <Slider
                  value={configData.topP}
                  onChange={(_, value) => handleChange('topP', value)}
                  min={0}
                  max={1}
                  step={0.1}
                  marks={[
                    { value: 0, label: '0' },
                    { value: 0.5, label: '0.5' },
                    { value: 1, label: '1' }
                  ]}
                />
              </Grid>

              {/* Top K */}
              <Grid item xs={12}>
                <Typography gutterBottom>
                  {t('admin:agentTemplate.topK')}: {configData.topK}
                </Typography>
                <Slider
                  value={configData.topK}
                  onChange={(_, value) => handleChange('topK', value)}
                  min={1}
                  max={50}
                  step={1}
                  marks={[
                    { value: 1, label: '1' },
                    { value: 20, label: '20' },
                    { value: 50, label: '50' }
                  ]}
                />
              </Grid>
            </Grid>
          </CardContent>
        </Card>
      )}
    </Box>
  );
};

export default ModelConfig;
