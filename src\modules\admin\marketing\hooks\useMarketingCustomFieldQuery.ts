/**
 * Hooks cho Marketing Custom Fields - hoàn toàn độc lập
 */

import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { useTranslation } from 'react-i18next';
import { AxiosError } from 'axios';
import { NotificationUtil } from '@/shared/utils/notification';
import useSmartNotification from '@/shared/hooks/common/useSmartNotification';
import {
  MarketingCustomFieldService,
  MarketingCustomFieldBusinessService,
  MarketingCustomFieldQueryParams,
} from '../services/marketing-custom-field.service';
import {
  CreateMarketingCustomFieldRequest,
  UpdateMarketingCustomFieldRequest,
  MarketingCustomFieldResponse,
} from '../types/custom-field.types';

/**
 * Query keys cho marketing custom fields
 */
export const MARKETING_CUSTOM_FIELD_QUERY_KEYS = {
  all: ['marketing-custom-fields'] as const,
  lists: () => [...MARKETING_CUSTOM_FIELD_QUERY_KEYS.all, 'list'] as const,
  list: (params: MarketingCustomFieldQueryParams) => 
    [...MARKETING_CUSTOM_FIELD_QUERY_KEYS.lists(), params] as const,
  details: () => [...MARKETING_CUSTOM_FIELD_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string | number) => [...MARKETING_CUSTOM_FIELD_QUERY_KEYS.details(), id] as const,
  configExamples: () => [...MARKETING_CUSTOM_FIELD_QUERY_KEYS.all, 'config-examples'] as const,
};

/**
 * Hook lấy danh sách marketing custom fields
*/
export const useMarketingCustomFields = (params: MarketingCustomFieldQueryParams = {}) => {
  return useQuery({
    queryKey: MARKETING_CUSTOM_FIELD_QUERY_KEYS.list(params),
    queryFn: () => MarketingCustomFieldBusinessService.getCustomFieldsWithBusinessLogic(params),
    select: (data) => {
      // API trả về { code, message, result: { data, meta } }
      // Transform để khớp với component expect { items, meta }
      const result = data.result as unknown as { data: MarketingCustomFieldResponse[]; meta: Record<string, unknown> };
      if (result && 'data' in result) {
        return {
          items: result.data.map((item: MarketingCustomFieldResponse, index: number) => ({
            ...item,
            id: index + 1, // Tạo ID tạm thời vì API không trả về id
          })),
          meta: {
            currentPage: result.meta?.['page'] as number || 1,
            itemsPerPage: result.meta?.['limit'] as number || 10,
            totalItems: result.meta?.['total'] as number || 0,
            totalPages: result.meta?.['totalPages'] as number || 1,
            itemCount: result.data?.length || 0,
            hasItems: (result.data?.length || 0) > 0,
          }
        };
      }
      return data.result || data;
    },
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
  });
};

/**
 * Hook lấy chi tiết marketing custom field
 */
export const useMarketingCustomField = (id: number) => {
  return useQuery({
    queryKey: MARKETING_CUSTOM_FIELD_QUERY_KEYS.detail(id),
    queryFn: () => MarketingCustomFieldService.getCustomFieldById(id),
    select: (data) => data.result,
    enabled: !!id,
    refetchOnWindowFocus: false,
    staleTime: 60000, // 60 seconds
  });
};

/**
 * Hook tạo marketing custom field mới
 */
export const useCreateMarketingCustomField = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['marketing', 'common']);

  return useMutation({
    mutationFn: (data: CreateMarketingCustomFieldRequest) =>
      MarketingCustomFieldBusinessService.createCustomFieldWithValidation(data),
    onSuccess: (data) => {
      NotificationUtil.success({
        message: data.message || t('marketing:customField.createSuccess'),
        duration: 3000,
      });

      // Invalidate và refetch danh sách
      queryClient.invalidateQueries({
        queryKey: MARKETING_CUSTOM_FIELD_QUERY_KEYS.lists(),
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string; errors?: string[] }>) => {
      console.error('Error creating marketing custom field:', error);
      
      let errorMessage = t('marketing:customField.errors.createError');
      
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      
      // Hiển thị chi tiết lỗi validation nếu có
      if (error.response?.data?.errors && Array.isArray(error.response.data.errors)) {
        errorMessage += '\n' + error.response.data.errors.join('\n');
      }

      NotificationUtil.error({
        message: errorMessage,
        duration: 5000,
      });
    },
  });
};

/**
 * Hook cập nhật marketing custom field
 */
export const useUpdateMarketingCustomField = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['marketing', 'common']);

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateMarketingCustomFieldRequest }) =>
      MarketingCustomFieldBusinessService.updateCustomFieldWithValidation(id, data),
    onSuccess: (data, variables) => {
      NotificationUtil.success({
        message: data.message || t('marketing:customField.updateSuccess'),
        duration: 3000,
      });

      // Invalidate và refetch danh sách và chi tiết
      queryClient.invalidateQueries({
        queryKey: MARKETING_CUSTOM_FIELD_QUERY_KEYS.lists(),
        exact: false,
      });
      
      queryClient.invalidateQueries({
        queryKey: MARKETING_CUSTOM_FIELD_QUERY_KEYS.detail(variables.id),
        exact: true,
      });
    },
    onError: (error: AxiosError<{ message: string; errors?: string[] }>) => {
      console.error('Error updating marketing custom field:', error);
      
      let errorMessage = t('marketing:customField.errors.updateError');
      
      if (error.response?.data?.message) {
        errorMessage = error.response.data.message;
      }
      
      // Hiển thị chi tiết lỗi validation nếu có
      if (error.response?.data?.errors && Array.isArray(error.response.data.errors)) {
        errorMessage += '\n' + error.response.data.errors.join('\n');
      }

      NotificationUtil.error({
        message: errorMessage,
        duration: 5000,
      });
    },
  });
};

/**
 * Hook xóa marketing custom field
 */
export const useDeleteMarketingCustomField = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['marketing', 'common']);

  return useMutation({
    mutationFn: (id: number) => MarketingCustomFieldService.deleteCustomField(id),
    onSuccess: (data) => {
      NotificationUtil.success({
        message: data.message || t('marketing:customField.deleteSuccess'),
        duration: 3000,
      });

      // Invalidate và refetch danh sách
      queryClient.invalidateQueries({
        queryKey: MARKETING_CUSTOM_FIELD_QUERY_KEYS.lists(),
        exact: false,
      });
    },
    onError: (error: AxiosError<{ message: string }>) => {
      console.error('Error deleting marketing custom field:', error);
      NotificationUtil.error({
        message: error.response?.data?.message || t('marketing:customField.errors.deleteError'),
        duration: 3000,
      });
    },
  });
};

/**
 * Hook xóa nhiều marketing custom fields - API mới với response chi tiết và smart notification
 */
export const useDeleteMultipleMarketingCustomFields = () => {
  const queryClient = useQueryClient();
  const { t } = useTranslation(['marketing', 'common']);
  const smartNotification = useSmartNotification();

  return useMutation({
    mutationFn: (ids: number[]) => MarketingCustomFieldService.deleteMultipleCustomFields(ids),
    onSuccess: (response) => {
      // Invalidate và refetch danh sách
      queryClient.invalidateQueries({
        queryKey: MARKETING_CUSTOM_FIELD_QUERY_KEYS.lists(),
        exact: false,
      });

      // Lấy message từ API response - ưu tiên message ở root level
      const apiMessage = response.message || response.result?.message;
      // Sử dụng response data để phân tích kết quả
      const result = response.result; // BulkDeleteMarketingCustomFieldsResponse
      if (result && result.data) {
        const data = result.data; // BulkDeleteMarketingCustomFieldsResult

        // Phân tích kết quả và hiển thị thông báo phù hợp với message từ API
        if (data.deletedCount > 0 && data.failedCount === 0) {
          // Tất cả đều xóa thành công
          smartNotification.success({
            title: t('marketing:customField.deleteSuccess', 'Xóa thành công'),
            message: apiMessage,
            duration: 3000,
          });
        } else if (data.deletedCount > 0 && data.failedCount > 0) {
          // Xóa một phần thành công
          smartNotification.warning({
            title: t('marketing:customField.deletePartialSuccess', 'Xóa một phần thành công'),
            message: apiMessage || t(
              'marketing:customField.deleteMultiplePartialSuccess',
              `Đã xóa ${data.deletedCount} trường, không thể xóa ${data.failedCount} trường (IDs: ${data.failedIds.join(', ')})`
            ),
            duration: 5000,
          });
        } else if (data.deletedCount === 0 && data.failedCount > 0) {
          // Không xóa được trường nào - sử dụng message từ API
          smartNotification.error({
            title: t('marketing:customField.deleteFailed', 'Xóa thất bại'),
            message: apiMessage || t(
              'marketing:customField.deleteAllFailed',
              `Không thể xóa ${data.failedCount} trường tùy chỉnh (IDs: ${data.failedIds.join(', ')})`
            ),
            duration: 5000,
          });
        }
      } else {
        // Fallback message nếu không có result
        smartNotification.success({
          title: t('marketing:customField.deleteSuccess', 'Xóa thành công'),
          message: t('marketing:customField.deleteMultipleSuccess', 'Đã xóa trường tùy chỉnh thành công'),
          duration: 3000,
        });
      }
    },
    onError: (error: AxiosError<{ message: string }>) => {
      console.error('❌ Error deleting multiple marketing custom fields:', error);
      smartNotification.error({
        title: t('marketing:customField.deleteError', 'Lỗi xóa'),
        message: error.response?.data?.message || t('marketing:customField.errors.deleteMultipleError', 'Có lỗi xảy ra khi xóa trường tùy chỉnh'),
        duration: 3000,
      });
    },
  });
};

