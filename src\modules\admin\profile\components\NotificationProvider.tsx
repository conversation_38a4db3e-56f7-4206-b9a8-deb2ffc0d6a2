import React, { ReactNode } from 'react';
import useChatNotification from '@/shared/hooks/common/useChatNotification';
import { ProfileNotificationContext } from '../contexts/ProfileNotificationContext';

interface NotificationProviderProps {
  children: ReactNode;
}

/**
 * Provider để quản lý thông báo trong trang profile
 */
const NotificationProvider: React.FC<NotificationProviderProps> = ({ children }) => {
  const { showNotification } = useChatNotification();

  return (
    <ProfileNotificationContext.Provider value={{ showNotification }}>
      {children}
    </ProfileNotificationContext.Provider>
  );
};

export default NotificationProvider;
