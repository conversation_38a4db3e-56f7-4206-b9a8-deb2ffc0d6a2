import React from 'react';
import { useTranslation } from 'react-i18next';
import { Card, ResponsiveGrid } from '@/shared/components/common';
import TrashToolCard from './TrashToolCard';
import { ToolListItem } from '../types/tool.types';

interface TrashToolGridProps {
  /**
   * Danh sách tools đã xóa
   */
  tools: ToolListItem[];

  /**
   * Trạng thái loading
   */
  loading?: boolean;

  /**
   * Callback khi khôi phục tool
   */
  onRestore?: (tool: ToolListItem) => void;

  /**
   * CSS class tùy chỉnh
   */
  className?: string;
}

/**
 * Component hiển thị grid các tool đã xóa mềm
 */
const TrashToolGrid: React.FC<TrashToolGridProps> = ({
  tools,
  loading = false,
  onRestore,
  className = '',
}) => {
  const { t } = useTranslation(['admin-tool', 'common']);

  // Hiển thị loading state
  if (loading) {
    return (
      <div className={`flex justify-center items-center py-12 ${className}`}>
        <div className="text-gray-500">{t('common:loading', 'Đang tải...')}</div>
      </div>
    );
  }

  // Hiển thị empty state khi không có dữ liệu
  if (!tools || tools.length === 0) {
    return (
      <Card className={`p-12 text-center ${className}`}>
        <div className="text-gray-500">
          {t('admin-tool:trash.noData', 'Không có tool nào đã xóa')}
        </div>
      </Card>
    );
  }

  // Hiển thị grid tools
  return (
    <div className={className}>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 4 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={{ xs: 4, md: 5, lg: 6 }}
      >
        {tools.map(tool => (
          <div key={tool.id} className="h-full">
            <TrashToolCard tool={tool} onRestore={onRestore!} />
          </div>
        ))}
      </ResponsiveGrid>
    </div>
  );
};

export default TrashToolGrid;
