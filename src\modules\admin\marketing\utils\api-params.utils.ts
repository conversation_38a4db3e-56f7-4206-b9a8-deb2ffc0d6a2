/**
 * Utility functions for API parameter validation and transformation
 */

/**
 * Valid sortBy fields for marketing custom fields API
 */
export const MARKETING_CUSTOM_FIELD_SORT_FIELDS = {
  FIELD_KEY: 'fieldKey',
  DISPLAY_NAME: 'displayName', 
  DATA_TYPE: 'dataType'
} as const;

export type MarketingCustomFieldSortField = typeof MARKETING_CUSTOM_FIELD_SORT_FIELDS[keyof typeof MARKETING_CUSTOM_FIELD_SORT_FIELDS];

/**
 * Validates and normalizes sortBy parameter for marketing custom fields API
 * @param sortBy - The sortBy parameter to validate
 * @returns Valid sortBy field or default
 */
export const validateMarketingCustomFieldSortBy = (sortBy?: string): MarketingCustomFieldSortField => {
  const validFields = Object.values(MARKETING_CUSTOM_FIELD_SORT_FIELDS);
  
  if (sortBy && validFields.includes(sortBy as MarketingCustomFieldSortField)) {
    return sortBy as MarketingCustomFieldSortField;
  }
  
  // Default to displayName if invalid or not provided
  return MARKETING_CUSTOM_FIELD_SORT_FIELDS.DISPLAY_NAME;
};

/**
 * Creates safe API parameters for marketing custom field queries
 * @param params - Input parameters
 * @returns Validated and safe API parameters
 */
export const createSafeMarketingCustomFieldParams = (params: {
  search?: string | undefined;
  page?: number | undefined;
  limit?: number | undefined;
  sortBy?: string | undefined;
  sortDirection?: 'ASC' | 'DESC' | undefined;
}) => {
  const result: {
    page: number;
    limit: number;
    sortBy: MarketingCustomFieldSortField;
    sortDirection: 'ASC' | 'DESC';
    search?: string;
  } = {
    page: params.page || 1,
    limit: Math.min(params.limit || 20, 100), // Ensure limit doesn't exceed 100
    sortBy: validateMarketingCustomFieldSortBy(params.sortBy),
    sortDirection: params.sortDirection || 'ASC' as const,
  };

  // Only add search if it's defined and not empty
  if (params.search && params.search.trim()) {
    result.search = params.search.trim();
  }

  return result;
};

/**
 * Error messages for API parameter validation
 */
export const API_PARAM_ERROR_MESSAGES = {
  INVALID_SORT_BY: (field: string, validFields: string[]) => 
    `sortBy field '${field}' is invalid. Valid fields are: ${validFields.join(', ')}`,
  LIMIT_EXCEEDED: (limit: number, max: number) => 
    `Limit ${limit} exceeds maximum allowed value of ${max}`,
  INVALID_PAGE: (page: number) => 
    `Page number ${page} must be greater than 0`,
} as const;

/**
 * Validates API query parameters
 * @param params - Parameters to validate
 * @returns Validation result
 */
export const validateApiParams = (params: {
  page?: number;
  limit?: number;
  sortBy?: string;
  validSortFields?: string[];
}) => {
  const errors: string[] = [];
  
  if (params.page !== undefined && params.page < 1) {
    errors.push(API_PARAM_ERROR_MESSAGES.INVALID_PAGE(params.page));
  }
  
  if (params.limit !== undefined && params.limit > 100) {
    errors.push(API_PARAM_ERROR_MESSAGES.LIMIT_EXCEEDED(params.limit, 100));
  }
  
  if (params.sortBy && params.validSortFields && !params.validSortFields.includes(params.sortBy)) {
    errors.push(API_PARAM_ERROR_MESSAGES.INVALID_SORT_BY(params.sortBy, params.validSortFields));
  }
  
  return {
    isValid: errors.length === 0,
    errors
  };
};

/**
 * Common API parameter defaults
 */
export const API_DEFAULTS = {
  PAGE: 1,
  LIMIT: 20,
  MAX_LIMIT: 100,
  SORT_DIRECTION: 'ASC' as const,
  MARKETING_CUSTOM_FIELD_SORT_BY: MARKETING_CUSTOM_FIELD_SORT_FIELDS.DISPLAY_NAME,
} as const;
