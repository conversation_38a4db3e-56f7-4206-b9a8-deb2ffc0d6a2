import React, { useState } from 'react';
import { Card, Form, FormItem, Input, IconCard, Typography, PhoneInputWithCountry } from '@/shared/components/common';
import AdminTagsSelectWithPagination from '../common/AdminTagsSelectWithPagination';
import { z } from 'zod';

// Schema cho form - chỉ validate các field trong Form component
const formSchema = z.object({
  name: z.string().min(1, 'Tên đối tượng là bắt buộc'),
  email: z
    .string()
    .optional()
    .refine((val) => !val || /^[^\s@]+@[^\s@]+\.[^\s@]+$/.test(val), {
      message: 'Email không hợp lệ'
    }),
});

export type AudienceFormValues = z.infer<typeof formSchema>;

interface AudienceFormProps {
  onSubmit: (values: Record<string, unknown>) => void;
  onCancel: () => void;
}

/**
 * Component form thêm/sửa đối tượng
 */
const AudienceForm: React.FC<AudienceFormProps> = ({ onSubmit, onCancel }) => {
  const [selectedTags, setSelectedTags] = useState<number[]>([]);
  const [phoneValue, setPhoneValue] = useState<string>('');

  const handlePhoneChange = (internationalPhone: string) => {
    setPhoneValue(internationalPhone);
    // PhoneInputWithCountry component returns the full international number (e.g., "+84987654321")
    // We store the complete phone number including country code
  };

  const handleSubmit = (values: Record<string, unknown>) => {
    // Thêm selectedTags và phone vào values (không gửi countryCode vì API không chấp nhận)
    const formData = {
      ...values,
      ...(phoneValue && phoneValue.trim() && { phone: phoneValue }),
      tagIds: selectedTags,
    };

    onSubmit(formData);
  };

  return (
    <Card className="mb-4 p-4">
      <Typography variant="h5" className="mb-4">
        Thêm đối tượng mới
      </Typography>

      <Form schema={formSchema} onSubmit={handleSubmit} submitOnEnter={false} className="space-y-4">
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem name="name" label="Tên đối tượng" required>
            <Input placeholder="Nhập tên đối tượng" fullWidth />
          </FormItem>

          <FormItem name="email" label="Email">
            <Input type="email" placeholder="Nhập email" fullWidth />
          </FormItem>
        </div>

        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <div>
            <label className="block text-sm font-medium mb-1">
              Số điện thoại
            </label>
            <PhoneInputWithCountry
              value={phoneValue}
              onChange={handlePhoneChange}
              placeholder="Nhập số điện thoại"
              fullWidth
            />
          </div>

          <div>
            <label className="block text-sm font-medium mb-1">Tags</label>
            <AdminTagsSelectWithPagination
              value={selectedTags}
              onChange={setSelectedTags}
              placeholder="Chọn tags..."
              fullWidth
            />
          </div>
        </div>

        <div className="flex justify-end space-x-3">
          <IconCard
            icon="x"
            variant="default"
            size="md"
            title="Hủy"
            onClick={onCancel}
          />
          <IconCard
            icon="save"
            variant="default"
            size="md"
            title="Lưu"
            onClick={() => {
              const form = document.querySelector('form');
              if (form) {
                form.requestSubmit();
              }
            }}
          />
        </div>
      </Form>
    </Card>
  );
};

export default AudienceForm;
