import { createContext } from 'react';
import { ProfileCardId } from '../constants/profile-cards';

interface ProfileAccordionContextType {
  openCard: ProfileCardId | null;
  setOpenCard: (cardId: ProfileCardId | null) => void;
  toggleCard: (cardId: ProfileCardId) => void;
  isCardOpen: (cardId: ProfileCardId) => boolean;
}

export const ProfileAccordionContext = createContext<ProfileAccordionContextType | undefined>(undefined);
