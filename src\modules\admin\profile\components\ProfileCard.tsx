import React, { ReactNode } from 'react';
import { CollapsibleCard } from '@/shared/components/common';
import { useProfileAccordion } from '../hooks/useProfileAccordion';
import { ProfileCardId } from '../constants/profile-cards';

interface ProfileCardProps {
  /**
   * ID của card (dùng để quản lý accordion)
   */
  cardId: ProfileCardId;

  /**
   * Tiêu đề của card
   */
  title: ReactNode;

  /**
   * Nội dung của card
   */
  children: ReactNode;

  /**
   * Class bổ sung cho card
   */
  className?: string;

  /**
   * Có lazy load content không
   */
  lazyLoad?: boolean;
}

/**
 * Component wrapper cho các card trong profile page
 * Tự động tích hợp với ProfileAccordionContext
 */
const ProfileCard: React.FC<ProfileCardProps> = ({
  cardId,
  title,
  children,
  className = "mb-6",
  lazyLoad = true,
}) => {
  const { isCardOpen, toggleCard } = useProfileAccordion();

  const isOpen = isCardOpen(cardId);

  const handleToggle = (newIsOpen: boolean) => {
    if (newIsOpen) {
      toggleCard(cardId);
    } else {
      toggleCard(cardId); // Sẽ đóng card hiện tại
    }
  };

  return (
    <CollapsibleCard
      title={title}
      className={className}
      isOpen={isOpen}
      onToggle={handleToggle}
      lazyLoad={lazyLoad}
    >
      {children}
    </CollapsibleCard>
  );
};

export default ProfileCard;
