/**
 * Hooks cho quản lý người dùng
 */
import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getUsers,
  getUserById,
  createUser,
  updateUser,
  updateUserPassword,
  deleteUser,
  blockUser,
  unblockUser,
} from '../services/user.service';
import {
  UserQueryDto,
  CreateUserDto,
  UpdateUserDto,
  UpdateUserPasswordDto,
  BlockUserRequest,
  UnblockUserRequest,
} from '../types/user.types';

// Keys cho React Query
export const USER_QUERY_KEYS = {
  users: ['admin', 'users'],
  user: (id: number) => ['admin', 'users', id],
};

/**
 * Hook để lấy danh sách người dùng
 */
export const useUsers = (params: UserQueryDto) => {
  return useQuery({
    queryKey: [...USER_QUERY_KEYS.users, params],
    queryFn: () => getUsers(params),
    select: data => data.result,
  });
};

/**
 * Hook để lấy thông tin chi tiết người dùng
 */
export const useUser = (id: number) => {
  return useQuery({
    queryKey: USER_QUERY_KEYS.user(id),
    queryFn: () => getUserById(id),
    select: data => data.result,
    enabled: !!id,
  });
};

/**
 * Hook để tạo người dùng mới
 */
export const useCreateUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateUserDto) => createUser(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.users });
    },
  });
};

/**
 * Hook để cập nhật thông tin người dùng
 */
export const useUpdateUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateUserDto }) => updateUser(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.users });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.user(variables.id) });
    },
  });
};

/**
 * Hook để cập nhật mật khẩu người dùng
 */
export const useUpdateUserPassword = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: number; data: UpdateUserPasswordDto }) =>
      updateUserPassword(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.user(variables.id) });
    },
  });
};

/**
 * Hook để xóa người dùng
 */
export const useDeleteUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: number) => deleteUser(id),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.users });
    },
  });
};

/**
 * Hook để block người dùng
 */
export const useBlockUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: BlockUserRequest) => blockUser(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.users });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.user(variables.id) });
    },
  });
};

/**
 * Hook để unblock người dùng
 */
export const useUnblockUser = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: UnblockUserRequest) => unblockUser(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.users });
      queryClient.invalidateQueries({ queryKey: USER_QUERY_KEYS.user(variables.id) });
    },
  });
};
