import { z } from 'zod';
import { TFunction } from 'i18next';

/**
 * Schema creator function for security info form with translation support
 */
export const createSecurityInfoSchema = (t: TFunction) =>
  z
    .object({
      otpSmsEnabled: z.boolean(),
      otpEmailEnabled: z.boolean(),
      googleAuthenticatorEnabled: z.boolean(),
      currentPassword: z.string().optional(),
      newPassword: z.string().min(8, t('validation:password.min')).optional(),
      confirmPassword: z.string().optional(),
    })
    .refine(
      data => {
        if (data.newPassword && !data.currentPassword) {
          return false;
        }
        return true;
      },
      {
        message: t('validation:currentPassword.required'),
        path: ['currentPassword'],
      }
    )
    .refine(
      data => {
        if (data.newPassword && data.confirmPassword && data.newPassword !== data.confirmPassword) {
          return false;
        }
        return true;
      },
      {
        message: t('validation:confirmPassword.match'),
        path: ['confirmPassword'],
      }
    );

/**
 * Schema creator function for password change form with translation support
 */
export const createPasswordChangeSchema = (t: TFunction) =>
  z
    .object({
      currentPassword: z.string().optional(),
      newPassword: z.string().min(8, t('validation:password.min')).optional(),
      confirmPassword: z.string().optional(),
    })
    .refine(
      data => {
        if (data.newPassword && !data.currentPassword) {
          return false;
        }
        return true;
      },
      {
        message: t('validation:currentPassword.required'),
        path: ['currentPassword'],
      }
    )
    .refine(
      data => {
        if (data.newPassword && data.confirmPassword && data.newPassword !== data.confirmPassword) {
          return false;
        }
        return true;
      },
      {
        message: t('validation:confirmPassword.match'),
        path: ['confirmPassword'],
      }
    );

/**
 * Type for security info schema
 */
export type SecurityInfoSchema = z.infer<ReturnType<typeof createSecurityInfoSchema>>;

/**
 * Type for password change schema
 */
export type PasswordChangeSchema = z.infer<ReturnType<typeof createPasswordChangeSchema>>;
