import React from 'react';
import ProfileHeader from '../components/ProfileHeader';
import PersonalInfoForm from '../components/PersonalInfoForm';
import SecurityForm from '../components/SecurityForm';

import NotificationProvider from '../components/NotificationProvider';

import { ProfileAccordionProvider } from '../contexts/ProfileAccordionProvider.tsx';

/**
 * Trang profile admin
 */
const ProfilePage: React.FC = () => {
  return (
    <NotificationProvider>
      <ProfileAccordionProvider>
        <div>
          {/* Header với banner và avatar */}
          <ProfileHeader />

          {/* Nội dung chính */}
          <div className="py-8">
            {/* Thông tin cá nhân */}
            <PersonalInfoForm />

            {/* Bảo mật */}
            <SecurityForm />
          </div>
        </div>
      </ProfileAccordionProvider>
    </NotificationProvider>
  );
};

export default ProfilePage;
