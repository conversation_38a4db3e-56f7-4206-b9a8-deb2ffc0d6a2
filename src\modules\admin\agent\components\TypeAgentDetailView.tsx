import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Grid,
  Badge,
  Loading,
  Alert
} from '@/shared/components/common';
import { apiClient } from '@/shared/api/axios';

interface TypeAgent {
  id: number;
  name: string;
  description: string;
  createdAt: string;
  status: string;
}

interface TypeAgentDetailViewProps {
  onSelectType?: (typeAgent: TypeAgent) => void;
  selectedTypeId?: number;
}

const TypeAgentDetailView: React.FC<TypeAgentDetailViewProps> = ({
  onSelectType,
  selectedTypeId
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const [typeAgents, setTypeAgents] = useState<TypeAgent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTypeAgents = async () => {
      try {
        setLoading(true);
        const response = await apiClient.get('/admin/type-agents', {
          params: {
            page: 1,
            limit: 100
          },
          tokenType: 'admin'
        });

        const result = response.result as any;
        if (result?.items) {
          setTypeAgents(result.items);
        } else if (Array.isArray(result)) {
          setTypeAgents(result);
        }
      } catch (err) {
        console.error('Error fetching type agents:', err);
        setError(t('admin:agentTemplate.noTypeAgents'));
      } finally {
        setLoading(false);
      }
    };

    fetchTypeAgents();
  }, [t]);

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-48">
        <Loading />
      </div>
    );
  }

  if (error) {
    return (
      <Alert type="error" message={error} className="mb-4" />
    );
  }

  return (
    <div>
      <Typography variant="h6" className="mb-2">
        {t('admin:agentTemplate.selectType')}
      </Typography>
      <Typography variant="body2" className="text-gray-600 mb-6">
        {t('admin:agentTemplate.selectTypeDescription')}
      </Typography>

      <Grid columns={{ xs: 1, sm: 2, md: 3 }} columnGap="lg" rowGap="lg">
        {typeAgents.map((typeAgent) => (
          <Card
            key={typeAgent.id}
            className={`cursor-pointer transition-all duration-200 hover:shadow-lg ${
              selectedTypeId === typeAgent.id
                ? 'border-2 border-blue-500 shadow-md'
                : 'border border-gray-200 hover:border-blue-300'
            }`}
            onClick={() => onSelectType?.(typeAgent)}
          >
            <div className="p-4">
              <div className="flex justify-between items-start mb-2">
                <Typography variant="h6" className="font-semibold truncate">
                  {typeAgent.name}
                </Typography>
                <Badge
                  variant={typeAgent.status === 'APPROVED' ? 'success' : 'secondary'}
                  size="sm"
                >
                  {typeAgent.status}
                </Badge>
              </div>

              <Typography variant="body2" className="text-gray-600 mb-4">
                {typeAgent.description}
              </Typography>

              <Typography variant="caption" className="text-gray-500">
                {t('admin:agentTemplate.createdAt')}: {new Date(parseInt(typeAgent.createdAt)).toLocaleDateString('vi-VN')}
              </Typography>
            </div>
          </Card>
        ))}
      </Grid>

      {typeAgents.length === 0 && (
        <Alert type="info" message={t('admin:agentTemplate.noTypeAgents')} className="mt-4" />
      )}
    </div>
  );
};

export default TypeAgentDetailView;
