/**
 * Types cho Agent Strategy module
 */

/**
 * Enum cho trạng thái agent strategy
 */
export enum AgentStrategyStatusEnum {
  ACTIVE = 'active',
  INACTIVE = 'inactive',
}

/**
 * Enum cho sắp xếp agent strategy
 */
export enum AgentStrategySortBy {
  NAME = 'name',
  CREATED_AT = 'createdAt',
  UPDATED_AT = 'updatedAt',
}

/**
 * Enum cho hướng sắp xếp
 */
export enum SortDirection {
  ASC = 'asc',
  DESC = 'desc',
}

/**
 * Interface cho cấu hình model AI
 */
export interface ModelConfig {
  /** Độ ngẫu nhiên của model (0-2) */
  temperature: number;
  /** Số token tối đa */
  max_tokens: number;
  /** Top P sampling */
  top_p: number;
  /** Top K sampling */
  top_k: number;
}

/**
 * Interface cho nội dung các bước
 */
export interface StrategyContent {
  /** Thứ tự bước */
  stepOrder: number;
  /** Nội dung bước */
  content: string;
}

/**
 * Interface cho ví dụ mặc định
 */
export interface StrategyExample {
  /** Thứ tự bước */
  stepOrder: number;
  /** Nội dung ví dụ */
  content: string;
}

/**
 * Interface cho agent strategy trong danh sách
 */
export interface AgentStrategyListItem {
  /** ID của strategy */
  id: string;
  /** Tên hiển thị của strategy */
  name: string;
  /** URL avatar của strategy */
  avatar: string | null;
  /** System Model ID */
  systemModelId: string;
  /** Model ID */
  modelId: string;
}

/**
 * Interface cho thông tin chi tiết agent strategy
 */
export interface AgentStrategyDetail {
  /** ID của strategy */
  id: string;
  /** Tên hiển thị của strategy */
  name: string;
  /** URL avatar của strategy */
  avatar: string | null;
  /** Cấu hình model AI */
  modelConfig?: ModelConfig;
  /** Hướng dẫn hoặc system prompt cho strategy */
  instruction: string;
  /** ID của vector store */
  vectorStoreId: string | null;
  /** Nội dung các bước */
  content: StrategyContent[];
  /** Ví dụ mặc định */
  exampleDefault: StrategyExample[];
  /** System Model ID */
  modelSystemId: string;
  /** Model ID */
  modelId?: string;
}

/**
 * Interface cho tham số tạo agent strategy
 */
export interface CreateAgentStrategyParams {
  /** Tên hiển thị của strategy */
  name: string;
  /** MIME type của avatar */
  avatarMimeType?: string;
  /** Cấu hình model AI */
  modelConfig: ModelConfig;
  /** Hướng dẫn hoặc system prompt cho strategy */
  instruction: string;
  /** ID của vector store */
  vectorStoreId?: string | null;
  /** Nội dung các bước */
  content: StrategyContent[];
  /** Ví dụ mặc định */
  exampleDefault: StrategyExample[];
  /** System Model ID */
  systemModelId: string;
}

/**
 * Interface cho tham số cập nhật agent strategy
 */
export interface UpdateAgentStrategyParams {
  /** Tên hiển thị của strategy */
  name: string;
  /** MIME type của avatar */
  avatarMimeType?: string;
  /** Hướng dẫn hoặc system prompt cho strategy */
  instruction: string;
  /** ID của vector store */
  vectorStoreId?: string | null;
  /** Cấu hình model AI */
  modelConfig: ModelConfig;
  /** Nội dung các bước */
  content: StrategyContent[];
  /** Ví dụ mặc định */
  exampleDefault: StrategyExample[];
  /** System Model ID */
  systemModelId: string;
}

/**
 * Interface cho tham số query agent strategy
 */
export interface AgentStrategyQueryParams {
  /** Từ khóa tìm kiếm */
  search?: string;
  /** Số trang */
  page?: number;
  /** Số item mỗi trang */
  limit?: number;
  /** Trường sắp xếp */
  sortBy?: AgentStrategySortBy;
  /** Hướng sắp xếp */
  sortDirection?: SortDirection;
}

/**
 * Interface cho response danh sách agent strategy
 */
export interface AgentStrategyListResponse {
  /** Danh sách strategy */
  items: AgentStrategyListItem[];
  /** Metadata phân trang */
  meta: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
    hasItems: boolean;
  };
}

/**
 * Interface cho response tạo agent strategy
 */
export interface CreateAgentStrategyResponse {
  /** ID của strategy được tạo */
  id: string;
  /** URL upload avatar (nếu có) */
  avatarUploadUrl?: string;
}

/**
 * Interface cho response cập nhật agent strategy
 */
export interface UpdateAgentStrategyResponse {
  /** URL upload avatar (nếu có) */
  avatarUrlUpload?: string;
}
