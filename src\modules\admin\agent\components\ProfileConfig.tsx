import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typo<PERSON>,
  <PERSON>,
  Button
} from '@/shared/components/common';

interface ProfileData {
  birthDate: string;
  gender: string;
  language: string;
  education: string;
  country: string;
  position: string;
  skills: string[];
  personality: string;
}

interface ProfileConfigProps {
  initialData?: ProfileData;
  onSave?: (data: ProfileData) => void;
}

const ProfileConfig: React.FC<ProfileConfigProps> = ({
  initialData,
  onSave
}) => {
  const { t } = useTranslation(['admin', 'common']);

  const [profileData, setProfileData] = useState<ProfileData>(initialData || {
    birthDate: '2000-08-01',
    gender: t('admin:agentTemplate.female'),
    language: 'English',
    education: t('admin:agentTemplate.college'),
    country: 'United States',
    position: t('admin:agentTemplate.positionPlaceholder'),
    skills: [t('admin:agentTemplate.skills'), t('admin:agentTemplate.skills')],
    personality: t('admin:agentTemplate.personalityPlaceholder')
  });

  const [skillInput, setSkillInput] = useState('');

  const handleChange = (field: keyof ProfileData, value: any) => {
    const newData = { ...profileData, [field]: value };
    setProfileData(newData);
    if (onSave) onSave(newData);
  };

  const handleAddSkill = () => {
    if (skillInput.trim() && !profileData.skills.includes(skillInput.trim())) {
      const newSkills = [...profileData.skills, skillInput.trim()];
      handleChange('skills', newSkills);
      setSkillInput('');
    }
  };

  const handleRemoveSkill = (skillToRemove: string) => {
    const newSkills = profileData.skills.filter(skill => skill !== skillToRemove);
    handleChange('skills', newSkills);
  };

  const handleSkillInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddSkill();
    }
  };

  useEffect(() => {
    if (onSave) onSave(profileData);
  }, []);

  return (
    <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Birth Date */}
            <div>
              <label className="block text-sm font-medium mb-2">
                {t('admin:agentTemplate.birthDate')}
              </label>
              <input
                type="date"
                value={profileData.birthDate}
                onChange={(e) => handleChange('birthDate', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>

            {/* Gender */}
            <div>
              <label className="block text-sm font-medium mb-2">
                {t('admin:agentTemplate.gender')}
              </label>
              <select
                value={profileData.gender}
                onChange={(e) => handleChange('gender', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="Nam">{t('admin:agentTemplate.male')}</option>
                <option value="Nữ">{t('admin:agentTemplate.female')}</option>
                <option value="Khác">{t('admin:agentTemplate.other')}</option>
              </select>
            </div>

            {/* Language */}
            <div>
              <label className="block text-sm font-medium mb-2">
                {t('admin:agentTemplate.language')}
              </label>
              <select
                value={profileData.language}
                onChange={(e) => handleChange('language', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="Tiếng Việt">Tiếng Việt</option>
                <option value="English">English</option>
                <option value="中文">中文</option>
              </select>
            </div>

            {/* Education */}
            <div>
              <label className="block text-sm font-medium mb-2">
                {t('admin:agentTemplate.education')}
              </label>
              <select
                value={profileData.education}
                onChange={(e) => handleChange('education', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="Trung học">{t('admin:agentTemplate.highSchool')}</option>
                <option value="Cao đẳng">{t('admin:agentTemplate.college')}</option>
                <option value="Đại học">{t('admin:agentTemplate.university')}</option>
                <option value="Sau đại học">{t('admin:agentTemplate.postgraduate')}</option>
              </select>
            </div>

            {/* Country */}
            <div>
              <label className="block text-sm font-medium mb-2">
                {t('admin:agentTemplate.country')}
              </label>
              <select
                value={profileData.country}
                onChange={(e) => handleChange('country', e.target.value)}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              >
                <option value="Việt Nam">Việt Nam</option>
                <option value="United States">United States</option>
                <option value="China">China</option>
                <option value="Japan">Japan</option>
              </select>
            </div>

            {/* Position */}
            <div>
              <label className="block text-sm font-medium mb-2">
                {t('admin:agentTemplate.position')}
              </label>
              <input
                type="text"
                value={profileData.position}
                onChange={(e) => handleChange('position', e.target.value)}
                placeholder={t('admin:agentTemplate.positionPlaceholder')}
                className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
            </div>
          </div>

          {/* Skills */}
          <div className="mt-6">
            <Typography variant="subtitle2" className="mb-3">
              {t('admin:agentTemplate.skills')}
            </Typography>
            <div className="mb-4">
              {profileData.skills.map((skill, index) => (
                <Chip
                  key={index}
                  label={skill}
                  onRemove={() => handleRemoveSkill(skill)}
                  className="mr-2 mb-2"
                />
              ))}
            </div>
            <div className="flex gap-2">
              <input
                type="text"
                placeholder={t('admin:agentTemplate.skillPlaceholder')}
                value={skillInput}
                onChange={(e) => setSkillInput(e.target.value)}
                onKeyDown={handleSkillInputKeyDown}
                className="flex-1 px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              />
              <Button
                onClick={handleAddSkill}
                variant="outline"
                size="sm"
              >
                ➕
              </Button>
            </div>
          </div>

          {/* Personality */}
          <div className="mt-6">
            <label className="block text-sm font-medium mb-2">
              {t('admin:agentTemplate.personality')}
            </label>
            <textarea
              value={profileData.personality}
              onChange={(e) => handleChange('personality', e.target.value)}
              placeholder={t('admin:agentTemplate.personalityPlaceholder')}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            />
          </div>
        </div>
  );
};

export default ProfileConfig;
