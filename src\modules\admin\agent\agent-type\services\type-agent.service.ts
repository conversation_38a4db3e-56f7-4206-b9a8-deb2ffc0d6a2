import { apiClient } from '@/shared/api/axios';
import {
  TypeAgentDetail,
  CreateTypeAgentParams,
  UpdateTypeAgentParams,
  TypeAgentQueryParams,
  TypeAgentResponse,
  CreateTypeAgentResponse,
  AgentSystemListItem,
} from '../types/type-agent.types';

/**
 * Service để tương tác với API type agent của admin
 */
export class AdminTypeAgentService {
  private baseUrl = '/admin/type-agents';

  /**
   * Lấy danh sách type agents
   * @param params Tham số truy vấn
   * @returns Danh sách type agents
   */
  
  async getTypeAgents(params: TypeAgentQueryParams): Promise<TypeAgentResponse['result']> {
    try {
      const response = await apiClient.get<TypeAgentResponse>(this.baseUrl, {
        params,
        tokenType: 'admin'
      });
      return response.result;
    } catch (error) {
      console.error('Error fetching type agents:', error);
      throw error;
    }
  }

  /**
   * <PERSON><PERSON><PERSON> thông tin chi tiết type agent theo ID
   * @param id ID của type agent
   * @returns Thông tin chi tiết type agent
   */
  async getTypeAgentById(id: number): Promise<TypeAgentDetail> {
    try {
      const response = await apiClient.get<{ result: TypeAgentDetail }>(`${this.baseUrl}/${id}`, {
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error(`Error fetching type agent ${id}:`, error);
      throw error;
    }
  }

  /**
   * Tạo type agent mới
   * @param data Dữ liệu tạo type agent
   * @returns ID của type agent được tạo
   */
  async createTypeAgent(data: CreateTypeAgentParams): Promise<CreateTypeAgentResponse['result']> {
    try {
      const response = await apiClient.post<CreateTypeAgentResponse>(this.baseUrl, data, {
        tokenType: 'admin',
      });
      return response.result;
    } catch (error) {
      console.error('Error creating type agent:', error);
      throw error;
    }
  }

  /**
   * Cập nhật type agent
   * @param id ID của type agent
   * @param data Dữ liệu cập nhật
   * @returns Kết quả cập nhật
   */
  async updateTypeAgent(id: number, data: UpdateTypeAgentParams): Promise<boolean> {
    try {
      await apiClient.patch(`${this.baseUrl}/${id}`, data, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error updating type agent ${id}:`, error);
      throw error;
    }
  }

  /**
   * Xóa type agent
   * @param id ID của type agent
   * @returns Kết quả xóa
   */
  async deleteTypeAgent(id: number): Promise<boolean> {
    try {
      await apiClient.delete(`${this.baseUrl}/${id}`, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error deleting type agent ${id}:`, error);
      throw error;
    }
  }

  /**
   * Lấy danh sách agent systems
   * @returns Danh sách agent systems
   */
  async getAgentSystems(): Promise<AgentSystemListItem[]> {
    try {
      const response = await apiClient.get<{ result: { items: AgentSystemListItem[] } }>('/admin/agents/system', {
        tokenType: 'admin',
      });
      return response.result.items;
    } catch (error) {
      console.error('Error fetching agent systems:', error);
      throw error;
    }
  }

  /**
   * Lấy danh sách type agents đã xóa (trash)
   * @param params Tham số truy vấn
   * @returns Danh sách type agents đã xóa
   */
  async getTypeAgentsTrash(params: TypeAgentQueryParams): Promise<TypeAgentResponse['result']> {
    try {
      const response = await apiClient.get<TypeAgentResponse>(`${this.baseUrl}/trash`, {
        params,
        tokenType: 'admin'
      });
      return response.result;
    } catch (error) {
      console.error('Error fetching type agents trash:', error);
      throw error;
    }
  }

  /**
   * Xóa type agent với migration
   * @param id ID của type agent cần xóa
   * @param newTypeAgentId ID của type agent mới để chuyển đổi
   * @returns Kết quả xóa
   */
  async deleteTypeAgentWithMigration(id: number, newTypeAgentId: number): Promise<boolean> {
    try {
      await apiClient.delete(`${this.baseUrl}/${id}`, {
        data: { newTypeAgentId },
        tokenType: 'admin'
      });
      return true;
    } catch (error) {
      console.error(`Error deleting type agent ${id} with migration:`, error);
      throw error;
    }
  }



  /**
   * Khôi phục type agent từ trash
   * @param id ID của type agent cần khôi phục
   * @returns Kết quả khôi phục
   */
  async restoreTypeAgent(id: number): Promise<boolean> {
    try {
      await apiClient.post(`${this.baseUrl}/${id}/restore`, {}, { tokenType: 'admin' });
      return true;
    } catch (error) {
      console.error(`Error restoring type agent ${id}:`, error);
      throw error;
    }
  }
}

export const adminTypeAgentService = new AdminTypeAgentService();
