/**
 * Enum giới tính
 */
export enum Gender {
  MALE = 'male',
  FEMALE = 'female',
  OTHER = 'other',
}

/**
 * Interface thông tin cá nhân
 */
export interface PersonalInfo {
  fullName: string;
  gender: Gender;
  birthDate: string | Date;
  address: string;
  email: string;
  phone: string;
}

/**
 * Interface thông tin bảo mật
 */
export interface SecurityInfo {
  googleAuthEnabled: boolean;
  emailVerificationEnabled: boolean;
  currentPassword?: string;
  newPassword?: string;
  confirmPassword?: string;
}

/**
 * Interface thông tin doanh nghiệp
 */
export interface BusinessInfo {
  companyName: string;
  taxCode: string;
  representative: string;
  position: string;
  address: string;
  email: string;
  phone: string;
}

/**
 * Interface thông tin ngân hàng
 */
export interface BankInfo {
  bankName: string;
  accountNumber: string;
  branch: string;
  accountHolder: string;
}

/**
 * Interface tùy chọn thông báo
 */
export interface NotificationOption {
  id: string;
  name: string;
  enabled: boolean;
  description: string;
}

/**
 * Interface tùy chọn thông báo
 */
export interface NotificationSettings {
  options: NotificationOption[];
}

/**
 * Interface thông tin profile đầy đủ
 */
export interface UserProfile {
  id: string;
  username: string;
  avatarUrl: string;
  coverImageUrl: string;
  personalInfo: PersonalInfo;
  securityInfo: SecurityInfo;
  businessInfo: BusinessInfo;
  bankInfo: BankInfo;
  notificationSettings: NotificationSettings;
}

/**
 * Interface response từ API
 */
export interface ProfileResponse {
  profile: UserProfile;
}

/**
 * Interface request cập nhật thông tin cá nhân
 */
export interface UpdatePersonalInfoRequest {
  personalInfo: PersonalInfo;
}

/**
 * Interface request cập nhật thông tin bảo mật
 */
export interface UpdateSecurityInfoRequest {
  securityInfo: SecurityInfo;
}

/**
 * Interface request cập nhật thông tin doanh nghiệp
 */
export interface UpdateBusinessInfoRequest {
  businessInfo: BusinessInfo;
}

/**
 * Interface request cập nhật thông tin ngân hàng
 */
export interface UpdateBankInfoRequest {
  bankInfo: BankInfo;
}

/**
 * Interface request cập nhật tùy chọn thông báo
 */
export interface UpdateNotificationSettingsRequest {
  notificationSettings: NotificationSettings;
}

/**
 * Interface request cập nhật avatar
 */
export interface UpdateAvatarRequest {
  avatarUrl: string;
}

/**
 * Interface request cập nhật ảnh bìa
 */
export interface UpdateCoverImageRequest {
  coverImageUrl: string;
}

/**
 * Danh sách ngân hàng
 */
export interface Bank {
  id: string;
  name: string;
  code: string;
}

/**
 * Enum trạng thái form
 */
export enum FormStatus {
  IDLE = 'idle',
  EDITING = 'editing',
  SUBMITTING = 'submitting',
  SUCCESS = 'success',
  ERROR = 'error',
}
