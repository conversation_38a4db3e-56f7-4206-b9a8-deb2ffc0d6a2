import React, { useState, useCallback, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Textarea,
  Button,
  Typography,
  Select,
  Card,
  FormGrid,
  Divider,
  Icon,
  Slider,

} from '@/shared/components/common';
import MultiFileUpload, { FileWithMetadata } from '@/modules/data/components/MultiFileUpload';
import { z } from 'zod';
import { apiClient } from '@/shared/api/axios';
import { useAdminAgentNotification } from '../hooks/useAdminAgentNotification';
import { TypeProviderEnum } from '@/modules/ai-agents/types/enums';
import { getProviderIcon } from '@/modules/admin/integration/provider-model/types';

// Import the correct types from service
import {
  StrategyContent,
  StrategyExample,
  AgentStrategyDetail,
  UpdateAgentStrategyParams,
  UpdateAgentStrategyResponse
} from '../agent-strategy/types/agent-strategy.types';

// Types
interface SystemModel {
  id: string;
  modelId: string;
  provider: string;
  name?: string;
}

interface SystemModelsResponse {
  items?: SystemModel[];
  meta?: {
    totalItems: number;
    itemCount: number;
    itemsPerPage: number;
    totalPages: number;
    currentPage: number;
    hasItems: boolean;
  };
}

// Component hiển thị card cho provider
interface ProviderCardProps {
  provider: TypeProviderEnum;
  name: string;
  isSelected: boolean;
  onClick: (provider: TypeProviderEnum) => void;
  disabled?: boolean;
}

const ProviderCard: React.FC<ProviderCardProps> = ({
  provider,
  name,
  isSelected,
  onClick,
  disabled = false
}) => {
  return (
    <Card
      variant={isSelected ? 'elevated' : 'default'}
      className={`cursor-pointer transition-all duration-200 ${
        isSelected
          ? 'border-2 border-primary-500 shadow-md bg-primary-50 dark:bg-primary-900/20'
          : 'hover:border-gray-300 hover:shadow-sm'
      } ${disabled ? 'opacity-50 cursor-not-allowed' : ''}`}
      onClick={() => !disabled && onClick(provider)}
      noPadding={false}
    >
      <div className="flex items-center p-3">
        <div className="mr-3">
          <Icon name={getProviderIcon(provider)} size="md" />
        </div>
        <div className="font-medium text-foreground">{name}</div>
      </div>
    </Card>
  );
};

interface EditAgentStrategyFormProps {
  agentStrategy: AgentStrategyDetail;
  onCancel: () => void;
  onSuccess?: () => void;
}

// Schema validation
// eslint-disable-next-line @typescript-eslint/no-explicit-any
const updateAgentStrategySchema = (t: any) => z.object({
  name: z.string()
    .min(1, t('admin:agent.strategy.validation.nameRequired', 'Tên chiến lược là bắt buộc'))
    .trim(),
  instruction: z.string()
    .min(1, t('admin:agent.strategy.validation.instructionRequired', 'Hướng dẫn là bắt buộc'))
    .trim(),
  systemModelId: z.string()
    .min(1, t('admin:agent.strategy.validation.systemModelRequired', 'System model là bắt buộc'))
    .uuid(t('admin:agent.strategy.validation.systemModelRequired', 'System Model ID phải là UUID hợp lệ')),
});

const EditAgentStrategyForm: React.FC<EditAgentStrategyFormProps> = ({ 
  agentStrategy, 
  onCancel, 
  onSuccess 
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const {
    updateSuccess,
    updateError,
    uploadSuccess,
    uploadError,
    loadError,
    validationError,
    processing
  } = useAdminAgentNotification();
  
  // Avatar upload states
  const [avatarFiles, setAvatarFiles] = useState<FileWithMetadata[]>([]);
  
  // Model config states - Initialize with default values since API doesn't return modelConfig
  const [modelConfig, setModelConfig] = useState({
    temperature: 0.7,
    max_tokens: 2000,
    top_p: 0.95,
    top_k: 0.8,
  });

  // Content and example states - Initialize with existing data
  const [contentSteps, setContentSteps] = useState<StrategyContent[]>(
    agentStrategy.content && agentStrategy.content.length > 0 
      ? agentStrategy.content 
      : [{ stepOrder: 1, content: '' }]
  );
  const [exampleSteps, setExampleSteps] = useState<StrategyExample[]>(
    agentStrategy.exampleDefault && agentStrategy.exampleDefault.length > 0 
      ? agentStrategy.exampleDefault 
      : [{ stepOrder: 1, content: '' }]
  );

  // System models states
  const [systemModels, setSystemModels] = useState<SystemModel[]>([]);
  const [loadingSystemModels, setLoadingSystemModels] = useState(false);
  
  // Determine provider from existing model
  const getProviderFromModelId = (modelId: string): TypeProviderEnum => {
    if (modelId.includes('gpt')) return TypeProviderEnum.OPENAI;
    if (modelId.includes('claude')) return TypeProviderEnum.ANTHROPIC;
    if (modelId.includes('gemini')) return TypeProviderEnum.GOOGLE;
    if (modelId.includes('llama')) return TypeProviderEnum.META;
    if (modelId.includes('deepseek')) return TypeProviderEnum.DEEPSEEK;
    if (modelId.includes('grok')) return TypeProviderEnum.XAI;
    return TypeProviderEnum.OPENAI; // default
  };
  
  const [selectedProvider, setSelectedProvider] = useState<TypeProviderEnum>(
    getProviderFromModelId(agentStrategy.modelId || '')
  );

  // Default form values - Initialize with existing data
  const defaultValues = {
    name: agentStrategy.name || '',
    instruction: agentStrategy.instruction || '',
    systemModelId: agentStrategy.modelSystemId || '',
  };

  // Load system models - sử dụng useCallback để tránh re-render
  const loadSystemModels = useCallback(async (provider: string) => {
    try {
      setLoadingSystemModels(true);
      const params = new URLSearchParams({
        page: '1',
        limit: '40',
        sortBy: 'systemModels.modelId',
        provider: provider
      });

      const response = await apiClient.get<SystemModelsResponse>(`/admin/system-models?${params.toString()}`);
      console.log('🔍 [EditAgentStrategyForm] System models response:', response);

      const result = response.result as SystemModelsResponse;
      if (result?.items && Array.isArray(result.items)) {
        setSystemModels(result.items);
      } else if (Array.isArray(result)) {
        setSystemModels(result);
      } else {
        console.warn('Unexpected response structure for system models:', response);
        setSystemModels([]);
      }
    } catch (err) {
      console.error('Error loading system models:', err);
      setSystemModels([]);
      loadError(t('admin:agent.strategy.pageTitle', 'Chiến lược Agent'));
    } finally {
      setLoadingSystemModels(false);
    }
  }, [loadError, t]);

  // Load system models khi component mount với provider mặc định
  useEffect(() => {
    loadSystemModels(selectedProvider);
  }, [selectedProvider, loadSystemModels]);

  // Handle provider selection
  const handleProviderSelect = (provider: TypeProviderEnum) => {
    setSelectedProvider(provider);
    setSystemModels([]);
  };

  // Handle model config changes
  const handleModelConfigChange = (key: keyof typeof modelConfig, value: number) => {
    setModelConfig(prev => ({
      ...prev,
      [key]: value,
    }));
  };

  // Danh sách providers
  const providers = [
    { type: TypeProviderEnum.OPENAI, name: 'OpenAI' },
    { type: TypeProviderEnum.ANTHROPIC, name: 'Anthropic' },
    { type: TypeProviderEnum.GOOGLE, name: 'Google' },
    { type: TypeProviderEnum.META, name: 'Meta' },
    { type: TypeProviderEnum.DEEPSEEK, name: 'DeepSeek' },
    { type: TypeProviderEnum.XAI, name: 'XAI' },
  ];

  // Handle avatar file selection - chỉ cho phép 1 ảnh
  const handleAvatarChange = useCallback((files: FileWithMetadata[]) => {
    if (files.length > 0 && files[0]) {
      setAvatarFiles([files[0]]);
    } else {
      setAvatarFiles([]);
    }
  }, []);

  // Handle content steps
  const addContentStep = () => {
    const newStep: StrategyContent = {
      stepOrder: contentSteps.length + 1,
      content: ''
    };
    setContentSteps([...contentSteps, newStep]);
    
    // Đồng bộ thêm example step
    const newExampleStep: StrategyExample = {
      stepOrder: exampleSteps.length + 1,
      content: ''
    };
    setExampleSteps([...exampleSteps, newExampleStep]);
  };

  const removeContentStep = (index: number) => {
    if (contentSteps.length > 1) {
      const newSteps = contentSteps.filter((_, i) => i !== index);
      const reorderedSteps = newSteps.map((step, i) => ({
        ...step,
        stepOrder: i + 1
      }));
      setContentSteps(reorderedSteps);
      
      // Đồng bộ xóa example step
      if (exampleSteps.length > 1) {
        const newExampleSteps = exampleSteps.filter((_, i) => i !== index);
        const reorderedExampleSteps = newExampleSteps.map((step, i) => ({
          ...step,
          stepOrder: i + 1
        }));
        setExampleSteps(reorderedExampleSteps);
      }
    }
  };

  const updateContentStep = (index: number, content: string) => {
    const newSteps = [...contentSteps];
    if (newSteps[index]) {
      newSteps[index] = {
        ...newSteps[index],
        content,
        stepOrder: newSteps[index].stepOrder || index + 1
      };
      setContentSteps(newSteps);
    }
  };

  // Handle example steps
  const addExampleStep = () => {
    const newStep: StrategyExample = {
      stepOrder: exampleSteps.length + 1,
      content: ''
    };
    setExampleSteps([...exampleSteps, newStep]);
  };

  const removeExampleStep = (index: number) => {
    if (exampleSteps.length > 1) {
      const newSteps = exampleSteps.filter((_, i) => i !== index);
      const reorderedSteps = newSteps.map((step, i) => ({
        ...step,
        stepOrder: i + 1
      }));
      setExampleSteps(reorderedSteps);
    }
  };

  const updateExampleStep = (index: number, content: string) => {
    const newSteps = [...exampleSteps];
    if (newSteps[index]) {
      newSteps[index] = {
        ...newSteps[index],
        content,
        stepOrder: newSteps[index].stepOrder || index + 1
      };
      setExampleSteps(newSteps);
    }
  };

  // Upload image file to S3 using presigned URL với method PUT
  const uploadImageToS3 = async (file: File, presignedUrl: string): Promise<void> => {
    console.log(`🔍 [uploadImageToS3] Starting upload:`, {
      fileName: file.name,
      fileSize: file.size,
      fileType: file.type,
      uploadUrl: presignedUrl
    });

    try {
      const response = await fetch(presignedUrl, {
        method: 'PUT',
        headers: {
          'Content-Type': file.type,
        },
        body: file,
      });

      if (!response.ok) {
        const errorText = await response.text();
        throw new Error(`S3 Upload failed: ${response.status} ${response.statusText} - ${errorText}`);
      }

      console.log('✅ [uploadImageToS3] Upload successful');
    } catch (error) {
      console.error('❌ [uploadImageToS3] Upload failed:', error);
      throw error;
    }
  };

  // Handle form submission
  // eslint-disable-next-line @typescript-eslint/no-explicit-any
  const handleFormSubmit = async (values: Record<string, any>) => {
    console.log('🔍 [EditAgentStrategyForm] Form submitted with values:', values);
    console.log('🔍 [EditAgentStrategyForm] Model config:', modelConfig);
    console.log('🔍 [EditAgentStrategyForm] Avatar files:', avatarFiles);
    console.log('🔍 [EditAgentStrategyForm] Content steps:', contentSteps);
    console.log('🔍 [EditAgentStrategyForm] Example steps:', exampleSteps);

    // Validate content and examples
    const validContentSteps = contentSteps.filter(step => step.content.trim() !== '');
    const validExampleSteps = exampleSteps.filter(step => step.content.trim() !== '');

    if (validContentSteps.length === 0) {
      validationError(t('admin:agent.strategy.validation.contentRequired', 'Nội dung các bước là bắt buộc'));
      return;
    }

    if (validExampleSteps.length === 0) {
      validationError(t('admin:agent.strategy.validation.exampleRequired', 'Ví dụ mặc định là bắt buộc'));
      return;
    }

    setIsSubmitting(true);

    try {
      // Prepare form data theo đúng API spec
      const strategyData: UpdateAgentStrategyParams = {
        name: values['name'] as string,
        instruction: values['instruction'] as string,
        vectorStoreId: null, // Always null as per requirement
        modelConfig,
        content: validContentSteps,
        exampleDefault: validExampleSteps,
        systemModelId: values['systemModelId'] as string,
      };

      // Thêm avatarMimeType nếu có file
      if (avatarFiles.length > 0 && avatarFiles[0]) {
        strategyData.avatarMimeType = avatarFiles[0].file.type;
      }

      console.log('🔍 [EditAgentStrategyForm] Updating strategy data:', strategyData);

      // Submit form to API với method PUT
      const response = await apiClient.put<{
        code: number;
        message: string;
        result: UpdateAgentStrategyResponse;
      }>(`/admin/agent-strategy/${agentStrategy.id}`, strategyData);

      console.log('🔍 [EditAgentStrategyForm] Update response:', response);

      // Upload avatar if provided and we have upload URL
      if (avatarFiles.length > 0 && avatarFiles[0] && response.result?.avatarUploadUrl) {
        try {
          processing(t('admin:agent.strategy.form.uploadingAvatar', 'Đang tải lên avatar...'));

          await uploadImageToS3(avatarFiles[0].file, response.result.avatarUploadUrl);

          uploadSuccess(t('admin:agent.strategy.form.uploadAvatarSuccess', 'Tải lên avatar thành công'));
        } catch (uploadErr) {
          console.error('❌ Error uploading avatar to S3:', uploadErr);
          uploadError(t('admin:agent.strategy.form.uploadAvatarError', 'Có lỗi xảy ra khi tải lên avatar'));
          // Không throw error ở đây để form vẫn được coi là thành công
        }
      }

      // Show success message
      updateSuccess(t('admin:agent.strategy.form.updateSuccess', 'Cập nhật chiến lược thành công'));

      // Call success callback
      if (onSuccess) {
        onSuccess();
      }

    } catch (err) {
      console.error('❌ Error updating strategy:', err);
      updateError(t('admin:agent.strategy.form.updateError', 'Có lỗi xảy ra khi cập nhật chiến lược'));
    } finally {
      setIsSubmitting(false);
    }
  };

  return (
    <Card className="">
      <div className="p-6">
        <div className="flex items-center justify-between mb-6">
          <Typography variant="h5" className="font-semibold">
            {t('admin:agent.strategy.editStrategy', 'Chỉnh sửa Chiến lược')}
          </Typography>
          <Button
            variant="ghost"
            size="sm"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            <Icon name="x" size="sm" />
          </Button>
        </div>
      </div>

      <Form
        schema={updateAgentStrategySchema(t)}
        onSubmit={handleFormSubmit}
        defaultValues={defaultValues}
        className="space-y-6"
      >
        {/* Basic Information */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.strategy.form.basicInfo', 'Thông tin cơ bản')}
          </Typography>

          <FormGrid columns={1} gap="md">
            <FormItem
              name="name"
              label={t('admin:agent.strategy.form.name', 'Tên Chiến lược')}
              required
            >
              <Input
                fullWidth
                placeholder={t('admin:agent.strategy.form.namePlaceholder', 'Nhập tên chiến lược')}
              />
            </FormItem>

            <FormItem
              name="instruction"
              label={t('admin:agent.strategy.form.instruction', 'Hướng dẫn')}
              required
            >
              <Textarea
                fullWidth
                rows={4}
                placeholder={t('admin:agent.strategy.form.instructionPlaceholder', 'Nhập hướng dẫn cho chiến lược')}
              />
            </FormItem>
          </FormGrid>
        </div>

        <Divider />

        {/* Avatar Upload */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.strategy.form.avatar', 'Avatar')}
          </Typography>
          
          {agentStrategy.avatar && (
            <div className="mb-4">
              <Typography variant="body2" className="mb-2">
                {t('admin:agent.strategy.form.currentAvatar', 'Avatar hiện tại')}:
              </Typography>
              <img 
                src={agentStrategy.avatar} 
                alt="Current avatar" 
                className="w-20 h-20 rounded-lg object-cover"
              />
            </div>
          )}

          <MultiFileUpload
            label={t('admin:agent.strategy.form.avatarUpload', 'Tải lên avatar')}
            accept="image/jpeg,image/png"
            placeholder={t('admin:agent.strategy.form.avatarHelp', 'Hỗ trợ định dạng: JPG, PNG (chỉ 1 ảnh)')}
            value={avatarFiles}
            onChange={handleAvatarChange}
            mediaOnly={true}
            showPreview={true}
            height="h-32"
          />
        </div>

        <Divider />

        {/* Model Configuration */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.strategy.form.modelConfig', 'Cấu hình Model')}
          </Typography>

          <FormGrid columns={2} columnsMd={2} columnsSm={1} gap="md">
            <div className="space-y-3">
              <label className="block text-sm font-medium text-foreground">
                {t('admin:agent.form.temperature', 'Temperature')}
              </label>
              <Slider
                value={modelConfig.temperature}
                min={0}
                max={2}
                step={0.1}
                onValueChange={(value) => handleModelConfigChange('temperature', value)}
                valueSuffix=""
              />
            </div>

            <div className="space-y-3">
              <label className="block text-sm font-medium text-foreground">
                {t('admin:agent.form.topP', 'Top P')}
              </label>
              <Slider
                value={modelConfig.top_p}
                min={0}
                max={1}
                step={0.1}
                onValueChange={(value) => handleModelConfigChange('top_p', value)}
                valueSuffix=""
              />
            </div>

            <div className="space-y-3">
              <label className="block text-sm font-medium text-foreground">
                {t('admin:agent.form.topK', 'Top K')}
              </label>
              <Slider
                value={modelConfig.top_k}
                min={0}
                max={1}
                step={0.1}
                onValueChange={(value) => handleModelConfigChange('top_k', value)}
                valueSuffix=""
              />
            </div>

            <div className="space-y-3">
              <label className="block text-sm font-medium text-foreground">
                {t('admin:agent.form.maxTokens', 'Max Tokens')}
              </label>
              <Slider
                value={modelConfig.max_tokens}
                min={1}
                max={4096}
                step={1}
                onValueChange={(value) => handleModelConfigChange('max_tokens', value)}
                valueSuffix=""
              />
            </div>
          </FormGrid>
        </div>

        <Divider />

        {/* Provider Selection */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.provider', 'Loại Provider')}
          </Typography>

          <div className="flex flex-nowrap gap-3 overflow-x-auto pb-2">
            {providers.map((provider) => (
              <ProviderCard
                key={provider.type}
                provider={provider.type}
                name={provider.name}
                isSelected={selectedProvider === provider.type}
                onClick={handleProviderSelect}
                disabled={isSubmitting}
              />
            ))}
          </div>
        </div>

        <Divider />

        {/* Model Selection */}
        <div className="space-y-4">
          <Typography variant="subtitle1" className="font-medium">
            {t('admin:agent.form.resources', 'Tài nguyên')}
          </Typography>

          <FormGrid columns={1} gap="md">
            <FormItem
              name="systemModelId"
              label={t('admin:agent.strategy.form.model', 'Model')}
              required
            >
              <Select
                fullWidth
                loading={loadingSystemModels}
                placeholder={t('admin:agent.strategy.form.selectModel', 'Chọn model')}
                options={systemModels.map(model => ({
                  value: model.id,
                  label: model.modelId,
                }))}
              />
            </FormItem>
          </FormGrid>
        </div>

        <Divider />

        {/* Content Steps */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Typography variant="subtitle1" className="font-medium">
              {t('admin:agent.strategy.form.content', 'Nội dung các bước')}
            </Typography>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addContentStep}
              disabled={isSubmitting}
            >
              <Icon name="plus" size="sm" className="mr-2" />
              {t('admin:agent.strategy.form.addStep', 'Thêm bước')}
            </Button>
          </div>

          <div className="space-y-3">
            {contentSteps.map((step, index) => (
              <div key={index} className="flex items-start gap-3">
                <div className="flex-1">
                  <div className="space-y-2">
                    <Typography variant="body2" className="font-medium text-gray-700 dark:text-gray-300">
                      Bước {step.stepOrder}
                    </Typography>
                    <Textarea
                      fullWidth
                      rows={3}
                      placeholder={t('admin:agent.strategy.form.contentPlaceholder', 'Nhập nội dung bước {step}', { step: step.stepOrder })}
                      value={step.content}
                      onChange={(e) => updateContentStep(index, e.target.value)}
                    />
                  </div>
                </div>
                {contentSteps.length > 1 && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeContentStep(index)}
                    disabled={isSubmitting}
                    className="text-red-500 hover:text-red-600 mt-6"
                  >
                    <Icon name="trash" size="sm" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>

        <Divider />

        {/* Example Steps */}
        <div className="space-y-4">
          <div className="flex items-center justify-between">
            <Typography variant="subtitle1" className="font-medium">
              {t('admin:agent.strategy.form.exampleDefault', 'Ví dụ mặc định')}
            </Typography>
            <Button
              type="button"
              variant="outline"
              size="sm"
              onClick={addExampleStep}
              disabled={isSubmitting}
            >
              <Icon name="plus" size="sm" className="mr-2" />
              {t('admin:agent.strategy.form.addExample', 'Thêm ví dụ')}
            </Button>
          </div>

          <div className="space-y-3">
            {exampleSteps.map((step, index) => (
              <div key={index} className="flex items-start gap-3">
                <div className="flex-1">
                  <div className="space-y-2">
                    <Typography variant="body2" className="font-medium text-gray-700 dark:text-gray-300">
                      Bước {step.stepOrder}
                    </Typography>
                    <Textarea
                      fullWidth
                      rows={3}
                      placeholder={t('admin:agent.strategy.form.examplePlaceholder', 'Nhập ví dụ cho bước {step}', { step: step.stepOrder })}
                      value={step.content}
                      onChange={(e) => updateExampleStep(index, e.target.value)}
                    />
                  </div>
                </div>
                {exampleSteps.length > 1 && (
                  <Button
                    type="button"
                    variant="ghost"
                    size="sm"
                    onClick={() => removeExampleStep(index)}
                    disabled={isSubmitting}
                    className="text-red-500 hover:text-red-600 mt-6"
                  >
                    <Icon name="trash" size="sm" />
                  </Button>
                )}
              </div>
            ))}
          </div>
        </div>

        {/* Form Actions */}
        <div className="flex justify-end space-x-3 pt-6 border-t border-border">
          <Button
            type="button"
            variant="outline"
            onClick={onCancel}
            disabled={isSubmitting}
          >
            {t('admin:agent.strategy.cancel', 'Hủy')}
          </Button>
          <Button
            type="submit"
            variant="primary"
            isLoading={isSubmitting}
          >
            {t('admin:agent.strategy.form.update', 'Cập nhật Chiến lược')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default EditAgentStrategyForm;
