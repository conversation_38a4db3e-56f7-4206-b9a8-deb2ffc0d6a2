import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';
import AdminAgentTemplateGrid from '../components/AdminAgentTemplateGrid';
import AddAgentTemplateForm from '../components/AddAgentTemplateForm';
import SlideInForm from '@/shared/components/common/SlideInForm';

/**
 * Trang quản lý Agent Template
 */
const AgentTemplatePage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const navigate = useNavigate();

  // Filter states
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [search, setSearch] = useState('');
  const [showAddForm, setShowAddForm] = useState(false);

  const handleSearch = (term: string) => {
    setSearch(term);
    setPage(1); // Reset về trang đầu khi search
  };

  const handleAddTemplate = () => {
    setShowAddForm(true);
  };

  const handleAddFormCancel = () => {
    setShowAddForm(false);
  };

  const handleAddFormSuccess = (agentId: string) => {
    setShowAddForm(false);
    console.log('Agent template created with ID:', agentId);
    // Refresh the grid or navigate to the new template
  };

  const handleViewTrash = () => {
    navigate('/admin/agent/template/trash');
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset về trang đầu khi thay đổi limit
  };

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleAddTemplate}
        items={[]}
        additionalIcons={[
          {
            icon: 'trash',
            tooltip: t('admin:agent.template.viewTrash', 'Xem thùng rác'),
            variant: 'default',
            onClick: handleViewTrash,
            className: 'text-gray-600 hover:text-gray-800',
          }
        ]}
      />

      <AdminAgentTemplateGrid
        searchTerm={search}
        page={page}
        limit={limit}
        onPageChange={handlePageChange}
        onLimitChange={handleLimitChange}
        isTrashPage={false}
      />

      {/* Add Agent Template Form */}
      <SlideInForm isVisible={showAddForm}>
        <AddAgentTemplateForm
          onCancel={handleAddFormCancel}
          onSuccess={handleAddFormSuccess}
        />
      </SlideInForm>
    </div>
  );
};

export default AgentTemplatePage;
