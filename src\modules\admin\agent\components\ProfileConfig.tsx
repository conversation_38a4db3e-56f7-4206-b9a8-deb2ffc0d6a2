import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Grid,
  Input,
  Select,
  SelectOption,
  Chip,
  Button,
  Textarea,
  Icon
} from '@/shared/components/common';

interface ProfileData {
  birthDate: string;
  gender: string;
  language: string;
  education: string;
  country: string;
  position: string;
  skills: string[];
  personality: string;
}

interface ProfileConfigProps {
  initialData?: ProfileData;
  onSave?: (data: ProfileData) => void;
}

const ProfileConfig: React.FC<ProfileConfigProps> = ({
  initialData,
  onSave
}) => {
  const { t } = useTranslation(['admin', 'common']);

  const [profileData, setProfileData] = useState<ProfileData>(initialData || {
    birthDate: '2000-08-01',
    gender: t('admin:agentTemplate.female'),
    language: 'English',
    education: t('admin:agentTemplate.college'),
    country: 'United States',
    position: t('admin:agentTemplate.positionPlaceholder'),
    skills: [t('admin:agentTemplate.skills'), t('admin:agentTemplate.skills')],
    personality: t('admin:agentTemplate.personalityPlaceholder')
  });

  const [skillInput, setSkillInput] = useState('');

  const handleChange = (field: keyof ProfileData, value: any) => {
    const newData = { ...profileData, [field]: value };
    setProfileData(newData);
    if (onSave) onSave(newData);
  };

  const handleAddSkill = () => {
    if (skillInput.trim() && !profileData.skills.includes(skillInput.trim())) {
      const newSkills = [...profileData.skills, skillInput.trim()];
      handleChange('skills', newSkills);
      setSkillInput('');
    }
  };

  const handleRemoveSkill = (skillToRemove: string) => {
    const newSkills = profileData.skills.filter(skill => skill !== skillToRemove);
    handleChange('skills', newSkills);
  };

  const handleSkillInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddSkill();
    }
  };

  useEffect(() => {
    if (onSave) onSave(profileData);
  }, []);

  return (
    <div>
      <Typography variant="h6" className="mb-4">
        {t('admin:agentTemplate.profileConfig')}
      </Typography>

      <Card>
        <div className="p-6">
          <div className="grid grid-cols-1 md:grid-cols-2 gap-6">
            {/* Birth Date */}
            <div>
              <Input
                type="date"
                label={t('admin:agentTemplate.birthDate')}
                value={profileData.birthDate}
                onChange={(e) => handleChange('birthDate', e.target.value)}
                className="w-full"
              />
            </div>

            {/* Gender */}
            <div>
              <Select
                label={t('admin:agentTemplate.gender')}
                value={profileData.gender}
                onChange={(value) => handleChange('gender', value)}
                className="w-full"
              >
                <SelectOption value="Nam">{t('admin:agentTemplate.male')}</SelectOption>
                <SelectOption value="Nữ">{t('admin:agentTemplate.female')}</SelectOption>
                <SelectOption value="Khác">{t('admin:agentTemplate.other')}</SelectOption>
              </Select>
            </div>

            {/* Language */}
            <div>
              <Select
                label={t('admin:agentTemplate.language')}
                value={profileData.language}
                onChange={(value) => handleChange('language', value)}
                className="w-full"
              >
                <SelectOption value="Tiếng Việt">Tiếng Việt</SelectOption>
                <SelectOption value="English">English</SelectOption>
                <SelectOption value="中文">中文</SelectOption>
              </Select>
            </div>

            {/* Education */}
            <div>
              <Select
                label={t('admin:agentTemplate.education')}
                value={profileData.education}
                onChange={(value) => handleChange('education', value)}
                className="w-full"
              >
                <SelectOption value="Trung học">{t('admin:agentTemplate.highSchool')}</SelectOption>
                <SelectOption value="Cao đẳng">{t('admin:agentTemplate.college')}</SelectOption>
                <SelectOption value="Đại học">{t('admin:agentTemplate.university')}</SelectOption>
                <SelectOption value="Sau đại học">{t('admin:agentTemplate.postgraduate')}</SelectOption>
              </Select>
            </div>

            {/* Country */}
            <div>
              <Select
                label={t('admin:agentTemplate.country')}
                value={profileData.country}
                onChange={(value) => handleChange('country', value)}
                className="w-full"
              >
                <SelectOption value="Việt Nam">Việt Nam</SelectOption>
                <SelectOption value="United States">United States</SelectOption>
                <SelectOption value="China">China</SelectOption>
                <SelectOption value="Japan">Japan</SelectOption>
              </Select>
            </div>

            {/* Position */}
            <div>
              <Input
                label={t('admin:agentTemplate.position')}
                value={profileData.position}
                onChange={(e) => handleChange('position', e.target.value)}
                placeholder={t('admin:agentTemplate.positionPlaceholder')}
                className="w-full"
              />
            </div>
          </div>

          {/* Skills */}
          <div className="mt-6">
            <Typography variant="subtitle2" className="mb-3">
              {t('admin:agentTemplate.skills')}
            </Typography>
            <div className="mb-4">
              {profileData.skills.map((skill, index) => (
                <Chip
                  key={index}
                  label={skill}
                  onRemove={() => handleRemoveSkill(skill)}
                  className="mr-2 mb-2"
                />
              ))}
            </div>
            <div className="flex gap-2">
              <Input
                placeholder={t('admin:agentTemplate.skillPlaceholder')}
                value={skillInput}
                onChange={(e) => setSkillInput(e.target.value)}
                onKeyDown={handleSkillInputKeyDown}
                className="flex-1"
              />
              <Button
                onClick={handleAddSkill}
                variant="outline"
                icon="plus"
                size="sm"
              />
            </div>
          </div>

          {/* Personality */}
          <div className="mt-6">
            <Textarea
              label={t('admin:agentTemplate.personality')}
              value={profileData.personality}
              onChange={(e) => handleChange('personality', e.target.value)}
              placeholder={t('admin:agentTemplate.personalityPlaceholder')}
              rows={3}
              className="w-full"
            />
          </div>
        </div>
      </Card>
    </div>
  );
};

export default ProfileConfig;
