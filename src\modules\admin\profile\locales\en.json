{"profile": {"title": "Account Information", "header": {"registerAffiliate": "Register Affiliate"}, "rpoint": {"historyTitle": "R-Point Deposit History", "amount": "R-Point Amount", "paymentMethod": "Payment Method", "transactionId": "Transaction ID", "status": "Status", "searchPlaceholder": "Search by transaction ID", "startDate": "From Date", "endDate": "To Date", "noData": "No R-Point deposit history data"}, "purchaseHistory": {"title": "Purchase History", "rpointTitle": "R-Point Deposit History", "purchaseDate": "Purchase Date", "createdAt": "Created Date", "amount": "Payment", "points": "R-Point Amount", "status": "Status", "statusCompleted": "Completed", "statusPending": "Pending", "invoice": "Invoice", "download": "Download", "processing": "Processing", "searchPlaceholder": "Search by ID or purchase date", "startDate": "From Date", "endDate": "To Date", "noData": "No purchase history data"}, "personalInfo": {"title": "Personal Information", "fullName": "Full Name", "gender": "Gender", "birthDate": "Date of Birth", "address": "Address", "email": "Email", "phone": "Phone Number", "genderOptions": {"male": "Male", "female": "Female", "other": "Other"}}, "security": {"title": "Security", "googleAuth": "Google Authenticator Verification", "smsAuth": "SMS Authentication", "emailAuth": "Email <PERSON>", "emailVerification": "Email Verification Link", "currentPassword": "Your Current Password", "newPassword": "Your New Password", "confirmPassword": "Confirm Your New Password", "accountSecurity": "Account Security", "securityRecommendation": "We recommend enabling two-factor authentication to add an extra layer of security to your account.", "setupGoogleAuth": "Setup Google Authenticator", "scanQrCode": "Scan QR code with Google Authenticator app", "secretKey": "Or enter this secret key into the app:", "enterOtp": "Enter verification code from Google Authenticator app", "verifyAndEnable": "Verify and Enable", "setupSuccess": "Google Authenticator setup successful", "setupError": "An error occurred while setting up Google Authenticator", "passwordSection": "Change Password"}, "businessInfo": {"title": "Business Information", "companyName": "Company Name", "taxCode": "Tax Code", "representative": "Representative", "position": "Position", "address": "Address", "email": "Email", "phone": "Phone Number"}, "bankInfo": {"title": "Bank Information", "bankName": "Bank Name", "accountNumber": "Account Number", "branch": "Branch", "accountHolder": "Account Holder", "selectBank": "Select Bank"}, "notifications": {"title": "Email Preferences", "description": "Manage the types of email notifications you want to receive", "options": {"account": {"name": "General - Receive emails related to account & system", "description": "Receive notifications about account and system changes"}, "invoice": {"name": "Invoices - Receive emails for new invoices, reminders & overdue notices", "description": "Receive notifications about new invoices and payment reminders"}, "features": {"name": "New Features - Receive emails about new feature updates", "description": "Receive notifications about new system features"}, "affiliate": {"name": "Affiliate - Receive emails about the Affiliate program", "description": "Receive notifications about the Affiliate program"}, "resources": {"name": "Resources - Receive emails about documents & guides", "description": "Receive notifications about new documents and guides"}, "promotions": {"name": "Promotions - Receive emails about promotional campaigns", "description": "Receive notifications about promotional campaigns"}, "accountSystem": {"name": "Account System", "description": "Receive notifications about the account system."}, "billing": {"name": "Billing", "description": "Receive notifications about invoices and payments."}, "newFeature": {"name": "New Features", "description": "Receive notifications about new features."}, "documentation": {"name": "Documentation", "description": "Receive notifications about new documentation."}, "promotional": {"name": "Promotional", "description": "Receive notifications about promotional offers."}}}, "buttons": {"save": "Save", "cancel": "Cancel", "edit": "Edit", "changeAvatar": "Change Avatar", "changeCover": "Change Cover Image", "changePassword": "Change Password"}, "messages": {"updateSuccess": "Updated successfully", "updateError": "An error occurred while updating", "loadingError": "An error occurred while loading data", "passwordChangeSuccess": "Password changed successfully", "passwordChangeError": "An error occurred while changing password"}, "error": {"loadingProfile": "Error loading personal information"}, "loading": {"loadingProfile": "Loading personal information..."}}, "validation": {"required": "{{field}} is required", "fullName": {"min": "Full name must be at least 2 characters", "max": "Full name must not exceed 100 characters", "required": "Full name is required"}, "gender": {"required": "Gender is required"}, "address": {"min": "Address must be at least 5 characters", "max": "Address must not exceed 200 characters", "required": "Address is required"}, "email": {"invalid": "Invalid email address", "required": "Email is required"}, "phone": {"invalid": "Invalid phone number", "required": "Phone number is required", "duplicate": "Phone number is already in use"}, "currentPassword": {"required": "Please enter your current password"}, "password": {"min": "Password must be at least 8 characters", "uppercase": "Password must contain at least one uppercase letter", "lowercase": "Password must contain at least one lowercase letter", "number": "Password must contain at least one number", "special": "Password must contain at least one special character"}, "confirmPassword": {"match": "Passwords do not match"}, "companyName": {"min": "Company name must be at least 2 characters", "max": "Company name must not exceed 100 characters"}, "taxCode": {"invalid": "Invalid tax code"}, "representative": {"min": "Representative name must be at least 2 characters", "max": "Representative name must not exceed 100 characters"}, "position": {"min": "Position must be at least 2 characters", "max": "Position must not exceed 100 characters"}, "bankName": {"required": "Please select a bank"}, "accountNumber": {"invalid": "Invalid account number"}, "branch": {"min": "Branch must be at least 2 characters", "max": "Branch must not exceed 100 characters"}, "accountHolder": {"min": "Account holder name must be at least 2 characters", "max": "Account holder name must not exceed 100 characters"}}}