import React from 'react';
import { render, screen, fireEvent, waitFor } from '@testing-library/react';
import '@testing-library/jest-dom';
import { QueryClient, QueryClientProvider } from '@tanstack/react-query';
import { BrowserRouter } from 'react-router-dom';
import { I18nextProvider } from 'react-i18next';
import i18n from '@/lib/i18n';
import LoginPage from '../LoginPage';
import { env } from '@/shared/utils';

// Mock the auth hooks
jest.mock('../hooks/useAuthQuery', () => ({
  useGoogleAuthUrl: () => ({
    mutate: jest.fn(),
  }),
  useFacebookAuthUrl: () => ({
    mutate: jest.fn(),
  }),
  useZaloAuthUrl: () => ({
    mutate: jest.fn(),
  }),
}));

// Mock the env utility
jest.mock('@/shared/utils', () => ({
  env: {
    googleLoginUrl: '',
    facebookLoginUrl: '',
    zaloLoginUrl: '',
    googleRedirectUri: 'https://v2.redai.vn/auth/callback',
    facebookRedirectUri: 'https://v2.redai.vn/auth/callback',
    zaloRedirectUri: 'https://v2.redai.vn/auth/callback',
  },
}));

// Mock window.location.href
Object.defineProperty(window, 'location', {
  value: {
    href: '',
  },
  writable: true,
});

// Test wrapper component
const TestWrapper: React.FC<{ children: React.ReactNode }> = ({ children }) => {
  const queryClient = new QueryClient({
    defaultOptions: {
      queries: { retry: false },
      mutations: { retry: false },
    },
  });

  return (
    <QueryClientProvider client={queryClient}>
      <BrowserRouter>
        <I18nextProvider i18n={i18n}>
          {children}
        </I18nextProvider>
      </BrowserRouter>
    </QueryClientProvider>
  );
};

describe('LoginPage', () => {
  beforeEach(() => {
    // Reset window.location.href before each test
    window.location.href = '';
  });

  it('should render login page with social login buttons', () => {
    render(
      <TestWrapper>
        <LoginPage />
      </TestWrapper>
    );

    // Check if social login buttons are rendered
    expect(screen.getByLabelText(/loginWithFacebook/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/loginWithGoogle/i)).toBeInTheDocument();
    expect(screen.getByLabelText(/loginWithZalo/i)).toBeInTheDocument();
  });

  it('should call Google auth URL API when Google button is clicked and no direct URL is available', async () => {
    const mockGoogleAuthUrl = jest.fn();
    
    // Mock the hook to return our mock function
    jest.doMock('../hooks/useAuthQuery', () => ({
      useGoogleAuthUrl: () => ({
        mutate: mockGoogleAuthUrl,
      }),
      useFacebookAuthUrl: () => ({
        mutate: jest.fn(),
      }),
      useZaloAuthUrl: () => ({
        mutate: jest.fn(),
      }),
    }));

    render(
      <TestWrapper>
        <LoginPage />
      </TestWrapper>
    );

    const googleButton = screen.getByLabelText(/loginWithGoogle/i);
    fireEvent.click(googleButton);

    await waitFor(() => {
      expect(mockGoogleAuthUrl).toHaveBeenCalledWith(env.googleRedirectUri, expect.any(Object));
    });
  });

  it('should call Facebook auth URL API when Facebook button is clicked and no direct URL is available', async () => {
    const mockFacebookAuthUrl = jest.fn();
    
    // Mock the hook to return our mock function
    jest.doMock('../hooks/useAuthQuery', () => ({
      useGoogleAuthUrl: () => ({
        mutate: jest.fn(),
      }),
      useFacebookAuthUrl: () => ({
        mutate: mockFacebookAuthUrl,
      }),
      useZaloAuthUrl: () => ({
        mutate: jest.fn(),
      }),
    }));

    render(
      <TestWrapper>
        <LoginPage />
      </TestWrapper>
    );

    const facebookButton = screen.getByLabelText(/loginWithFacebook/i);
    fireEvent.click(facebookButton);

    await waitFor(() => {
      expect(mockFacebookAuthUrl).toHaveBeenCalledWith(env.facebookRedirectUri, expect.any(Object));
    });
  });

  it('should redirect to direct URL when available for Google login', () => {
    // Mock env with direct Google URL
    const mockEnv = {
      ...env,
      googleLoginUrl: 'https://accounts.google.com/oauth/authorize?client_id=test',
    };
    
    jest.doMock('@/shared/utils', () => ({
      env: mockEnv,
    }));

    render(
      <TestWrapper>
        <LoginPage />
      </TestWrapper>
    );

    const googleButton = screen.getByLabelText(/loginWithGoogle/i);
    fireEvent.click(googleButton);

    expect(window.location.href).toBe(mockEnv.googleLoginUrl);
  });

  it('should redirect to direct URL when available for Facebook login', () => {
    // Mock env with direct Facebook URL
    const mockEnv = {
      ...env,
      facebookLoginUrl: 'https://www.facebook.com/v18.0/dialog/oauth?client_id=test',
    };
    
    jest.doMock('@/shared/utils', () => ({
      env: mockEnv,
    }));

    render(
      <TestWrapper>
        <LoginPage />
      </TestWrapper>
    );

    const facebookButton = screen.getByLabelText(/loginWithFacebook/i);
    fireEvent.click(facebookButton);

    expect(window.location.href).toBe(mockEnv.facebookLoginUrl);
  });
});
