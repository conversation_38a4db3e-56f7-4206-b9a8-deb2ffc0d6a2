/**
 * Service for audience API
 */

import { apiClient } from '@/shared/api';
import {
  AudienceDetailResponse,
  AudienceListResponse,
  AudienceQueryParams,
  CreateAudienceRequest,
  UpdateAudienceRequest,
  BulkUpdateCustomFieldsDto,
  UpdateAudienceApiResponse,
} from '../types/audience.types';
import { ApiResponseDto } from '@/shared/dto/response/api-response.dto';
import { MarketingCustomFieldResponse } from '../types/custom-field.types';

/**
 * Base URL for audience API
 */
const BASE_URL = '/admin/marketing/audiences';

/**
 * Audience service
 */
export const AudienceService = {
  /**
   * Get audiences with pagination and filtering
   */
  getAudiences: async (params?: AudienceQueryParams): Promise<AudienceListResponse> => {
    return apiClient.get<AudienceListResponse['result']>(BASE_URL, { params });
  },

  /**
   * Get audience by ID
   */
  getAudienceById: async (id: number): Promise<AudienceDetailResponse> => {
    return apiClient.get<AudienceDetailResponse['result']>(`${BASE_URL}/${id}`);
  },

  /**
   * Create audience
   */
  createAudience: async (data: CreateAudienceRequest): Promise<AudienceDetailResponse> => {
    const result = await apiClient.post<AudienceDetailResponse['result']>(BASE_URL, data);
    return result;
  },

  /**
   * Update audience - trả về response với avatar upload URLs
   */
  updateAudience: async (
    id: number,
    data: UpdateAudienceRequest
  ): Promise<UpdateAudienceApiResponse> => {
    console.log('🔄 Updating audience:', { id, data });

    const response = await apiClient.put<UpdateAudienceApiResponse['result']>(`${BASE_URL}/${id}`, data);

    console.log('✅ Update audience response:', response);
    return response;
  },

  /**
   * Delete audience
   */
  deleteAudience: async (id: number): Promise<ApiResponseDto<{ success: boolean }>> => {
    return apiClient.delete<{ success: boolean }>(`${BASE_URL}/${id}`);
  },

  /**
   * Delete multiple audiences
   */
  deleteMultipleAudiences: async (ids: number[]): Promise<ApiResponseDto<{ success: boolean }>> => {
    return apiClient.delete<{ success: boolean }>(BASE_URL, { data: { ids } });
  },

  /**
   * Bulk update custom fields for audience
   */
  bulkUpdateCustomFields: async (
    audienceId: number,
    data: BulkUpdateCustomFieldsDto
  ): Promise<ApiResponseDto<MarketingCustomFieldResponse[]>> => {
    console.log('AudienceService.bulkUpdateCustomFields called with:', { audienceId, data });
    try {
      const result = await apiClient.put<MarketingCustomFieldResponse[]>(
        `/admin/marketing/audiences/${audienceId}/custom-fields`,
        data
      );
      console.log('Bulk update custom fields response:', result);
      return result;
    } catch (error) {
      console.error('Error in bulkUpdateCustomFields:', error);
      throw error;
    }
  },
};
