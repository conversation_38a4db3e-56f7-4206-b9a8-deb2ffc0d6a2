/**
 * Service cho Marketing Custom Fields - ho<PERSON>n toàn độc lập
 */

import { apiClient } from '@/shared/api';
import type { ApiResponseDto, PaginatedResult } from '@/shared/dto/response/api-response.dto';
import {
  CreateMarketingCustomFieldRequest,
  UpdateMarketingCustomFieldRequest,
  MarketingCustomFieldResponse,
} from '../types/custom-field.types';
import { createSafeMarketingCustomFieldParams } from '../utils/api-params.utils';

/**
 * Query parameters cho marketing custom fields
 */
export interface MarketingCustomFieldQueryParams {
  page?: number | undefined;
  limit?: number | undefined;
  search?: string | undefined;
  sortBy?: string | undefined;
  sortDirection?: 'ASC' | 'DESC' | undefined;
}

/**
 * Base URL cho marketing custom field API
 */
const BASE_URL = '/user/marketing/audience-custom-fields';

/**
 * Marketing Custom Field Service - Layer 1: Raw API calls
 */
export const MarketingCustomFieldService = {
  /**
   * <PERSON><PERSON>y danh sách trường tùy chỉnh với phân trang và lọc
   */
  getCustomFields: async (
    params?: MarketingCustomFieldQueryParams
  ): Promise<ApiResponseDto<PaginatedResult<MarketingCustomFieldResponse>>> => {
    try {
      const queryParams: Record<string, unknown> = {
        page: params?.page || 1,
        limit: params?.limit || 10,
      };

      if (params?.search && params.search.trim()) {
        queryParams['search'] = params.search.trim();
      }

      if (params?.sortBy) {
          queryParams['sortBy'] = params.sortBy;
        if (params.sortDirection) {
          queryParams['sortDirection'] = params.sortDirection;
        }
      }

      console.log('Marketing Custom Fields API request:', BASE_URL, queryParams);

      const response = await apiClient.get<PaginatedResult<MarketingCustomFieldResponse>>(
        BASE_URL,
        { params: queryParams }
      );

      console.log('Marketing Custom Fields API response:', response);
      return response;
    } catch (error) {
      console.error('Error in MarketingCustomFieldService.getCustomFields:', error);
      throw error;
    }
  },

  /**
   * Lấy chi tiết trường tùy chỉnh theo ID
   */
  getCustomFieldById: async (
    id: number
  ): Promise<ApiResponseDto<MarketingCustomFieldResponse>> => {
    try {
      const response = await apiClient.get<MarketingCustomFieldResponse>(
        `${BASE_URL}/${id}`
      );
      console.log('Get marketing custom field by ID response:', response);
      return response;
    } catch (error) {
      console.error('Error in MarketingCustomFieldService.getCustomFieldById:', error);
      throw error;
    }
  },

  /**
   * Tạo trường tùy chỉnh mới
   */
  createCustomField: async (
    data: CreateMarketingCustomFieldRequest
  ): Promise<ApiResponseDto<MarketingCustomFieldResponse>> => {
    try {
      console.log('Creating marketing custom field with data:', data);
      
      const response = await apiClient.post<MarketingCustomFieldResponse>(
        BASE_URL,
        data
      );
      
      console.log('Create marketing custom field response:', response);
      return response;
    } catch (error) {
      console.error('Error in MarketingCustomFieldService.createCustomField:', error);
      throw error;
    }
  },

  /**
   * Cập nhật trường tùy chỉnh
   */
  updateCustomField: async (
    id: number,
    data: UpdateMarketingCustomFieldRequest
  ): Promise<ApiResponseDto<MarketingCustomFieldResponse>> => {
    try {
      console.log('Updating marketing custom field:', id, data);
      
      const response = await apiClient.put<MarketingCustomFieldResponse>(
        `${BASE_URL}/${id}`,
        data
      );
      
      console.log('Update marketing custom field response:', response);
      return response;
    } catch (error) {
      console.error('Error in MarketingCustomFieldService.updateCustomField:', error);
      throw error;
    }
  },

  /**
   * Xóa trường tùy chỉnh
   */
  deleteCustomField: async (
    id: number
  ): Promise<ApiResponseDto<{ success: boolean }>> => {
    try {
      console.log('Deleting marketing custom field:', id);
      
      const response = await apiClient.delete<{ success: boolean }>(
        `${BASE_URL}/${id}`
      );
      
      console.log('Delete marketing custom field response:', response);
      return response;
    } catch (error) {
      console.error('Error in MarketingCustomFieldService.deleteCustomField:', error);
      throw error;
    }
  },

  /**
   * Xóa nhiều trường tùy chỉnh
   */
  deleteMultipleCustomFields: async (
    customFieldIds: number[]
  ): Promise<ApiResponseDto<{ success: boolean; deletedCount: number }>> => {
    try {
      console.log('Deleting multiple marketing custom fields:', customFieldIds);
      
      const response = await apiClient.delete<{ success: boolean; deletedCount: number }>(
        `${BASE_URL}/bulk`,
        { data: { customFieldIds } }
      );
      
      console.log('Delete multiple marketing custom fields response:', response);
      return response;
    } catch (error) {
      console.error('Error in MarketingCustomFieldService.deleteMultipleCustomFields:', error);
      throw error;
    }
  },

  /**
   * Lấy config examples cho các dataType
   */
  getConfigExamples: async (): Promise<ApiResponseDto<Record<string, unknown>>> => {
    try {
        const response = await apiClient.get<Record<string, unknown>>(
        `${BASE_URL}/config-examples`
      );
      
      console.log('Get config examples response:', response);
      return response;
    } catch (error) {
      console.error('Error in MarketingCustomFieldService.getConfigExamples:', error);
      throw error;
    }
  },
};

/**
 * Marketing Custom Field Business Service - Layer 2: Business logic
 */
export const MarketingCustomFieldBusinessService = {
  /**
   * Lấy danh sách trường tùy chỉnh với business logic
   */
  getCustomFieldsWithBusinessLogic: async (params?: MarketingCustomFieldQueryParams) => {
    // Use utility function to create safe parameters
    const safeParams = createSafeMarketingCustomFieldParams({
      search: params?.search,
      page: params?.page,
      limit: params?.limit,
      sortBy: params?.sortBy,
      sortDirection: params?.sortDirection,
    });

    return MarketingCustomFieldService.getCustomFields(safeParams);
  },

  /**
   * Tạo trường tùy chỉnh với validation
   */
  createCustomFieldWithValidation: async (data: CreateMarketingCustomFieldRequest) => {
    // Validate fieldKey format
    if (!/^[a-zA-Z0-9_-]+$/.test(data.fieldKey)) {
      throw new Error('Field key must contain only letters, numbers, underscores, and hyphens');
    }

    // Validate displayName
    if (!data.displayName || data.displayName.trim().length === 0) {
      throw new Error('Display name is required');
    }

    // Validate config based on dataType
    if (data.config) {
      switch (data.dataType) {
        case 'text':
          if (data.config.minLength && data.config.maxLength) {
            if (Number(data.config.minLength) > Number(data.config.maxLength)) {
              throw new Error('Min length cannot be greater than max length');
            }
          }
          break;
        case 'number':
          if (data.config.minValue && data.config.maxValue) {
            if (Number(data.config.minValue) > Number(data.config.maxValue)) {
              throw new Error('Min value cannot be greater than max value');
            }
          }
          break;
        case 'select':
          if (!data.config.options || !Array.isArray(data.config.options) || data.config.options.length === 0) {
            throw new Error('Select field must have at least one option');
          }
          break;
      }
    }

    return MarketingCustomFieldService.createCustomField(data);
  },

  /**
   * Cập nhật trường tùy chỉnh với validation
   */
  updateCustomFieldWithValidation: async (id: number, data: UpdateMarketingCustomFieldRequest) => {
    // Validate displayName if provided
    if (data.displayName !== undefined && (!data.displayName || data.displayName.trim().length === 0)) {
      throw new Error('Display name cannot be empty');
    }

    // Validate config based on dataType if provided
    if (data.config && data.dataType) {
      switch (data.dataType) {
        case 'text':
          if (data.config.minLength && data.config.maxLength) {
            if (Number(data.config.minLength) > Number(data.config.maxLength)) {
              throw new Error('Min length cannot be greater than max length');
            }
          }
          break;
        case 'number':
          if (data.config.minValue && data.config.maxValue) {
            if (Number(data.config.minValue) > Number(data.config.maxValue)) {
              throw new Error('Min value cannot be greater than max value');
            }
          }
          break;
        case 'select':
          if (!data.config.options || !Array.isArray(data.config.options) || data.config.options.length === 0) {
            throw new Error('Select field must have at least one option');
          }
          break;
      }
    }

    return MarketingCustomFieldService.updateCustomField(id, data);
  },
};
