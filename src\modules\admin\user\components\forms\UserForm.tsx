import React, { useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Form,
  FormItem,
  Input,
  Button,
  Card,
  Select,
  DatePicker,
  Typography,
  PhoneInputWithCountry,
} from '@/shared/components/common';
import { createUserSchema, updateUserSchema } from '../../schemas/user.schema';
import { UserGender, UserType, User } from '../../types/user.types';
import { useFormErrors } from '@/shared/hooks';

interface UserFormProps {
  /**
   * Callback khi submit form
   */
  onSubmit: (values: Record<string, unknown>) => void;

  /**
   * Callback khi hủy form
   */
  onCancel: () => void;

  /**
   * Dữ liệu người dùng để cập nhật (nếu có)
   */
  userData?: User;

  /**
   * Đang xử lý submit
   */
  isSubmitting?: boolean;
}

/**
 * Form thêm/sửa người dùng
 */
const UserForm: React.FC<UserFormProps> = ({
  onSubmit,
  onCancel,
  userData,
  isSubmitting = false,
}) => {
  const { t } = useTranslation(['user', 'common', 'validation']);
  const { formRef, setFormErrors } = useFormErrors();

  // Xác định schema dựa vào việc có userData hay không
  const schema = userData ? updateUserSchema(t) : createUserSchema(t);

  // Xử lý lỗi API
  useEffect(() => {
    return () => {
      // Reset form errors khi unmount
      setFormErrors({});
    };
  }, [setFormErrors]);

  // Giá trị mặc định cho form
  const defaultValues = userData
    ? {
        fullName: userData.fullName,
        email: userData.email,
        phoneNumber: userData.phoneNumber,
        type: userData.type,
        gender: userData.gender,
        dateOfBirth: userData.dateOfBirth,
        address: userData.address,
      }
    : {
        type: UserType.INDIVIDUAL,
      };

  return (
    <Card title={userData ? t('user:editUser') : t('user:addUser')} className="w-full">
      <Form
        ref={formRef}
        schema={schema}
        onSubmit={onSubmit}
        defaultValues={defaultValues}
        className="space-y-4"
      >
        <FormItem name="fullName" label={t('user:fullName')} required>
          <Input placeholder={t('user:enterFullName')} fullWidth />
        </FormItem>

        <FormItem name="email" label={t('user:email')} required>
          <Input type="email" placeholder={t('user:enterEmail')} fullWidth />
        </FormItem>

        <FormItem name="phoneNumber" label={t('user:phoneNumber')} required>
          <PhoneInputWithCountry
            placeholder={t('user:enterPhoneNumber')}
            fullWidth
            defaultCountry="VN"
          />
        </FormItem>

        {!userData && (
          <>
            <FormItem name="password" label={t('user:password')} required>
              <Input type="password" placeholder={t('user:enterPassword')} fullWidth />
            </FormItem>

            <FormItem name="confirmPassword" label={t('user:confirmPassword')} required>
              <Input type="password" placeholder={t('user:confirmPassword')} fullWidth />
            </FormItem>
          </>
        )}

        <FormItem name="type" label={t('user:accountTypeLabel')} required>
          <Select
            options={[
              { value: UserType.INDIVIDUAL, label: t('user:individual') },
              { value: UserType.BUSINESS, label: t('user:business') },
            ]}
            placeholder={t('user:selectAccountType')}
            fullWidth
          />
        </FormItem>

        <Typography variant="subtitle1" className="font-medium mt-6">
          {t('user:additionalInfo')}
        </Typography>

        <FormItem name="gender" label={t('user:genderLabel')}>
          <Select
            options={[
              { value: UserGender.MALE, label: t('user:male') },
              { value: UserGender.FEMALE, label: t('user:female') },
              { value: UserGender.OTHER, label: t('user:other') },
            ]}
            placeholder={t('user:selectGender')}
            fullWidth
          />
        </FormItem>

        <FormItem name="dateOfBirth" label={t('user:dateOfBirth')}>
          <DatePicker placeholder={t('user:selectDateOfBirth')} fullWidth />
        </FormItem>

        <FormItem name="address" label={t('user:address')}>
          <Input placeholder={t('user:enterAddress')} fullWidth />
        </FormItem>

        <div className="flex justify-end space-x-3 pt-4">
          <Button variant="outline" onClick={onCancel}>
            {t('common:cancel')}
          </Button>
          <Button type="submit" variant="primary" isLoading={isSubmitting}>
            {userData ? t('common:update') : t('common:create')}
          </Button>
        </div>
      </Form>
    </Card>
  );
};

export default UserForm;
