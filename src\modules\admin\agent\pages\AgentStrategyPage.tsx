import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { Button, EmptyState, Loading, Pagination, SlideInForm } from '@/shared/components/common';
import React, { useState, useMemo } from 'react';
import { useTranslation } from 'react-i18next';

import { AdminAgentStrategyGrid, AddAgentStrategyForm } from '../components';
import EditAgentStrategyForm from '../components/EditAgentStrategyForm';
import { useAdminAgentStrategies, useAdminAgentStrategyDetail, useUpdateAdminAgentStrategy } from '../agent-strategy/hooks/useAgentStrategy';
import { AgentStrategyListItem, UpdateAgentStrategyParams } from '../agent-strategy/types/agent-strategy.types';
import { useAdminAgentNotification } from '../hooks/useAdminAgentNotification';
import { useNavigate } from 'react-router-dom';

// Interface không cần thiết vì đã có AgentStrategyListItem

// Interface cho response từ API
// interface ApiAgentStrategyResponse {
//   items: ApiAgentStrategyItem[];
//   meta: {
//     totalItems: number;
//     itemCount: number;
//     itemsPerPage: number;
//     totalPages: number;
//     currentPage: number;
//     hasItems: boolean;
//   };
// }

// // Interface cho dữ liệu từ API
// interface ApiAgentStrategyItem {
//   id: string;
//   name: string;
//   avatar: string | null;
//   systemModelId: string;
//   modelId: string;
// }

// interface ApiAgentStrategyResponse {
//   items: ApiAgentStrategyItem[];
//   meta: {
//     totalItems: number;
//     itemCount: number;
//     itemsPerPage: number;
//     totalPages: number;
//     currentPage: number;
//     hasItems: boolean;
//   };
// }

/**
 * Trang hiển thị danh sách Agent Strategies
 */
const AgentStrategyPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const navigate = useNavigate();
  const {
    createSuccess,
    updateSuccess,
    loadError
  } = useAdminAgentNotification();

  // Filter states
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [search, setSearch] = useState('');

  // Form states
  const [isCreateFormVisible, setIsCreateFormVisible] = useState(false);
  const [isEditFormVisible, setIsEditFormVisible] = useState(false);
  const [editingStrategyId, setEditingStrategyId] = useState<string | null>(null);

  // Query params
  const queryParams = {
    page,
    limit,
    search: search || '',
  };

  // Lấy danh sách agent strategies
  const { data: strategiesResponse, isLoading, error, refetch } = useAdminAgentStrategies(queryParams);

  // Lấy thông tin chi tiết strategy để edit
  const { data: editingStrategy } = useAdminAgentStrategyDetail(editingStrategyId || '');

  // Hook để update strategy
  const updateStrategyMutation = useUpdateAdminAgentStrategy();

  // Debug logs
  console.log('🔍 [AgentStrategyPage] Query params:', queryParams);
  console.log('🔍 [AgentStrategyPage] isLoading:', isLoading);
  console.log('🔍 [AgentStrategyPage] error:', error);
  console.log('🔍 [AgentStrategyPage] strategiesResponse:', strategiesResponse);

  // Mock data for testing UI (temporary) - memoized để tránh re-render
  const mockStrategiesResponse = useMemo(() => ({
    items: [
      {
        id: '1',
        name: 'AI Assistant Strategy',
        avatar: null,
        systemModelId: 'gpt-4',
        modelId: 'gpt-4',
      },
      {
        id: '2',
        name: 'Customer Support Strategy',
        avatar: null,
        systemModelId: 'gpt-3.5-turbo',
        modelId: 'gpt-3.5-turbo',
      },
      {
        id: '3',
        name: 'Sales Strategy',
        avatar: null,
        systemModelId: 'claude-3',
        modelId: 'claude-3',
      }
    ],
    meta: {
      totalItems: 3,
      itemCount: 3,
      itemsPerPage: 10,
      totalPages: 1,
      currentPage: 1,
      hasItems: true,
    }
  }), []);

  const handleSearch = (term: string) => {
    setSearch(term);
    setPage(1); // Reset về trang đầu khi search
  };

  const handleAddStrategy = () => {
    setIsCreateFormVisible(true);
  };

  const handleViewTrash = () => {
    navigate('/admin/agent/strategy/trash');
  };

  // Form handlers
  const hideCreateForm = () => setIsCreateFormVisible(false);

  const handleEditStrategy = (strategyId: string) => {
    setEditingStrategyId(strategyId);
    setIsEditFormVisible(true);
  };

  const hideEditForm = () => {
    setIsEditFormVisible(false);
    setEditingStrategyId(null);
  };

  const handleSubmitUpdateStrategy = async (data: UpdateAgentStrategyParams) => {
    if (!editingStrategyId) return;

    try {
      await updateStrategyMutation.mutateAsync({
        id: editingStrategyId,
        data,
      });
    } catch (error) {
      console.error('Error updating strategy:', error);
      throw error;
    }
  };

  // handleSubmitCreateStrategy không cần thiết vì AddAgentStrategyForm tự handle submit

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset về trang đầu khi thay đổi limit
  };

  // Transform API data to component format
  const strategies: AgentStrategyListItem[] = useMemo(() => {
    console.log('🔍 [AgentStrategyPage] Raw strategiesResponse:', strategiesResponse);

    // Use mock data if no real data from API
    const dataToUse = strategiesResponse || mockStrategiesResponse;

    if (!dataToUse) {
      console.log('🔍 [AgentStrategyPage] No data available');
      return [];
    }

    // AgentStrategyListResponse không có result property, sử dụng trực tiếp
    const responseData = dataToUse;
    console.log('🔍 [AgentStrategyPage] Response data:', responseData);

    if (!responseData?.items) {
      console.log('🔍 [AgentStrategyPage] No items in response data');
      return [];
    }

    const mappedStrategies = responseData.items.map((item: AgentStrategyListItem) => {
      const mapped = {
        id: item.id,
        name: item.name,
        avatar: item.avatar,
        systemModelId: item.systemModelId || '',
        modelId: item.modelId || '',
      };
      console.log('🔍 [AgentStrategyPage] Original item:', item);
      console.log('🔍 [AgentStrategyPage] Mapped strategy:', mapped);
      return mapped;
    });

    console.log('🔍 [AgentStrategyPage] Final strategies:', mappedStrategies);
    return mappedStrategies;
  }, [strategiesResponse, mockStrategiesResponse]);

  const totalItems = useMemo(() => {
    const dataToUse = strategiesResponse || mockStrategiesResponse;
    if (!dataToUse) return 0;
    const responseData = dataToUse;
    return responseData?.meta?.totalItems || 0;
  }, [strategiesResponse, mockStrategiesResponse]);

  // Hiển thị loading
  if (isLoading) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleAddStrategy}
          items={[]}
        />
        <div className="flex justify-center items-center h-64">
          <Loading size="lg" />
        </div>
      </div>
    );
  }

  // Hiển thị lỗi
  if (error) {
    return (
      <div>
        <MenuIconBar
          onSearch={handleSearch}
          onAdd={handleAddStrategy}
          items={[]}
        />
        <EmptyState
          icon="alert-triangle"
          title={t('admin:agent.strategy.list.loadError')}
          description={error.message || t('admin:agent.strategy.list.loadError')}
          actions={
            <Button
              variant="primary"
              onClick={() => {
                loadError(t('admin:agent.strategy.pageTitle', 'Chiến lược Agent'));
                refetch();
              }}
            >
              {t('common:retry', 'Thử lại')}
            </Button>
          }
        />
      </div>
    );
  }

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleAddStrategy}
        items={[]}
        additionalIcons={[
          {
            icon: 'trash',
            tooltip: t('admin:agent.strategy.viewTrash', 'Xem thùng rác'),
            variant: 'default',
            onClick: handleViewTrash,
            className: 'text-gray-600 hover:text-gray-800',
          }
        ]}
      />

       {/* SlideInForm for Create Strategy */}
      <SlideInForm isVisible={isCreateFormVisible}>
        <AddAgentStrategyForm
          onCancel={hideCreateForm}
          onSuccess={() => {
            hideCreateForm();
            createSuccess(t('admin:agent.strategy.pageTitle', 'Chiến lược Agent'));
            refetch(); // Refresh the list
          }}
        />
      </SlideInForm>

      {/* SlideInForm for Edit Strategy */}
      <SlideInForm isVisible={isEditFormVisible}>
        {editingStrategy && (
          <EditAgentStrategyForm
            agentStrategy={editingStrategy}
            onCancel={hideEditForm}
            onSuccess={() => {
              hideEditForm();
              updateSuccess(t('admin:agent.strategy.pageTitle', 'Chiến lược Agent'));
              refetch(); // Refresh the list
            }}
          />
        )}
      </SlideInForm>

      {strategies.length > 0 ? (
        <>
          <AdminAgentStrategyGrid
            strategies={strategies}
            onEditStrategy={handleEditStrategy}
            onSuccess={refetch}
          />

          {/* Pagination */}
          {totalItems > limit && (
            <div className="mt-6 flex justify-end">
              <Pagination
                currentPage={page}
                totalItems={totalItems}
                itemsPerPage={limit}
                onPageChange={handlePageChange}
                onItemsPerPageChange={handleLimitChange}
                itemsPerPageOptions={[10, 20, 50, 100]}
                showItemsPerPageSelector={true}
                showPageInfo={true}
                variant="compact"
                borderless={true}
              />
            </div>
          )}
        </>
      ) : (
        <EmptyState
          icon="strategy"
          title={t('admin:agent.strategy.list.noStrategies')}
          description={
            search
              ? t('admin:agent.strategy.noSearchResults')
              : t('admin:agent.strategy.list.noStrategiesDescription')
          }
          actions={
            <Button
              variant="primary"
              onClick={handleAddStrategy}
            >
              {t('admin:agent.strategy.addStrategy')}
            </Button>
          }
        />
      )}
    </div>
  );
};

export default AgentStrategyPage;
