import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Button, Modal, Tooltip, IconCard } from '@/shared/components/common';
import { useDeleteAdminAgentTemplate, useRestoreAdminAgentTemplate } from '../agent-template/hooks/useAgentTemplate';
import { AgentTemplateListItem } from '../agent-template/types/agent-template.types';
import { useNotification } from '@/shared/hooks/common';

interface AdminAgentTemplateCardProps {
  template: AgentTemplateListItem;
  allTemplates: AgentTemplateListItem[];
  isTrashPage?: boolean;
  onSuccess?: () => void;
}

/**
 * Component hiển thị thông tin agent template dưới dạng card
 */
const AdminAgentTemplateCard: React.FC<AdminAgentTemplateCardProps> = ({
  template,
  allTemplates,
  isTrashPage = false,
  onSuccess,
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const { success, error } = useNotification();
  
  // State cho modal xác nhận xóa
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Mutations
  const deleteTemplateMutation = useDeleteAdminAgentTemplate();
  const restoreTemplateMutation = useRestoreAdminAgentTemplate();

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleDeleteConfirm = async () => {
    try {
      await deleteTemplateMutation.mutateAsync(template.id);
      setShowDeleteModal(false);

      success({
        title: t('admin:agent.template.card.deleteSuccess'),
        message: t('admin:agent.template.card.deleteSuccess'),
      });

      // Call success callback
      if (onSuccess) {
        onSuccess();
      }
    } catch (err) {
      console.error('Error deleting template:', err);
      error({
        title: t('admin:agent.template.card.deleteError'),
        message: t('admin:agent.template.card.deleteError'),
      });
    }
  };

  const handleRestoreClick = async () => {
    try {
      await restoreTemplateMutation.mutateAsync(template.id);

      success({
        title: t('admin:agent.template.card.restoreSuccess', 'Khôi phục thành công'),
        message: t('admin:agent.template.card.restoreSuccess', 'Khôi phục template thành công'),
      });

      // Call success callback
      if (onSuccess) {
        onSuccess();
      }
    } catch (err) {
      console.error('Error restoring template:', err);
      error({
        title: t('admin:agent.template.card.restoreError', 'Lỗi khôi phục'),
        message: t('admin:agent.template.card.restoreError', 'Không thể khôi phục template'),
      });
    }
  };

  const handleDeleteCancel = () => {
    setShowDeleteModal(false);
  };

  return (
    <>
      <Card className="h-full flex flex-col">
        <div className="p-4 flex-1">
          <div className="space-y-4">
            {/* Hàng 1: Avatar và thông tin cơ bản */}
            <div className="flex items-start gap-3">
              <img
                src={template.avatar || '/default-avatar.png'}
                alt={template.name}
                className="w-12 h-12 rounded-full object-cover flex-shrink-0"
              />
              <div className="flex-1 min-w-0">
                <h3 className="font-semibold text-gray-900 dark:text-white truncate">
                  {template.name}
                </h3>
                <p className="text-sm text-gray-500 dark:text-gray-400 truncate">
                  {template.typeName}
                </p>
              </div>
            </div>

            {/* Hàng 2: Thông tin chi tiết */}
            <div className="space-y-2">
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-300">
                  {t('admin:agent.template.card.model', 'Model')}:
                </span>
                <span className="text-sm font-medium text-gray-900 dark:text-white">
                  {template.modelId}
                </span>
              </div>
              
              <div className="flex items-center justify-between">
                <span className="text-sm text-gray-600 dark:text-gray-300">
                  {t('admin:agent.template.card.forSale', 'Hỗ trợ bán')}:
                </span>
                <span className={`text-sm font-medium ${
                  template.isForSale 
                    ? 'text-green-600 dark:text-green-400' 
                    : 'text-red-600 dark:text-red-400'
                }`}>
                  {template.isForSale 
                    ? t('admin:agent.template.card.supported', 'Có hỗ trợ')
                    : t('admin:agent.template.card.notSupported', 'Không hỗ trợ')
                  }
                </span>
              </div>
            </div>

            {/* Hàng 3: Các nút chức năng */}
            <div className="flex justify-end space-x-2">
              {isTrashPage ? (
                // Buttons cho trang trash
                <Tooltip content={t('admin:agent.template.card.restore', 'Khôi phục')} position="top">
                  <IconCard
                    icon="refresh-cw"
                    variant="default"
                    size="md"
                    onClick={handleRestoreClick}
                    className="text-blue-500 hover:text-blue-600"
                  />
                </Tooltip>
              ) : (
                // Buttons cho trang chính
                <Tooltip content={t('admin:agent.template.card.delete', 'Xóa')} position="top">
                  <IconCard
                    icon="trash"
                    variant="default"
                    size="md"
                    onClick={handleDeleteClick}
                    className="text-red-500 hover:text-red-600"
                  />
                </Tooltip>
              )}
            </div>
          </div>
        </div>
      </Card>

      {/* Modal xác nhận xóa */}
      <Modal
        isOpen={showDeleteModal}
        onClose={handleDeleteCancel}
        title={t('admin:agent.template.card.confirmDelete')}
        footer={
          <div className="flex justify-end space-x-3 pt-4 border-t border-border">
            <Button
              variant="outline"
              onClick={handleDeleteCancel}
              disabled={deleteTemplateMutation.isPending}
            >
              {t('common:cancel', 'Hủy')}
            </Button>
            <Button
              variant="danger"
              onClick={handleDeleteConfirm}
              isLoading={deleteTemplateMutation.isPending}
            >
              {t('admin:agent.template.card.delete', 'Xóa')}
            </Button>
          </div>
        }
      >
        <div className="space-y-4">
          <p className="text-gray-600 dark:text-gray-300">
            {t('admin:agent.template.card.deleteMessage')}
          </p>

          <div className="bg-gray-50 dark:bg-gray-800 p-3 rounded-lg">
            <div className="flex items-center gap-3">
              <img
                src={template.avatar || '/default-avatar.png'}
                alt={template.name}
                className="w-10 h-10 rounded-full"
              />
              <div>
                <p className="font-medium text-gray-900 dark:text-white">{template.name}</p>
                <p className="text-sm text-gray-500 dark:text-gray-400">
                  {template.typeName} - {template.modelId}
                </p>
              </div>
            </div>
          </div>
        </div>
      </Modal>
    </>
  );
};

export default AdminAgentTemplateCard;
