import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import { useNavigate } from 'react-router-dom';

import AdminAgentTemplateGrid from '../components/AdminAgentTemplateGrid';

/**
 * Trang hiển thị danh sách Agent Templates đã xóa (trash)
 */
const AgentTemplateDeletePage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const navigate = useNavigate();

  // Filter states
  const [page, setPage] = useState(1);
  const [limit, setLimit] = useState(10);
  const [search, setSearch] = useState('');

  const handleSearch = (term: string) => {
    setSearch(term);
    setPage(1); // Reset về trang đầu khi search
  };

  const handleBackToMain = () => {
    navigate('/admin/agent/template');
  };

  const handlePageChange = (newPage: number) => {
    setPage(newPage);
  };

  const handleLimitChange = (newLimit: number) => {
    setLimit(newLimit);
    setPage(1); // Reset về trang đầu khi thay đổi limit
  };

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={handleBackToMain}
        items={[]}
      />

      <AdminAgentTemplateGrid
        searchTerm={search}
        page={page}
        limit={limit}
        onPageChange={handlePageChange}
        onLimitChange={handleLimitChange}
        isTrashPage={true}
      />
    </div>
  );
};

export default AgentTemplateDeletePage;
