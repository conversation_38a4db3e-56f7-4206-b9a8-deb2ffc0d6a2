import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Typography,
  Switch,
  Button,
  Modal
} from '@/shared/components/common';

interface ConvertField {
  id: string;
  name: string;
  type: string;
  required: boolean;
  description: string;
  enabled: boolean;
}

interface ConvertConfigData {
  fields: ConvertField[];
}

interface ConvertConfigProps {
  initialData?: ConvertConfigData;
  onSave?: (data: ConvertConfigData) => void;
}

const ConvertConfig: React.FC<ConvertConfigProps> = ({
  initialData,
  onSave
}) => {
  const { t } = useTranslation(['admin', 'common']);

  const [configData, setConfigData] = useState<ConvertConfigData>(initialData || {
    fields: [
      {
        id: 'field-1',
        name: 'email',
        description: 'Địa chỉ email người dùng',
        enabled: true,
        type: 'string',
        required: true
      },
      {
        id: 'field-2',
        name: 'phone',
        description: '<PERSON><PERSON> điện thoại người dùng',
        enabled: true,
        type: 'string',
        required: true
      },
      {
        id: 'field-3',
        name: 'name',
        description: 'Họ tên đầy đủ của người dùng',
        enabled: true,
        type: 'string',
        required: true
      },
      {
        id: 'field-4',
        name: 'address',
        description: 'Địa chỉ của người dùng',
        enabled: false,
        type: 'string',
        required: false
      },
      {
        id: 'field-5',
        name: 'birthday',
        description: 'Ngày sinh của người dùng',
        enabled: false,
        type: 'string',
        required: false
      }
    ]
  });

  const [showAddForm, setShowAddForm] = useState(false);
  const [editingField, setEditingField] = useState<ConvertField | null>(null);
  const [formData, setFormData] = useState<Partial<ConvertField>>({});

  const handleToggleField = (id: string) => {
    const updatedFields = configData.fields.map(field => {
      if (field.id === id) {
        return { ...field, enabled: !field.enabled };
      }
      return field;
    });
    const newData = { ...configData, fields: updatedFields };
    setConfigData(newData);
    if (onSave) onSave(newData);
  };

  const handleAddField = () => {
    if (formData.name && formData.type && formData.description) {
      const newField: ConvertField = {
        id: `field-${Date.now()}`,
        name: formData.name,
        type: formData.type,
        description: formData.description,
        required: formData.required || false,
        enabled: true
      };
      
      const newData = {
        ...configData,
        fields: [...configData.fields, newField]
      };
      setConfigData(newData);
      if (onSave) onSave(newData);
      
      setShowAddForm(false);
      setFormData({});
    }
  };

  const handleEditField = () => {
    if (editingField && formData.name && formData.type && formData.description) {
      const updatedFields = configData.fields.map(field => {
        if (field.id === editingField.id) {
          return {
            ...field,
            name: formData.name!,
            type: formData.type!,
            description: formData.description!,
            required: formData.required || false
          };
        }
        return field;
      });
      
      const newData = { ...configData, fields: updatedFields };
      setConfigData(newData);
      if (onSave) onSave(newData);
      
      setEditingField(null);
      setFormData({});
    }
  };

  const handleDeleteField = (id: string) => {
    const updatedFields = configData.fields.filter(field => field.id !== id);
    const newData = { ...configData, fields: updatedFields };
    setConfigData(newData);
    if (onSave) onSave(newData);
  };

  const openEditForm = (field: ConvertField) => {
    setEditingField(field);
    setFormData({
      name: field.name,
      type: field.type,
      description: field.description,
      required: field.required
    });
  };

  const closeForm = () => {
    setShowAddForm(false);
    setEditingField(null);
    setFormData({});
  };

  useEffect(() => {
    if (onSave) onSave(configData);
  }, []);

  return (
    <div>
      <div className="p-6">
          <div className="mb-6">
            <Typography variant="subtitle1" className="mb-2">
              {t('admin:agentTemplate.configureFields')}
            </Typography>
            <Typography variant="body2" className="text-gray-600">
              {t('admin:agentTemplate.configureFieldsDescription')}
            </Typography>
          </div>

          {/* Fields List */}
          <div className="space-y-3">
            {configData.fields.map((field) => (
              <Card key={field.id} className="border border-gray-200">
                <div className="p-4">
                  <div className="flex items-center justify-between">
                    <div className="flex-1">
                      <Typography variant="subtitle2" className="font-semibold mb-1">
                        {field.name}
                      </Typography>
                      <Typography variant="body2" className="text-gray-600 mb-2">
                        {field.description}
                      </Typography>
                      <Typography variant="caption" className="text-gray-500">
                        {t('admin:agentTemplate.type')}: {field.type} | {t('admin:agentTemplate.required')}: {field.required ? t('common:yes') : t('common:no')}
                      </Typography>
                    </div>

                    <div className="flex items-center gap-3">
                      <Switch
                        checked={field.enabled}
                        onChange={() => handleToggleField(field.id)}
                        size="sm"
                      />
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => openEditForm(field)}
                      >
                        ✏️
                      </Button>
                      <Button
                        variant="ghost"
                        size="sm"
                        onClick={() => handleDeleteField(field.id)}
                        className="text-red-600 hover:text-red-700"
                      >
                        🗑️
                      </Button>
                    </div>
                  </div>
                </div>
              </Card>
            ))}
          </div>

          {/* Add Field Button */}
          <div className="mt-6 text-center">
            <Button
              variant="outline"
              onClick={() => setShowAddForm(true)}
            >
              ➕ {t('admin:agentTemplate.addField')}
            </Button>
          </div>
        </div>

      {/* Add/Edit Field Modal */}
      <Modal
        isOpen={showAddForm || !!editingField}
        onClose={closeForm}
        title={editingField ? t('admin:agentTemplate.editField') : t('admin:agentTemplate.addField')}
        size="md"
      >
        <div className="space-y-4">
          <div>
            <label className="block text-sm font-medium mb-2">
              {t('admin:agentTemplate.fieldName')}
            </label>
            <input
              type="text"
              value={formData.name || ''}
              onChange={(e) => setFormData({ ...formData, name: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder={t('admin:agentTemplate.fieldName')}
            />
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">
              {t('admin:agentTemplate.fieldType')}
            </label>
            <select
              value={formData.type || ''}
              onChange={(e) => setFormData({ ...formData, type: e.target.value })}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
            >
              <option value="">Select type</option>
              <option value="string">String</option>
              <option value="number">Number</option>
              <option value="boolean">Boolean</option>
            </select>
          </div>
          <div>
            <label className="block text-sm font-medium mb-2">
              {t('admin:agentTemplate.fieldDescription')}
            </label>
            <textarea
              value={formData.description || ''}
              onChange={(e) => setFormData({ ...formData, description: e.target.value })}
              rows={3}
              className="w-full px-3 py-2 border border-gray-300 rounded-md focus:outline-none focus:ring-2 focus:ring-blue-500"
              placeholder={t('admin:agentTemplate.fieldDescription')}
            />
          </div>
          <div>
            <Switch
              checked={formData.required || false}
              onChange={(checked) => setFormData({ ...formData, required: checked })}
              label={t('admin:agentTemplate.required')}
            />
          </div>
        </div>

        <div className="flex justify-end gap-3 mt-6">
          <Button variant="outline" onClick={closeForm}>
            {t('common:cancel')}
          </Button>
          <Button variant="primary" onClick={editingField ? handleEditField : handleAddField}>
            {editingField ? t('common:update') : t('common:add')}
          </Button>
        </div>
      </Modal>
    </div>
  );
};

export default ConvertConfig;
