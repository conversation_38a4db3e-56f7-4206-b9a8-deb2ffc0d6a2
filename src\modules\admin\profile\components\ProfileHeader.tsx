import React from 'react';
import { <PERSON><PERSON>, Typo<PERSON>, Banner } from '@/shared/components/common';
import { useCurrentUser } from '../hooks/useUser';
import profilebg from '@/shared/assets/images/background/profilebg.jpg';
/**
 * Component hiển thị phần header của trang profile - Simplified version
 * Chỉ hiển thị thông tin cơ bản, không có upload ảnh
 */
const ProfileHeader: React.FC = () => {
  const { data: user } = useCurrentUser();

  // Sử dụng avatar mặc định
  const avatarUrl = '/assets/images/default-avatar.png';

  // Sử dụng ảnh bìa mặc định
  const coverImageUrl = profilebg;

  return (
    <div className="w-full">
      {/* Banner - không có tính năng thay đổi ảnh */}
      <Banner
        backgroundImage={coverImageUrl}
        size="md"
        overlayOpacity={0}
        borderRadius="rounded-t-xl"
      >
        <div className="h-48 md:h-64 lg:h-80"></div>
      </Banner>

      {/* Avatar và thông tin cơ bản */}
      <div className="relative px-4 sm:px-6 lg:px-8 -mt-20 md:-mt-24 lg:-mt-28 flex flex-col items-center">
        {/* Avatar - không có tính năng upload */}
        <div className="relative">
          <Avatar
            src={avatarUrl}
            alt={user?.fullName || 'Admin User'}
            size="xl"
            className="border-4 border-white shadow-lg w-24 h-24 md:w-32 md:h-32 lg:w-36 lg:h-36"
          />
        </div>

        {/* Thông tin cơ bản */}
        <div className="mt-4 text-center">
          <Typography variant="h4" className="font-bold text-center">
            {user?.fullName || 'Admin User'}
          </Typography>
          <Typography variant="body1" color="muted" className="mt-1 text-center">
            {user?.phoneNumber || 'No phone number'}
          </Typography>
        </div>
      </div>
    </div>
  );
};

export default ProfileHeader;
