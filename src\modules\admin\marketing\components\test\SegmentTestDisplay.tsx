import React from 'react';
import { Card, Typography } from '@/shared/components/common';

// Mock data từ API response bạn cung cấp
const mockSegmentData = {
  "code": 200,
  "message": "<PERSON>h sách segment",
  "result": {
    "data": [
      {
        "id": "1",
        "name": "Phân đoạn 1",
        "description": "Phân đoạn để test 1",
        "criteria": {
          "groups": [
            {
              "conditions": [
                {
                  "field": "name",
                  "value": "Nam",
                  "operator": "equals"
                },
                {
                  "field": "email",
                  "value": "<EMAIL>",
                  "operator": "not_equals"
                }
              ],
              "logicalOperator": "AND"
            },
            {
              "conditions": [
                {
                  "field": "phone",
                  "value": "799",
                  "operator": "contains"
                }
              ],
              "logicalOperator": "AND"
            }
          ]
        },
        "createdAt": "1750221689",
        "updatedAt": "1750221689",
        "audienceCount": 667
      }
    ],
    "meta": {
      "total": 1,
      "page": 1,
      "limit": 10,
      "totalPages": 1,
      "hasPreviousPage": false,
      "hasNextPage": false
    }
  }
};

/**
 * Component test để hiển thị segment data
 */
const SegmentTestDisplay: React.FC = () => {
  const segments = mockSegmentData.result.data;

  return (
    <div className="space-y-4">
      <Typography variant="h4" className="text-foreground mb-4">
        🧪 Test Display: Segment Data
      </Typography>
      
      <Card className="p-4">
        <Typography variant="h6" className="mb-4">
          Tổng số phân đoạn: {segments.length} / {mockSegmentData.result.meta.total}
        </Typography>
        
        <div className="space-y-4">
          {segments.map((segment) => (
            <Card key={segment.id} className="p-4 border border-border">
              <div className="space-y-3">
                <div className="flex justify-between items-start">
                  <div>
                    <Typography variant="h6" className="font-semibold text-foreground">
                      {segment.name}
                    </Typography>
                    <Typography variant="body2" className="text-muted">
                      ID: {segment.id}
                    </Typography>
                  </div>
                  <div className="text-right">
                    <Typography variant="h5" className="font-bold text-primary">
                      {segment.audienceCount}
                    </Typography>
                    <Typography variant="caption" className="text-muted">
                      Đối tượng
                    </Typography>
                  </div>
                </div>
                
                <Typography variant="body2" className="text-foreground">
                  {segment.description}
                </Typography>
                
                <div className="space-y-2">
                  <Typography variant="body2" className="font-medium text-foreground">
                    Điều kiện phân đoạn:
                  </Typography>
                  
                  {segment.criteria.groups.map((group, groupIndex) => (
                    <div key={groupIndex} className="ml-4 p-3 bg-muted/20 rounded-lg">
                      <Typography variant="caption" className="font-medium text-muted">
                        Nhóm {groupIndex + 1} ({group.logicalOperator}):
                      </Typography>
                      
                      <div className="mt-2 space-y-1">
                        {group.conditions.map((condition, condIndex) => (
                          <div key={condIndex} className="flex items-center space-x-2 text-sm">
                            <span className="font-medium text-foreground">{condition.field}</span>
                            <span className="px-2 py-1 bg-blue-100 text-blue-800 rounded text-xs">
                              {condition.operator}
                            </span>
                            <span className="text-foreground">"{condition.value}"</span>
                          </div>
                        ))}
                      </div>
                    </div>
                  ))}
                </div>
                
                <div className="flex justify-between text-sm text-muted">
                  <span>Tạo: {new Date(parseInt(segment.createdAt) * 1000).toLocaleString()}</span>
                  <span>Cập nhật: {new Date(parseInt(segment.updatedAt) * 1000).toLocaleString()}</span>
                </div>
              </div>
            </Card>
          ))}
        </div>
      </Card>
      
      <Card className="p-4">
        <Typography variant="h6" className="mb-2">
          📊 Thống kê Operators
        </Typography>
        
        <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
          {Object.entries(
            segments.flatMap(s => s.criteria.groups.flatMap(g => g.conditions))
              .reduce((acc, condition) => {
                acc[condition.operator] = (acc[condition.operator] || 0) + 1;
                return acc;
              }, {} as Record<string, number>)
          ).map(([operator, count]) => (
            <div key={operator} className="text-center p-3 bg-muted/20 rounded-lg">
              <Typography variant="h5" className="font-bold text-foreground">
                {count}
              </Typography>
              <Typography variant="caption" className="text-muted">
                {operator}
              </Typography>
            </div>
          ))}
        </div>
      </Card>
      
      <Card className="p-4">
        <Typography variant="h6" className="mb-2">
          📄 Pagination Info
        </Typography>
        
        <div className="grid grid-cols-2 md:grid-cols-6 gap-4 text-center">
          <div>
            <Typography variant="h6" className="font-bold text-foreground">
              {mockSegmentData.result.meta.page}
            </Typography>
            <Typography variant="caption" className="text-muted">Current Page</Typography>
          </div>
          <div>
            <Typography variant="h6" className="font-bold text-foreground">
              {mockSegmentData.result.meta.totalPages}
            </Typography>
            <Typography variant="caption" className="text-muted">Total Pages</Typography>
          </div>
          <div>
            <Typography variant="h6" className="font-bold text-foreground">
              {mockSegmentData.result.meta.limit}
            </Typography>
            <Typography variant="caption" className="text-muted">Limit</Typography>
          </div>
          <div>
            <Typography variant="h6" className="font-bold text-foreground">
              {mockSegmentData.result.meta.total}
            </Typography>
            <Typography variant="caption" className="text-muted">Total Items</Typography>
          </div>
          <div>
            <Typography variant="h6" className={`font-bold ${mockSegmentData.result.meta.hasPreviousPage ? 'text-green-600' : 'text-red-600'}`}>
              {mockSegmentData.result.meta.hasPreviousPage ? 'Yes' : 'No'}
            </Typography>
            <Typography variant="caption" className="text-muted">Has Previous</Typography>
          </div>
          <div>
            <Typography variant="h6" className={`font-bold ${mockSegmentData.result.meta.hasNextPage ? 'text-green-600' : 'text-red-600'}`}>
              {mockSegmentData.result.meta.hasNextPage ? 'Yes' : 'No'}
            </Typography>
            <Typography variant="caption" className="text-muted">Has Next</Typography>
          </div>
        </div>
      </Card>
    </div>
  );
};

export default SegmentTestDisplay;
