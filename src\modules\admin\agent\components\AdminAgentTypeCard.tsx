import React, { useState } from 'react';
import { Card, IconCard, Tooltip, Chip, Typography } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { TypeAgentListItem, AgentTypeStatusEnum } from '../agent-type/types/type-agent.types';
import { useRestoreAdminTypeAgent } from '../agent-type/hooks/useTypeAgent';
import { useAdminAgentNotification } from '../hooks/useAdminAgentNotification';
import DeleteTypeAgentModal from './DeleteTypeAgentModal';

interface AdminAgentTypeCardProps {
  type: TypeAgentListItem;
  /** Danh sách tất cả types */
  allTypes?: TypeAgentListItem[];
  /** Callback khi click edit */
  onEditType?: (typeId: number) => void;
  /** Có phải trang trash không */
  isTrashPage?: boolean;
  /** Callback khi thành công */
  onSuccess?: () => void;
}

/**
 * Component hiển thị thông tin của một Agent Type
 */
const AdminAgentTypeCard: React.FC<AdminAgentTypeCardProps> = ({
  type,
  onEditType,
  isTrashPage = false,
  onSuccess
}) => {
   const { t } = useTranslation(['admin', 'common']);

   // Debug log
   console.log('🔍 [AdminAgentTypeCard] Rendering with type:', type);
  const {
    restoreSuccess,
    restoreError
  } = useAdminAgentNotification();
  const [showDeleteModal, setShowDeleteModal] = useState(false);

  // Hooks cho API calls
  const restoreTypeMutation = useRestoreAdminTypeAgent();

  const handleEditType = () => {
    if (onEditType) {
      onEditType(type.id);
    }
  };

  const handleDeleteClick = () => {
    setShowDeleteModal(true);
  };

  const handleRestoreClick = async () => {
    try {
      await restoreTypeMutation.mutateAsync(type.id);

      restoreSuccess(t('admin:agent.type.pageTitle', 'Loại Agent'));

      onSuccess?.();
    } catch (err) {
      console.error('Error restoring type agent:', err);
      restoreError(
        t('admin:agent.type.pageTitle', 'Loại Agent'),
        err instanceof Error ? err.message : undefined
      );
    }
  };

  const handleDeleteModalClose = () => {
    setShowDeleteModal(false);
  };

  const handleDeleteSuccess = () => {
    setShowDeleteModal(false);
    onSuccess?.();
  };

  // Xác định variant cho status chip
  const getStatusVariant = (
    status: AgentTypeStatusEnum
  ): 'primary' | 'success' | 'warning' | 'danger' | 'info' => {
    switch (status) {
      case AgentTypeStatusEnum.APPROVED:
        return 'success';
      case AgentTypeStatusEnum.DRAFT:
        return 'warning';
      default:
        return 'primary';
    }
  };

 

  return (
    <>
      <Card
        className="h-full overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300"
        variant="elevated"
      >
        <div className="p-4">
          <div className="flex flex-col space-y-1">
            {/* Hàng 1: Icon, tên, và status */}
            <div className="flex items-center gap-3 overflow-hidden">
              {/* Icon */}
              <div className="flex-shrink-0">
                <div className="w-16 h-16 sm:w-20 sm:h-20 bg-gradient-to-br from-blue-500 to-purple-600 rounded-lg flex items-center justify-center">
                  <IconCard
                    icon="settings"
                    variant="default"
                    size="lg"
                    className="text-white"
                  />
                </div>
              </div>

              {/* Thông tin type: tên và status */}
              <div className="flex flex-col min-w-0 flex-grow">
                <div className="flex flex-col sm:flex-row sm:items-center sm:justify-between gap-1 sm:gap-2">
                  <div className="min-w-0">
                    <Typography variant="h5" className="font-semibold text-gray-900 dark:text-white truncate">
                      {type.name}
                    </Typography>
                    <div className="text-xs text-gray-500 dark:text-gray-400">
                      {type.description}
                    </div>
                  </div>
                  <div className="flex-shrink-0 mt-1 sm:mt-0">
                    <Chip
                      variant={getStatusVariant(type.status)}
                      size="sm"
                      className="font-normal"
                    >
                      {type.status === AgentTypeStatusEnum.APPROVED 
                        ? t('admin:agent.form.approved') 
                        : t('admin:agent.form.draft')
                      }
                    </Chip>
                  </div>
                </div>
              </div>
            </div>

           

            {/* Hàng 3: Các nút chức năng */}
            <div className="flex justify-end space-x-2">
              {isTrashPage ? (
                // Buttons cho trang trash
                <Tooltip content={t('admin:agent.trash.restoreAgent', 'Khôi phục loại agent')} position="top">
                  <IconCard
                    icon="refresh-cw"
                    variant="default"
                    size="md"
                    onClick={handleRestoreClick}
                    className="text-green-500 hover:text-green-600"
                    disabled={restoreTypeMutation.isPending}
                  />
                </Tooltip>
              ) : (
                // Buttons cho trang chính
                <>
                  <Tooltip content={t('admin:agent.type.card.edit')} position="top">
                    <IconCard
                      icon="edit"
                      variant="default"
                      size="md"
                      onClick={handleEditType}
                    />
                  </Tooltip>
                  <Tooltip content={t('admin:agent.type.card.delete')} position="top">
                    <IconCard
                      icon="trash"
                      variant="default"
                      size="md"
                      onClick={handleDeleteClick}
                      className="text-red-500 hover:text-red-600"
                    />
                  </Tooltip>
                </>
              )}
            </div>
          </div>
        </div>
      </Card>

      {/* Modal xóa với tùy chọn migration */}
      {!isTrashPage && (
        <DeleteTypeAgentModal
          isOpen={showDeleteModal}
          onClose={handleDeleteModalClose}
          typeAgent={type}
          onSuccess={handleDeleteSuccess}
        />
      )}
    </>
  );
};

export default AdminAgentTypeCard;
