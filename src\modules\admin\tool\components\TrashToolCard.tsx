import React from 'react';
import { Card, Chip, IconCard, Tooltip } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import { ToolListItem, ToolStatus, AccessType } from '../types/tool.types';
import { formatDate } from '@/shared/utils/format';

interface TrashToolCardProps {
  tool: ToolListItem;
  onRestore?: (tool: ToolListItem) => void;
  className?: string;
}

const TrashToolCard: React.FC<TrashToolCardProps> = ({ tool, onRestore, className = '' }) => {
  const { t } = useTranslation(['admin-tool', 'common']);

  // Status chip variants
  const getStatusVariant = (status: ToolStatus) => {
    switch (status) {
      case ToolStatus.APPROVED:
        return 'success';
      case ToolStatus.DRAFT:
        return 'warning';
      case ToolStatus.DEPRECATED:
        return 'danger';
      default:
        return 'default';
    }
  };

  // Status labels
  const getStatusLabel = (status: ToolStatus) => {
    switch (status) {
      case ToolStatus.DRAFT:
        return t('admin-tool:status.draft', 'Bản nháp');
      case ToolStatus.APPROVED:
        return t('admin-tool:status.approved', 'Đã duyệt');
      case ToolStatus.DEPRECATED:
        return t('admin-tool:status.deprecated', 'Không dùng');
      default:
        return status;
    }
  };

  // Access type labels
  const getAccessTypeLabel = (accessType: AccessType) => {
    switch (accessType) {
      case AccessType.PUBLIC:
        return t('admin-tool:accessType.public', 'Công khai');
      case AccessType.PRIVATE:
        return t('admin-tool:accessType.private', 'Riêng tư');
      case AccessType.RESTRICTED:
        return t('admin-tool:accessType.restricted', 'Hạn chế');
      default:
        return accessType;
    }
  };

  const handleRestore = () => {
    if (onRestore) {
      onRestore(tool);
    }
  };

  return (
    <Card
      className={`h-full overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300  ${className}`}
      variant="elevated"
    >
      <div className="p-4">
        {/* Header: Icon + Tên + Status */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-3 min-w-0 flex-1">
            {/* Icon với màu đỏ để biểu thị đã xóa */}
            <div className="w-10 h-10 flex-shrink-0 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
              <span className="text-lg">🛠️</span>
            </div>

            {/* Tên tool */}
            <div className="min-w-0 flex-1">
              <h3 className="font-medium text-base text-gray-900 dark:text-white truncate">
                <p>{tool?.name ?? 'Không có tên'}</p>
              </h3>
              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">ID: {tool.id}</p>
            </div>
          </div>

          {/* Status chip */}
          <Chip
            variant={getStatusVariant(tool.status)}
            size="sm"
            className="text-xs font-medium flex-shrink-0"
          >
            {getStatusLabel(tool.status)}
          </Chip>
        </div>

        {/* Mô tả */}
        <div className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2 leading-relaxed">
          {tool.description || t('admin-tool:noDescription', 'Không có mô tả')}
        </div>

        {/* Thông tin xóa */}
        <div className="space-y-2 mb-4">
          {tool.deletedAt && (
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500 dark:text-gray-400">
                {t('admin-tool:trash.deletedAt', 'Ngày xóa')}:
              </span>
              <span className="text-gray-700 dark:text-gray-300 font-medium">
                {formatDate(
                  typeof tool.deletedAt === 'string' ? parseInt(tool.deletedAt, 10) : tool.deletedAt
                )}
              </span>
            </div>
          )}

          {tool.deletedBy && (tool.deletedBy.name || tool.deletedBy.email) && (
            <div className="flex items-center justify-between text-xs">
              <span className="text-gray-500 dark:text-gray-400">
                {t('admin-tool:trash.deletedBy', 'Người xóa')}:
              </span>
              <div className="text-right">
                <div className="text-gray-700 dark:text-gray-300 font-medium">
                  {tool.deletedBy.name || 'N/A'}
                </div>
                {tool.deletedBy.email && (
                  <div className="text-gray-500 dark:text-gray-400">{tool.deletedBy.email}</div>
                )}
              </div>
            </div>
          )}
        </div>

        {/* Footer: Access Type + Actions */}
        <div className="flex items-center justify-between pt-3 border-t border-gray-200 dark:border-gray-700">
          {/* Access type info */}
          <div className="text-xs text-gray-500 dark:text-gray-400">
            {getAccessTypeLabel(tool.accessType)}
          </div>

          {/* Action button */}
          <div className="flex items-center space-x-2">
            <Tooltip content={t('admin-tool:trash.rollback', 'Khôi phục')} position="top">
              <IconCard icon="refresh" variant="primary" size="sm" onClick={handleRestore} />
            </Tooltip>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default TrashToolCard;
