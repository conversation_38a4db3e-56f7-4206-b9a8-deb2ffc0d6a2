/**
 * Service for segment API
 */

import { apiClient } from '@/shared/api';
import {
  CreateSegmentRequest,
  SegmentDetailResponse,
  SegmentListApiResponse,
  SegmentQueryParams,
  SegmentStatsResponse,
  UpdateSegmentRequest,
  BulkDeleteSegmentsResponse,
} from '../types/segment.types';

/**
 * Base URL for segment API
 */
const BASE_URL = '/admin/marketing/segments';

/**
 * Segment service
 */
export const SegmentService = {
  /**
   * Get segments with pagination and filtering
   */
  getSegments: async (params: SegmentQueryParams): Promise<SegmentListApiResponse> => {
    // API trả về { code, message, result: { data: [...], meta: {...} } }
    const response = await apiClient.get<SegmentListApiResponse['result']>(BASE_URL, { params });
    return response;
  },

  /**
   * Get segment by ID
   */
  getSegmentById: async (id: string | number): Promise<SegmentDetailResponse> => {
    return apiClient.get<SegmentDetailResponse['result']>(`${BASE_URL}/${id}`);
  },

  /**
   * Create segment
   */
  createSegment: async (data: CreateSegmentRequest): Promise<SegmentDetailResponse> => {
    return apiClient.post<SegmentDetailResponse['result']>(BASE_URL, data);
  },

  /**
   * Update segment
   */
  updateSegment: async (id: string | number, data: UpdateSegmentRequest): Promise<SegmentDetailResponse> => {
    return apiClient.put<SegmentDetailResponse['result']>(`${BASE_URL}/${id}`, data);
  },

  /**
   * Delete segment
   */
  deleteSegment: async (id: string | number): Promise<{ success: boolean }> => {
    await apiClient.delete<{ success: boolean }>(`${BASE_URL}/${id}`);
    return { success: true };
  },

  /**
   * Delete multiple segments - API mới
   */
  deleteMultipleSegments: async (ids: (string | number)[]): Promise<BulkDeleteSegmentsResponse> => {
    const response = await apiClient.delete<BulkDeleteSegmentsResponse['result']>(`${BASE_URL}`, {
      data: { ids }
    });
    return response;
  },

  /**
   * Get segment stats
   */
  getSegmentStats: async (id: string | number): Promise<SegmentStatsResponse> => {
    return apiClient.get<SegmentStatsResponse['result']>(`${BASE_URL}/${id}/stats`);
  },


};
