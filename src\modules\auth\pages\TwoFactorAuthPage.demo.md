# TwoFactorAuthPage Demo

## Chức năng đã implement

### 1. API select-2fa-method
- **Endpoint**: `POST /v1/auth/select-2fa-method`
- **Request**: 
  ```json
  {
    "token": "eyJhbGciOiJIUzI1NiIsInR5cCI6IkpXVCJ9...",
    "platform": "EMAIL" // hoặc "SMS"
  }
  ```
- **Response**:
  ```json
  {
    "code": 200,
    "message": "Success",
    "result": {
      "message": "OTP sent successfully",
      "expiresAt": 1750923684000
    }
  }
  ```

### 2. Flow hoạt động

#### Khi user chọn EMAIL hoặc SMS:
1. Gọi API `POST /v1/auth/select-2fa-method` với token và platform
2. Hiển thị loading state
3. <PERSON><PERSON>u thành công: chuyển sang màn hình nhập OTP
4. Nếu lỗi: hiển thị thông báo lỗi

#### Khi user nhấn "Gửi lại mã":
1. G<PERSON>i lại API `POST /v1/auth/select-2fa-method` với cùng token và platform
2. Hiển thị loading state cho button "Gửi lại mã"
3. Reset OTP input và countdown timer
4. Cập nhật thời gian hết hạn mới

### 3. UI/UX Improvements

#### Loading States:
- Loading khi đang chọn phương thức
- Loading khi đang gửi lại OTP
- Disable tất cả interactions khi đang loading

#### Error Handling:
- Hiển thị lỗi chi tiết từ API
- Fallback messages cho các trường hợp lỗi

#### Visual Feedback:
- Alert thông báo khi đang chọn phương thức
- Button states (loading, disabled)
- Opacity cho các method cards khi loading

### 4. Type Safety

```typescript
// Request type
interface SelectTwoFactorMethodRequest {
  token: string;
  platform: 'EMAIL' | 'SMS';
}

// Response type
interface SelectTwoFactorMethodResponse {
  message: string;
  expiresAt?: number;
}

// API response wrapper
ApiResponseDto<SelectTwoFactorMethodResponse>
```

### 5. Code Structure

```
src/modules/auth/
├── types/auth.types.ts              # Added SelectTwoFactorMethodRequest/Response
├── services/auth.service.ts         # Added selectTwoFactorMethod method
├── hooks/useAuthQuery.ts           # Added useSelectTwoFactorMethod hook
└── pages/TwoFactorAuthPage.tsx     # Updated with new functionality
```

## Test Cases

### 1. Happy Path - EMAIL
1. User vào trang /auth/two-factor
2. User nhấn vào "Xác thực qua Email"
3. API được gọi với platform: "EMAIL"
4. Chuyển sang màn hình nhập OTP
5. User nhập OTP và xác thực thành công

### 2. Happy Path - SMS
1. User vào trang /auth/two-factor
2. User nhấn vào "Xác thực qua SMS"
3. API được gọi với platform: "SMS"
4. Chuyển sang màn hình nhập OTP
5. User nhập OTP và xác thực thành công

### 3. Resend OTP
1. User đã chọn phương thức và ở màn hình nhập OTP
2. User nhấn "Gửi lại mã"
3. API được gọi lại với cùng platform
4. OTP input được reset
5. Countdown timer được cập nhật

### 4. Error Cases
1. API trả về lỗi khi chọn phương thức
2. API trả về lỗi khi gửi lại OTP
3. Token hết hạn
4. Network error

## Tuân thủ quy tắc RedAI

✅ Sử dụng Typography thay vì HTML tags
✅ Sử dụng Button với các variant phù hợp
✅ Sử dụng useTranslation với cú pháp colon
✅ TypeScript strict với interfaces rõ ràng
✅ API 3-layer pattern (API → Service → Hook)
✅ Error handling với proper types
✅ Responsive design và theme support
