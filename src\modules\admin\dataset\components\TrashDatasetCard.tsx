import React from 'react';
import { Card, IconCard, Tooltip } from '@/shared/components/common';
import { useTranslation } from 'react-i18next';
import {
  UserDataFineTuneResponseDto,
  ProviderFineTuneEnum,
} from '../user-data-fine-tune/types/user-data-fine-tune.types';

interface TrashDatasetCardProps {
  dataset: UserDataFineTuneResponseDto & { provider?: ProviderFineTuneEnum };
  onRestore?: (dataset: UserDataFineTuneResponseDto) => void;
  className?: string;
}

const TrashDatasetCard: React.FC<TrashDatasetCardProps> = ({
  dataset,
  onRestore,
  className = '',
}) => {
  const { t } = useTranslation(['admin-dataset', 'common']);

  const handleRestore = () => {
    if (onRestore) {
      onRestore(dataset);
    }
  };

  return (
    <Card
      className={`h-full overflow-hidden shadow-md hover:shadow-lg transition-shadow duration-300  ${className}`}
      variant="default"
    >
      <div className="p-4">
        {/* Header: Icon + Tên + Status */}
        <div className="flex items-center justify-between mb-3">
          <div className="flex items-center gap-3 min-w-0 flex-1">
            {/* Icon với màu đỏ để biểu thị đã xóa */}
            <div className="w-10 h-10 flex-shrink-0 bg-red-100 dark:bg-red-900/20 rounded-lg flex items-center justify-center">
              <span className="text-lg">🗑️</span>
            </div>

            {/* Tên dataset */}
            <div className="min-w-0 flex-1">
              <h3 className="font-medium text-base text-gray-900 dark:text-white truncate">
                {dataset.name}
              </h3>
              <p className="text-xs text-gray-500 dark:text-gray-400 truncate">ID: {dataset.id}</p>
            </div>
          </div>
        </div>

        {/* Mô tả */}
        <div className="text-sm text-gray-600 dark:text-gray-400 mb-4 line-clamp-2 leading-relaxed">
          {dataset.description || t('admin-dataset:noDescription', 'Không có mô tả')}
        </div>

        {/* Footer: Provider + Actions */}
        <div className="flex items-center justify-end pt-3  dark:border-gray-700">
          {/* Action button */}
          <div className="flex items-center space-x-2">
            <Tooltip content={t('admin-dataset:trash.restore', 'Khôi phục')} position="top">
              <IconCard icon="refresh" variant="primary" size="sm" onClick={handleRestore} />
            </Tooltip>
          </div>
        </div>
      </div>
    </Card>
  );
};

export default TrashDatasetCard;
