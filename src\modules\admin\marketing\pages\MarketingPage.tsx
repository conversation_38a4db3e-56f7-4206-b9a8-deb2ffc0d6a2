import React, { useEffect, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { ResponsiveGrid } from '@/shared/components/common';
import ModuleCard from '@/modules/components/card/ModuleCard';
import { useCustomFields } from '../hooks';

/**
 * Marketing Dashboard Page
 * Displays an overview of marketing module features
 */
const MarketingPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);

  // State to store counts
  const [, setCustomFieldCount] = useState<number>(0);

  // Call API to get custom fields list
  const { data: fieldData } = useCustomFields({
    page: 1,
    limit: 1,
  });

  // Update counts when data is available
  useEffect(() => {
    if (fieldData?.meta) {
      setCustomFieldCount(fieldData.meta.totalItems);
    }
  }, [fieldData]);

  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Audience Card */}
        <ModuleCard
          title={t('admin:marketing.audience.title', 'Quản lý đối tượng')}
          description={t('admin:marketing.audience.description', 'Tạo và quản lý đối tượng khách hàng')}
          icon="users"
          linkTo="/admin/marketing/audience"
        />

        {/* Segment Card */}
        <ModuleCard
          title={t('admin:marketing.segments.title', 'Quản lý phân đoạn')}
          description={t('admin:marketing.segments.description', 'Tạo và quản lý các phân đoạn khách hàng')}
          icon="filter"
          linkTo="/admin/marketing/segments"
        />

        {/* Custom Fields Card */}
        <ModuleCard
          title={t('admin:marketing.customFields.title', 'Trường tùy chỉnh')}
          description={t('admin:marketing.customFields.description', 'Tạo và quản lý các trường dữ liệu tùy chỉnh')}
          icon="database"
          linkTo="/admin/marketing/custom-fields"
        />

        {/* Tags Card */}
        <ModuleCard
          title={t('admin:marketing.tags.title', 'Quản lý Tag')}
          description={t('admin:marketing.tags.description', 'Tạo và quản lý các tag cho khách hàng')}
          icon="tag"
          linkTo="/admin/marketing/tags"
        />
      </ResponsiveGrid>
    </div>
  );
};

export default MarketingPage;
