import React, { useState, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ActionMenu, Tooltip, ConfirmDeleteModal } from '@/shared/components/common';
import { ActionMenuItem } from '@/shared/components/common/ActionMenu';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import {
  useMarketingCustomFields,
  useDeleteMarketingCustomField,
  useDeleteMultipleMarketingCustomFields,
  useCreateMarketingCustomField,
  useUpdateMarketingCustomField,
} from '../hooks/useMarketingCustomFieldQuery';
import { MarketingCustomFieldQueryParams } from '../services/marketing-custom-field.service';
import {
  MarketingCustomFieldResponse,
  CreateMarketingCustomFieldRequest,
  UpdateMarketingCustomFieldRequest,
} from '../types/custom-field.types';
import { SortDirection } from '@/shared/dto/request/query.dto';
import { ActiveFilters } from '@/modules/components/filters';
import { useDataTable, useDataTableConfig } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import MarketingCustomFieldForm from '../components/forms/MarketingCustomFieldForm';

/**
 * Trang quản lý Marketing Custom Fields - hoàn toàn độc lập
 */
const CustomFieldsPage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);

  // State cho form và selection
  const [selectedCustomField, setSelectedCustomField] =
    useState<MarketingCustomFieldResponse | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [customFieldToDelete, setCustomFieldToDelete] =
    useState<MarketingCustomFieldResponse | null>(null);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // Slide form hooks
  const {
    isVisible: isCreateVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  const {
    isVisible: isEditVisible,
    showForm: showEditForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Mutations
  const createCustomFieldMutation = useCreateMarketingCustomField();
  const updateCustomFieldMutation = useUpdateMarketingCustomField();
  const deleteCustomFieldMutation = useDeleteMarketingCustomField();
  const deleteMultipleCustomFieldsMutation = useDeleteMultipleMarketingCustomFields();

  // Create query params function
  const createQueryParams = useCallback(
    (params: {
      page: number;
      pageSize: number;
      searchTerm: string;
      sortBy: string | null;
      sortDirection: SortDirection | null;
      filterValue: string | number | boolean | undefined;
      dateRange: [Date | null, Date | null];
    }): MarketingCustomFieldQueryParams => {
      const queryParams: MarketingCustomFieldQueryParams = {
        page: params.page,
        limit: params.pageSize,
        search: params.searchTerm || undefined,
        sortBy: params.sortBy || undefined,
        sortDirection:
          params.sortDirection === SortDirection.ASC
            ? 'ASC'
            : params.sortDirection === SortDirection.DESC
              ? 'DESC'
              : undefined,
      };

      return queryParams;
    },
    []
  );

  // Data table configuration
  const dataTableConfig = useDataTableConfig({
    columns: [
      {
        title: t('marketing:customField.form.fieldKeyLabel', 'Trường định danh'),
        dataIndex: 'fieldKey',
        key: 'fieldKey',
        sortable: true,
        width: 180,
        render: (fieldKey: unknown) => (
          <span className="font-mono text-sm bg-gray-100 dark:bg-gray-800 px-2 py-1 rounded">
            {String(fieldKey)}
          </span>
        ),
      },
      {
        title: t('marketing:customField.form.displayNameLabel', 'Tên hiển thị'),
        dataIndex: 'displayName',
        key: 'displayName',
        sortable: true,
        width: 200,
      },
      {
        title: t('marketing:customField.dataType', 'Loại dữ liệu'),
        dataIndex: 'dataType',
        key: 'dataType',
        sortable: true,
        width: 120,
        render: (dataType: unknown) => {
          const typeStr = String(dataType || '');
          const typeMap: Record<string, { label: string; color: string }> = {
            string: { label: 'Text', color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' },
            text: { label: 'Text', color: 'bg-blue-100 text-blue-800 dark:bg-blue-900 dark:text-blue-200' },
            integer: { label: 'Number', color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' },
            number: { label: 'Number', color: 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200' },
            boolean: { label: 'Boolean', color: 'bg-purple-100 text-purple-800 dark:bg-purple-900 dark:text-purple-200' },
            date: { label: 'Date', color: 'bg-orange-100 text-orange-800 dark:bg-orange-900 dark:text-orange-200' },
            select: { label: 'Select', color: 'bg-indigo-100 text-indigo-800 dark:bg-indigo-900 dark:text-indigo-200' },
            object: { label: 'Object', color: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200' },
          };
          const typeInfo = typeMap[typeStr] || { label: typeStr, color: 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200' };
          return (
            <span className={`inline-block px-2 py-1 text-xs rounded font-medium ${typeInfo.color}`}>
              {typeInfo.label}
            </span>
          );
        },
      },
      {
        title: t('marketing:customField.form.description', 'Mô tả'),
        dataIndex: 'description',
        key: 'description',
        width: 250,
        render: (description: unknown) => {
          const descStr = String(description || '');
          return (
            <Tooltip
              content={descStr || t('marketing:customField.noDescription', 'Không có mô tả')}
            >
              <span className="truncate max-w-xs block">{descStr || '-'}</span>
            </Tooltip>
          );
        },
      },
      {
        title: t('marketing:customField.form.tags', 'Tags'),
        dataIndex: 'tags',
        key: 'tags',
        width: 200,
        render: (tags: unknown) => {
          const tagArray = Array.isArray(tags) ? tags : [];
          if (tagArray.length === 0) {
            return <span className="text-gray-400"></span>;
          }
          return (
            <div className="flex flex-wrap gap-1">
              {tagArray.slice(0, 3).map((tag: string, index: number) => (
                <span
                  key={index}
                  className="inline-block px-2 py-1 text-xs bg-blue-100 dark:bg-blue-900 text-blue-800 dark:text-blue-200 rounded"
                >
                  {tag}
                </span>
              ))}
              {tagArray.length > 3 && (
                <span className="text-xs text-gray-500">+{tagArray.length - 3}</span>
              )}
            </div>
          );
        },
      },
      {
        title: t('common:actions', 'Thao tác'),
        key: 'actions',
        width: 100,
        render: (_: unknown, record: unknown) => {
          const customField = record as MarketingCustomFieldResponse;
          const actionItems: ActionMenuItem[] = [
            {
              id: 'edit',
              label: t('common:edit', 'Chỉnh sửa'),
              icon: 'edit',
              onClick: () => handleEditCustomField(customField),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm hành động')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={false}
              preferRight={true}
            />
          );
        },
      },
    ],
    createQueryParams,
  });

  const dataTable = useDataTable(dataTableConfig);

  // Active filters
  const activeFilters = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: dataTable.tableData.handleSortChange,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {},
    t,
  });

  // Fetch data using dataTable query params
  const { data: customFieldsData, isLoading } = useMarketingCustomFields(dataTable.queryParams);

  // Type guard để đảm bảo customFieldsData có đúng structure
  const tableData = customFieldsData as
    | {
        items?: MarketingCustomFieldResponse[];
        meta?: { currentPage: number; itemsPerPage: number; totalItems: number };
      }
    | undefined;

  // Handlers
  const handleCreateCustomField = useCallback(() => {
    setSelectedCustomField(null);
    showCreateForm();
  }, [showCreateForm]);

  const handleEditCustomField = useCallback(
    (customField: MarketingCustomFieldResponse) => {
      setSelectedCustomField(customField);
      showEditForm();
    },
    [showEditForm]
  );

  const handleConfirmDelete = useCallback(async () => {
    if (!customFieldToDelete || !customFieldToDelete.id) return;

    try {
      await deleteCustomFieldMutation.mutateAsync(customFieldToDelete.id);
      setShowDeleteConfirm(false);
      setCustomFieldToDelete(null);
    } catch (error) {
      console.error('Error deleting custom field:', error);
    }
  }, [customFieldToDelete, deleteCustomFieldMutation]);

  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setCustomFieldToDelete(null);
  }, []);

  // Bulk delete handlers
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedRowKeys.length === 0) return;
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys]);

  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      const ids = selectedRowKeys.map(key => Number(key));

      // Gọi API xóa nhiều custom fields với endpoint mới
      const response = await deleteMultipleCustomFieldsMutation.mutateAsync(ids);

      setShowBulkDeleteConfirm(false);
      setSelectedRowKeys([]);

      // Log response để debug
      console.log('✅ Bulk delete custom fields response:', response);

    } catch (error) {
      console.error('❌ Error bulk deleting custom fields:', error);
    }
  }, [selectedRowKeys, deleteMultipleCustomFieldsMutation]);

  // Form handlers
  const handleCreateSubmit = useCallback(
    async (data: CreateMarketingCustomFieldRequest | UpdateMarketingCustomFieldRequest) => {
      try {
        await createCustomFieldMutation.mutateAsync(data as CreateMarketingCustomFieldRequest);
        hideCreateForm();
      } catch (error) {
        console.error('Error creating custom field:', error);
        throw error; // Re-throw để form xử lý
      }
    },
    [createCustomFieldMutation, hideCreateForm]
  );

  const handleEditSubmit = useCallback(
    async (data: CreateMarketingCustomFieldRequest | UpdateMarketingCustomFieldRequest) => {
      if (!selectedCustomField || !selectedCustomField.id) return;

      // Loại bỏ fieldKey khỏi data update vì API không cho phép cập nhật fieldKey
      // eslint-disable-next-line @typescript-eslint/no-unused-vars
      const { fieldKey, ...updateData } = data as Record<string, unknown>;

      await updateCustomFieldMutation.mutateAsync({
        id: selectedCustomField.fieldKey, // Sử dụng fieldKey thay vì numeric id
        data: updateData as UpdateMarketingCustomFieldRequest,
      });
      hideEditForm();
      setSelectedCustomField(null);
    },
    [selectedCustomField, updateCustomFieldMutation, hideEditForm]
  );

  const handleCreateCancel = useCallback(() => {
    hideCreateForm();
  }, [hideCreateForm]);

  const handleEditCancel = useCallback(() => {
    hideEditForm();
    setSelectedCustomField(null);
  }, [hideEditForm]);

  return (
    <div className="w-full bg-background text-foreground">
      {/* Menu Icon Bar */}
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleCreateCustomField}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
        additionalIcons={[
          {
            icon: 'trash',
            tooltip: t('common:bulkDelete', 'Xóa nhiều'),
            variant: 'primary',
            onClick: handleShowBulkDeleteConfirm,
            className: 'text-red-500',
            condition: selectedRowKeys.length > 0,
          },
        ]}
      />

      {/* Active Filters */}
      <ActiveFilters
        onClearSearch={activeFilters.handleClearSearch}
        onClearFilter={activeFilters.handleClearFilter}
        onClearAll={activeFilters.handleClearAll}
        filterLabel={activeFilters.getFilterLabel()}
      />

      {/* Create Custom Field Form */}
      <SlideInForm isVisible={isCreateVisible}>
        <MarketingCustomFieldForm
          onSubmit={handleCreateSubmit}
          onCancel={handleCreateCancel}
          isLoading={createCustomFieldMutation.isPending}
        />
      </SlideInForm>

      {/* Edit Custom Field Form */}
      <SlideInForm isVisible={isEditVisible}>
        {selectedCustomField && (
          <MarketingCustomFieldForm
            initialData={selectedCustomField}
            onSubmit={handleEditSubmit}
            onCancel={handleEditCancel}
            isLoading={updateCustomFieldMutation.isPending}
          />
        )}
      </SlideInForm>

      {/* Custom Fields Table */}
      <Card className="overflow-hidden">
        <Table<MarketingCustomFieldResponse>
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={tableData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: tableData?.meta?.currentPage || 1,
            pageSize: tableData?.meta?.itemsPerPage || 10,
            total: tableData?.meta?.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Delete Confirmation Modal */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('common:confirmDelete', 'Xác nhận xóa')}
        message={t(
          'marketing:customField.confirmDeleteMessage',
          'Bạn có chắc chắn muốn xóa trường tùy chỉnh này?'
        )}
        isSubmitting={deleteCustomFieldMutation.isPending}
      />

      {/* Bulk Delete Confirmation Modal */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmDelete', 'Xác nhận xóa')}
        message={t(
          'marketing:customField.confirmBulkDeleteMessage',
          'Bạn có chắc chắn muốn xóa {{count}} trường tùy chỉnh đã chọn?',
          {
            count: selectedRowKeys.length,
          }
        )}
        isSubmitting={deleteMultipleCustomFieldsMutation.isPending}
      />
    </div>
  );
};

export default CustomFieldsPage;
