import { QueryClient } from '@tanstack/react-query';

/**
 * Utility functions cho việc đăng xuất
 */

/**
 * Xóa tất cả cache và dữ liệu khi đăng xuất
 * @param queryClient - TanStack Query client instance
 */
export const clearAllDataOnLogout = (queryClient: QueryClient): void => {
  try {
    // Xóa tất cả cache trong TanStack Query
    queryClient.clear();
    
    console.log('All TanStack Query cache cleared on logout');
  } catch (error) {
    console.error('Error clearing TanStack Query cache on logout:', error);
  }
};

/**
 * Xóa cache cho các query cụ thể khi đăng xuất
 * @param queryClient - TanStack Query client instance
 * @param queryKeys - Danh sách các query key cần xóa
 */
export const clearSpecificCacheOnLogout = (
  queryClient: QueryClient, 
  queryKeys: string[][]
): void => {
  try {
    queryKeys.forEach(queryKey => {
      queryClient.removeQueries({ queryKey });
    });
    
    console.log('Specific TanStack Query cache cleared on logout:', queryKeys);
  } catch (error) {
    console.error('Error clearing specific TanStack Query cache on logout:', error);
  }
};

/**
 * Invalidate tất cả queries khi đăng xuất (thay vì xóa hoàn toàn)
 * @param queryClient - TanStack Query client instance
 */
export const invalidateAllQueriesOnLogout = (queryClient: QueryClient): void => {
  try {
    // Invalidate tất cả queries
    queryClient.invalidateQueries();
    
    console.log('All TanStack Query queries invalidated on logout');
  } catch (error) {
    console.error('Error invalidating TanStack Query queries on logout:', error);
  }
};
