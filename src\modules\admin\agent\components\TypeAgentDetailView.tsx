import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  Chip,
  Button,
  CircularProgress,
  Alert
} from '@mui/material';
import { apiClient } from '@/shared/api/axios';

interface TypeAgent {
  id: number;
  name: string;
  description: string;
  createdAt: string;
  status: string;
}

interface TypeAgentDetailViewProps {
  onSelectType: (typeAgent: TypeAgent) => void;
  selectedTypeId?: number;
}

const TypeAgentDetailView: React.FC<TypeAgentDetailViewProps> = ({
  onSelectType,
  selectedTypeId
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const [typeAgents, setTypeAgents] = useState<TypeAgent[]>([]);
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);

  useEffect(() => {
    const fetchTypeAgents = async () => {
      try {
        setLoading(true);
        const response = await apiClient.get('/admin/type-agents');
        
        if (response.result?.items) {
          setTypeAgents(response.result.items);
        }
      } catch (err) {
        console.error('Error fetching type agents:', err);
        setError('Không thể tải danh sách loại agent');
      } finally {
        setLoading(false);
      }
    };

    fetchTypeAgents();
  }, []);

  if (loading) {
    return (
      <Box display="flex" justifyContent="center" alignItems="center" minHeight="200px">
        <CircularProgress />
      </Box>
    );
  }

  if (error) {
    return (
      <Alert severity="error" sx={{ mb: 2 }}>
        {error}
      </Alert>
    );
  }

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        {t('admin:agentTemplate.selectType')}
      </Typography>
      <Typography variant="body2" color="text.secondary" sx={{ mb: 3 }}>
        {t('admin:agentTemplate.selectTypeDescription')}
      </Typography>

      <Grid container spacing={2}>
        {typeAgents.map((typeAgent) => (
          <Grid item xs={12} sm={6} md={4} key={typeAgent.id}>
            <Card
              sx={{
                cursor: 'pointer',
                border: selectedTypeId === typeAgent.id ? 2 : 1,
                borderColor: selectedTypeId === typeAgent.id ? 'primary.main' : 'divider',
                '&:hover': {
                  borderColor: 'primary.main',
                  boxShadow: 2
                }
              }}
              onClick={() => onSelectType(typeAgent)}
            >
              <CardContent>
                <Box display="flex" justifyContent="space-between" alignItems="flex-start" mb={1}>
                  <Typography variant="h6" component="h3" noWrap>
                    {typeAgent.name}
                  </Typography>
                  <Chip
                    label={typeAgent.status}
                    size="small"
                    color={typeAgent.status === 'APPROVED' ? 'success' : 'default'}
                  />
                </Box>
                
                <Typography variant="body2" color="text.secondary" sx={{ mb: 2 }}>
                  {typeAgent.description}
                </Typography>

                <Typography variant="caption" color="text.secondary">
                  {t('admin:agentTemplate.createdAt')}: {new Date(parseInt(typeAgent.createdAt)).toLocaleDateString('vi-VN')}
                </Typography>
              </CardContent>
            </Card>
          </Grid>
        ))}
      </Grid>

      {typeAgents.length === 0 && (
        <Alert severity="info" sx={{ mt: 2 }}>
          {t('admin:agentTemplate.noTypeAgents')}
        </Alert>
      )}
    </Box>
  );
};

export default TypeAgentDetailView;
