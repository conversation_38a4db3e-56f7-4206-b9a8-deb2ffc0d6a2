import React, { useMemo, useCallback, useState } from 'react';
import { useTranslation } from 'react-i18next';
import { Card, Table, ActionMenu, ActionMenuItem } from '@/shared/components/common';
import { TableColumn, SortOrder } from '@/shared/components/common/Table/types';
import MenuIconBar from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import { SortDirection } from '@/shared/dto/request/query.dto';
import {
  AudienceStatus,
  AudienceQueryParams,
  AudienceType,
  AudienceAttribute,
  ContactData,
  CreateAudienceRequest,
} from '../types/audience.types';
import AudienceForm from '../components/forms/AudienceForm';
import AudienceDetailView from '../components/forms/AudienceDetailView';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useAudiences, useCreateAudience, useDeleteMultipleAudiences } from '../hooks';
import { useDataTableConfig, useDataTable } from '@/shared/hooks/table';
import { useActiveFilters } from '@/shared/hooks/filters';
import { NotificationUtil } from '@/shared/utils/notification';
import ConfirmDeleteModal from '@/shared/components/common/ConfirmDeleteModal';

/**
 * Interface cho dữ liệu audience từ API
 */
interface AudienceData {
  id: string;
  name: string;
  description: string;
  type: AudienceType;
  status: AudienceStatus;
  totalContacts: number;
  attributes: AudienceAttribute[];
  createdAt: string;
  updatedAt: string;
}



/**
 * Trang quản lý đối tượng sử dụng các hooks tối ưu
 */
const AudiencePage: React.FC = () => {
  const { t } = useTranslation(['marketing', 'common']);

  // State cho bulk delete
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  // State cho detail view
  const [selectedAudienceId, setSelectedAudienceId] = useState<number | null>(null);
  const [showDetailView, setShowDetailView] = useState(false);

  // Sử dụng hook tạo filterOptions
  const filterOptions = useMemo(
    () => [
      { id: 'all', label: t('common:all'), icon: 'list', value: 'all' },
      { id: 'active', label: t('common:active'), icon: 'check', value: AudienceStatus.ACTIVE },
      { id: 'draft', label: t('common:draft'), icon: 'file', value: AudienceStatus.DRAFT },
      {
        id: 'inactive',
        label: t('common:inactive'),
        icon: 'eye-off',
        value: AudienceStatus.INACTIVE,
      },
    ],
    [t]
  );

  // Định nghĩa columns cho bảng
  const columns = useMemo<TableColumn<AudienceData>[]>(
    () => [
      { key: 'id', title: t('common:id', 'ID'), dataIndex: 'id', width: '10%', sortable: true },
      {
        key: 'name',
        title: t('marketing:audience.email', 'Email'),
        dataIndex: 'name',
        width: '25%',
        sortable: true,
      },
      {
        key: 'description',
        title: t('marketing:audience.phone', 'Số điện thoại'),
        dataIndex: 'description',
        width: '20%',
        sortable: true,
      },
      {
        key: 'createdAt',
        title: t('common:createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        width: '15%',
        sortable: true,
        render: (value: unknown) => {
          // Chuyển đổi timestamp thành ngày
          const date = new Date(Number(value) * 1000);
          return date.toLocaleDateString('vi-VN');
        },
      },
      {
        key: 'actions',
        title: t('common:actions', 'Thao tác'),
        width: '15%',
        render: (_: unknown, record: AudienceData) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            {
              id: 'view',
              label: t('common:view', 'Xem'),
              icon: 'eye',
              onClick: () => handleViewDetail(parseInt(record.id, 10)),
            }
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm thao tác')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={false}
              preferRight={true}
            />
          );
        },
      },
    ],
    [t]
  );

  // Tạo hàm createQueryParams
  const createQueryParams = (params: {
    page: number;
    pageSize: number;
    searchTerm: string;
    sortBy: string | null;
    sortDirection: SortDirection | null;
    filterValue: string | number | boolean | undefined;
    dateRange: [Date | null, Date | null];
  }): AudienceQueryParams => {
    console.log('createQueryParams called with:', params); // Debug log
    const queryParams: AudienceQueryParams = {
      page: params.page,
      limit: params.pageSize,
      search: params.searchTerm || undefined,
      sortBy: params.sortBy || undefined,
      sortDirection: params.sortDirection || undefined,
    };

    if (params.filterValue !== 'all') {
      queryParams.status = params.filterValue as AudienceStatus;
    }

    console.log('Generated query params:', queryParams); // Debug log
    return queryParams;
  };

  // Sử dụng hook useDataTable với cấu hình mặc định
  const dataTable = useDataTable(
    useDataTableConfig<AudienceData, AudienceQueryParams>({
      columns,
      filterOptions,
      showDateFilter: false,
      createQueryParams,
    })
  );

  // Sử dụng hooks từ API với query params từ dataTable
  console.log('Current query params:', dataTable.queryParams); // Debug log
  const { data: apiAudienceData, isLoading } = useAudiences(dataTable.queryParams);

  // Chuyển đổi từ API response sang AudienceData
  const audienceData = useMemo(() => {
    console.log('API Audience Data:', apiAudienceData); // Debug log

    // Lấy dữ liệu từ API response
    const rawData = apiAudienceData?.items || [];

    if (!rawData || rawData.length === 0) {
      return { items: [], meta: { currentPage: 1, totalItems: 0 } };
    }

    // Chuyển đổi mỗi item từ API response sang AudienceData
    // API trả về dữ liệu contact (email, phone, customFields, tags) thay vì audience data
    const items = rawData.map((contact: ContactData, index: number): AudienceData => ({
      id: contact.id?.toString() || index.toString(),
      name: contact.email || `Contact ${contact.id || index + 1}`, // Sử dụng email làm tên tạm thời
      description: contact.phone || '', // Sử dụng phone làm mô tả tạm thời
      type: AudienceType.CUSTOMER, // Mặc định là customer
      status: AudienceStatus.ACTIVE, // Mặc định là active
      totalContacts: 1, // Mỗi contact = 1
      attributes: contact.customFields?.map((field, fieldIndex: number): AudienceAttribute => ({
        id: fieldIndex.toString(),
        name: field.name || `Field ${fieldIndex}`,
        value: field.value || '',
      })) || [],
      createdAt: contact.createdAt,
      updatedAt: contact.updatedAt,
    }));

    console.log('Processed items:', items); // Debug log

    return {
      items,
      meta: apiAudienceData?.meta || { currentPage: 1, totalItems: items.length },
    };
  }, [apiAudienceData]);
  const createAudienceMutation = useCreateAudience();
  console.log('createAudienceMutation:', createAudienceMutation); // Debug log
  const { mutateAsync: deleteMultipleAudiences } = useDeleteMultipleAudiences();

  // Sử dụng hook animation cho form thêm mới
  const {
    isVisible: isAddFormVisible,
    showForm: showAddForm,
    hideForm: hideAddForm,
  } = useSlideForm();



  // Xử lý hiển thị popup xác nhận xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      NotificationUtil.warning({
        message: t('marketing:audience.selectToDelete', 'Vui lòng chọn audience để xóa'),
        duration: 3000,
      });
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys, t]);

  // Xử lý hủy xóa nhiều
  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  // Xử lý xác nhận xóa nhiều
  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      // Chuyển đổi selectedRowKeys thành number[]
      const ids = selectedRowKeys.map(key => {
        if (typeof key === 'string') {
          return parseInt(key, 10);
        }
        return key as number;
      }).filter(id => !isNaN(id));

      // Gọi API xóa nhiều audiences cùng lúc
      await deleteMultipleAudiences(ids);

      setShowBulkDeleteConfirm(false);
      setSelectedRowKeys([]);

      // Hiển thị thông báo thành công
      NotificationUtil.success({
        message: t('marketing:audience.bulkDeleteSuccess', 'Xóa {{count}} audience thành công', { count: selectedRowKeys.length }),
        duration: 3000,
      });
    } catch {
      NotificationUtil.error({
        message: t('marketing:audience.bulkDeleteError', 'Xóa nhiều audience thất bại'),
        duration: 3000,
      });
    }
  }, [selectedRowKeys, deleteMultipleAudiences, t]);

  // Tạo hàm wrapper để chuyển đổi kiểu dữ liệu của handleSortChange
  const handleSortChangeWrapper = useCallback(
    (sortBy: string | null, sortDirection: SortOrder) => {
      dataTable.tableData.handleSortChange(sortBy, sortDirection);
    },
    [dataTable.tableData]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const {
    handleClearSearch,
    handleClearFilter,
    handleClearDateRange,
    handleClearSort,
    handleClearAll,
    getFilterLabel,
  } = useActiveFilters({
    handleSearch: dataTable.tableData.handleSearch,
    setSelectedFilterId: dataTable.filter.setSelectedId,
    setDateRange: dataTable.dateRange.setDateRange,
    handleSortChange: handleSortChangeWrapper,
    selectedFilterValue: dataTable.filter.selectedValue,
    filterValueLabelMap: {
      [AudienceStatus.ACTIVE]: t('common:active'),
      [AudienceStatus.DRAFT]: t('common:draft'),
      [AudienceStatus.INACTIVE]: t('common:inactive'),
    },
    t,
  });

  // Xử lý thêm mới
  const handleAdd = () => {
    console.log('handleAdd called - showing form'); // Debug log
    showAddForm();
  };

  // Xử lý xem chi tiết
  const handleViewDetail = (audienceId: number) => {
    setSelectedAudienceId(audienceId);
    setShowDetailView(true);
  };

  // Xử lý đóng detail view
  const handleCloseDetailView = () => {
    setShowDetailView(false);
    setSelectedAudienceId(null);
  };

  // Xử lý submit form
  const handleSubmit = (values: Record<string, unknown>) => {
    console.log('Form submitted with values:', values); // Debug log

    // Chuyển đổi values thành CreateAudienceRequest (không gửi countryCode vì API không chấp nhận)
    const audienceData: CreateAudienceRequest = {
      name: values['name'] as string,
      ...(values['email'] ? { email: values['email'] as string } : {}),
      ...(values['phone'] ? { phone: values['phone'] as string } : {}),
    };

    if (values['tagIds']) {
      audienceData.tagIds = values['tagIds'] as number[];
    }

    if (values['attributes']) {
      audienceData.attributes = values['attributes'] as Omit<AudienceAttribute, 'id'>[];
    }

    console.log('Sending audience data to API:', audienceData); // Debug log

    createAudienceMutation.mutate(audienceData, {
      onSuccess: () => {
        console.log('Audience created successfully'); // Debug log
        NotificationUtil.success({
          message: t('marketing:audience.createSuccess', 'Tạo đối tượng thành công'),
          duration: 3000,
        });
        hideAddForm();
      },
      onError: (error) => {
        console.error('Error creating audience:', error); // Debug log
        NotificationUtil.error({
          message: t('marketing:audience.createError', 'Tạo đối tượng thất bại'),
          duration: 3000,
        });
      },
    });
  };

  // Xử lý hủy form
  const handleCancel = () => {
    hideAddForm();
  };

  return (
    <div>
      <MenuIconBar
        onSearch={dataTable.tableData.handleSearch}
        onAdd={handleAdd}
        onDateRangeChange={dataTable.dateRange.setDateRange}
        onColumnVisibilityChange={dataTable.columnVisibility.setVisibleColumns}
        columns={dataTable.columnVisibility.visibleColumns}
        showDateFilter={false}
        showColumnFilter={true}
        additionalIcons={[
          {
            icon: 'trash',
            tooltip: t('common:bulkDelete', 'Xóa nhiều'),
            variant: 'primary',
            onClick: handleShowBulkDeleteConfirm,
            className: 'text-red-500',
            condition: selectedRowKeys.length > 0,
          },
        ]}
      />

      {/* Thêm component ActiveFilters */}
      <ActiveFilters
        searchTerm={dataTable.tableData.searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={dataTable.filter.selectedValue}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        dateRange={dataTable.dateRange.dateRange}
        onClearDateRange={handleClearDateRange}
        sortBy={dataTable.tableData.sortBy}
        sortDirection={dataTable.tableData.sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* Form thêm mới */}
      <SlideInForm isVisible={isAddFormVisible}>
        <AudienceForm onSubmit={handleSubmit} onCancel={handleCancel} />
      </SlideInForm>

      {/* Detail View */}
      <SlideInForm isVisible={showDetailView}>
        {selectedAudienceId && (
          <AudienceDetailView
            audienceId={selectedAudienceId}
            onClose={handleCloseDetailView}
          />
        )}
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table
          columns={dataTable.columnVisibility.visibleTableColumns}
          data={audienceData?.items || []}
          rowKey="id"
          loading={isLoading}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys),
          }}
          onSortChange={dataTable.tableData.handleSortChange}
          pagination={{
            current: audienceData?.meta?.currentPage || 1,
            pageSize: dataTable.tableData.pageSize,
            total: audienceData?.meta?.totalItems || 0,
            onChange: dataTable.tableData.handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [5, 10, 15, 20],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common:confirmDelete', 'Xác nhận xóa')}
        message={t('marketing:audience.confirmBulkDeleteMessage', 'Bạn có chắc chắn muốn xóa {{count}} audience đã chọn?', { count: selectedRowKeys.length })}
      />
    </div>
  );
};

export default AudiencePage;
