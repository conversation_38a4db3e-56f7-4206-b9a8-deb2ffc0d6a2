/**
 * Validation Pattern Constants
 * Tập hợp các regex patterns thường dùng cho validation
 */

export interface ValidationPattern {
  key: string;
  value: string;
  label?: string;
  description?: string;
  example?: string;
}

/**
 * <PERSON>h sách các pattern validation thường dùng
 */
export const VALIDATION_PATTERNS: ValidationPattern[] = [
  // Email & Communication
  {
    key: 'email',
    value: '^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\\.[a-zA-Z]{2,}$',
    label: 'Email',
    description: 'Định dạng email hợp lệ',
    example: '<EMAIL>'
  },
  {
    key: 'phoneVN',
    value: '^(\\+84|0)[0-9]{9,10}$',
    label: 'Số điện thoại Việt Nam',
    description: 'Số điện thoại Việt Nam (10-11 số)',
    example: '0912345678, +84912345678'
  },
  {
    key: 'phoneIntl',
    value: '^\\+[1-9]\\d{1,14}$',
    label: 'Số điện thoại quốc tế',
    description: 'Số điện thoại quốc tế theo chuẩn E.164',
    example: '+*********0'
  },

  // Text & Names
  {
    key: 'lettersOnly',
    value: '^[a-zA-ZÀ-ỹ\\s]+$',
    label: 'Chỉ chữ cái',
    description: 'Chỉ cho phép chữ cái và khoảng trắng',
    example: 'Nguyễn Văn A'
  },
  {
    key: 'vietnameseName',
    value: '^[a-zA-ZÀ-ỹ\\s]+$',
    label: 'Tên tiếng Việt',
    description: 'Tên người tiếng Việt có dấu',
    example: 'Nguyễn Thị Hương'
  },
  {
    key: 'numbersOnly',
    value: '^[0-9]+$',
    label: 'Chỉ số',
    description: 'Chỉ cho phép các chữ số',
    example: '123456'
  },
  {
    key: 'alphanumeric',
    value: '^[a-zA-Z0-9]+$',
    label: 'Chữ và số',
    description: 'Chỉ cho phép chữ cái và số',
    example: 'abc123'
  },
  {
    key: 'noSpecialChars',
    value: '^[a-zA-Z0-9\\s]+$',
    label: 'Không ký tự đặc biệt',
    description: 'Chữ cái, số và khoảng trắng',
    example: 'Hello World 123'
  },

  // URLs & Web
  {
    key: 'url',
    value: '^https?://(?:www\\.)?[-a-zA-Z0-9@:%._+~#=]{1,256}\\.[a-zA-Z0-9()]{1,6}\\b(?:[-a-zA-Z0-9()@:%_+.~#?&//=]*)$',
    label: 'URL',
    description: 'Đường dẫn web hợp lệ',
    example: 'https://www.example.com'
  },
  {
    key: 'domain',
    value: '^[a-zA-Z0-9]([a-zA-Z0-9\\-]{0,61}[a-zA-Z0-9])?\\.[a-zA-Z]{2,}$',
    label: 'Tên miền',
    description: 'Tên miền hợp lệ',
    example: 'example.com'
  },
  {
    key: 'urlSlug',
    value: '^[a-z0-9]+(?:-[a-z0-9]+)*$',
    label: 'URL Slug',
    description: 'Slug cho URL thân thiện',
    example: 'my-article-title'
  },

  // IDs & Codes
  {
    key: 'nationalId',
    value: '^[0-9]{9,12}$',
    label: 'CMND/CCCD',
    description: 'Số chứng minh nhân dân/căn cước công dân',
    example: '*********'
  },
  {
    key: 'taxCode',
    value: '^[0-9]{10,13}$',
    label: 'Mã số thuế',
    description: 'Mã số thuế cá nhân/doanh nghiệp',
    example: '*********0'
  },
  {
    key: 'studentId',
    value: '^[A-Z]{2}[0-9]{6}$',
    label: 'Mã sinh viên',
    description: 'Mã sinh viên (2 chữ + 6 số)',
    example: 'SV123456'
  },
  {
    key: 'postalCodeVN',
    value: '^[0-9]{5,6}$',
    label: 'Mã bưu điện VN',
    description: 'Mã bưu điện Việt Nam',
    example: '100000'
  },

  // Network & Technical
  {
    key: 'ipv4',
    value: '^(?:(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)\\.){3}(?:25[0-5]|2[0-4][0-9]|[01]?[0-9][0-9]?)$',
    label: 'Địa chỉ IPv4',
    description: 'Địa chỉ IP version 4',
    example: '***********'
  },
  {
    key: 'uuid',
    value: '^[0-9a-f]{8}-[0-9a-f]{4}-[1-5][0-9a-f]{3}-[89ab][0-9a-f]{3}-[0-9a-f]{12}$',
    label: 'UUID',
    description: 'Universally Unique Identifier',
    example: '123e4567-e89b-12d3-a456-************'
  },
  {
    key: 'variableName',
    value: '^[a-zA-Z_$][a-zA-Z0-9_$]*$',
    label: 'Tên biến',
    description: 'Tên biến hợp lệ trong lập trình',
    example: 'myVariable_123'
  },

  // Security & Passwords
  {
    key: 'strongPassword',
    value: '^(?=.*[a-z])(?=.*[A-Z])(?=.*\\d)(?=.*[@$!%*?&])[A-Za-z\\d@$!%*?&]{8,}$',
    label: 'Mật khẩu mạnh',
    description: 'Ít nhất 8 ký tự, có chữ hoa, chữ thường, số và ký tự đặc biệt',
    example: 'MyPass123!'
  },
  {
    key: 'base64',
    value: '^[A-Za-z0-9+\\/]*={0,2}$',
    label: 'Base64',
    description: 'Chuỗi mã hóa Base64',
    example: 'SGVsbG8gV29ybGQ='
  },

  // Date & Time
  {
    key: 'dateFormat',
    value: '^(0[1-9]|[12][0-9]|3[01])\\/(0[1-9]|1[012])\\/(19|20)\\d\\d$',
    label: 'Ngày tháng',
    description: 'Định dạng ngày DD/MM/YYYY',
    example: '31/12/2023'
  },
  {
    key: 'timeFormat',
    value: '^([01]?[0-9]|2[0-3]):[0-5][0-9]$',
    label: 'Giờ phút',
    description: 'Định dạng giờ HH:MM',
    example: '14:30'
  },

  // Colors & Design
  {
    key: 'hexColor',
    value: '^#([A-Fa-f0-9]{6}|[A-Fa-f0-9]{3})$',
    label: 'Màu Hex',
    description: 'Mã màu hex (3 hoặc 6 ký tự)',
    example: '#FF0000, #F00'
  },
  {
    key: 'rgbColor',
    value: '^rgb\\(([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5]),\\s*([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5]),\\s*([01]?[0-9]?[0-9]|2[0-4][0-9]|25[0-5])\\)$',
    label: 'Màu RGB',
    description: 'Mã màu RGB',
    example: 'rgb(255, 0, 0)'
  },

  // Numbers & Finance
  {
    key: 'decimal',
    value: '^\\d+(\\.\\d{1,2})?$',
    label: 'Số thập phân',
    description: 'Số thập phân (tối đa 2 chữ số sau dấu phẩy)',
    example: '123.45'
  },
  {
    key: 'creditCard',
    value: '^[0-9]{13,19}$',
    label: 'Số thẻ tín dụng',
    description: 'Số thẻ tín dụng (13-19 chữ số)',
    example: '****************'
  },

  // Files & Media
  {
    key: 'filename',
    value: '^[^<>:"/\\\\|?*]+$',
    label: 'Tên file',
    description: 'Tên file hợp lệ (không chứa ký tự đặc biệt)',
    example: 'document.pdf'
  },

  // Coordinates & Location
  {
    key: 'gpsCoordinate',
    value: '^-?([1-8]?[1-9]|[1-9]0)\\.{1}\\d{1,6}$',
    label: 'Tọa độ GPS',
    description: 'Tọa độ GPS (latitude/longitude)',
    example: '21.028511'
  },

  // Barcodes & QR
  {
    key: 'barcode',
    value: '^[0-9]{8,14}$',
    label: 'Mã vạch',
    description: 'Mã vạch (8-14 chữ số)',
    example: '*********0123'
  },
  {
    key: 'qrCode',
    value: '^[A-Za-z0-9\\-._~:/?#\\[\\]@!$&\'()*+,;=]+$',
    label: 'QR Code',
    description: 'Nội dung QR Code hợp lệ',
    example: 'https://example.com/qr'
  }
];

/**
 * Lấy pattern theo key
 */
export const getPatternByKey = (key: string): ValidationPattern | undefined => {
  return VALIDATION_PATTERNS.find(pattern => pattern.key === key);
};

/**
 * Lấy danh sách patterns theo category
 */
export const getPatternsByCategory = () => {
  return {
    communication: VALIDATION_PATTERNS.filter(p => 
      ['email', 'phoneVN', 'phoneIntl'].includes(p.key)
    ),
    text: VALIDATION_PATTERNS.filter(p => 
      ['lettersOnly', 'vietnameseName', 'numbersOnly', 'alphanumeric', 'noSpecialChars'].includes(p.key)
    ),
    web: VALIDATION_PATTERNS.filter(p => 
      ['url', 'domain', 'urlSlug'].includes(p.key)
    ),
    ids: VALIDATION_PATTERNS.filter(p => 
      ['nationalId', 'taxCode', 'studentId', 'postalCodeVN'].includes(p.key)
    ),
    technical: VALIDATION_PATTERNS.filter(p => 
      ['ipv4', 'uuid', 'variableName'].includes(p.key)
    ),
    security: VALIDATION_PATTERNS.filter(p => 
      ['strongPassword', 'base64'].includes(p.key)
    ),
    datetime: VALIDATION_PATTERNS.filter(p => 
      ['dateFormat', 'timeFormat'].includes(p.key)
    ),
    design: VALIDATION_PATTERNS.filter(p => 
      ['hexColor', 'rgbColor'].includes(p.key)
    ),
    finance: VALIDATION_PATTERNS.filter(p => 
      ['decimal', 'creditCard'].includes(p.key)
    ),
    media: VALIDATION_PATTERNS.filter(p => 
      ['filename'].includes(p.key)
    ),
    location: VALIDATION_PATTERNS.filter(p => 
      ['gpsCoordinate'].includes(p.key)
    ),
    codes: VALIDATION_PATTERNS.filter(p => 
      ['barcode', 'qrCode'].includes(p.key)
    )
  };
};

/**
 * Validate giá trị theo pattern key
 */
export const validateWithPattern = (value: string, patternKey: string): boolean => {
  const pattern = getPatternByKey(patternKey);
  if (!pattern) return false;
  
  const regex = new RegExp(pattern.value);
  return regex.test(value);
};

/**
 * Backward compatibility - export old format
 * @deprecated Sử dụng VALIDATION_PATTERNS thay thế
 */
export const PATTERN_SUGGESTIONS = VALIDATION_PATTERNS.map(pattern => ({
  key: pattern.key,
  value: pattern.value
}));
