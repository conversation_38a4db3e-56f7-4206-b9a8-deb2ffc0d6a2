import { z } from 'zod';
import { GenderEnum } from '../types/user.types';
import { TFunction } from 'i18next';

/**
 * Schema creator function for personal info form with translation support
 */
export const createPersonalInfoSchema = (t: TFunction) =>
  z.object({
    fullName: z.string().min(1, t('validation:required')),
    gender: z.nativeEnum(GenderEnum),
    dateOfBirth: z.union([z.string(), z.date()]).optional(),
    address: z.string().min(1, t('validation:required')),
    email: z.string().email(t('validation:invalidEmail')).min(1, t('validation:required')),
    phoneNumber: z.string().min(1, t('validation:required')),
  });

/**
 * Type for personal info schema
 */
export type PersonalInfoSchema = z.infer<ReturnType<typeof createPersonalInfoSchema>>;
