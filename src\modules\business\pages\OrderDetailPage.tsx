import React, { useState } from 'react';
import { usePara<PERSON>, useNavigate } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Button,
  Icon,
  Loading,
  Typography,
  Chip,
  Divider,
  Badge,
  Avatar,
  Progress,
  Alert,
  Modal,
  Textarea,
} from '@/shared/components/common';
import { useOrder, useCancelOrder } from '../hooks/useOrderQuery';
import { useOrderPrintAuto } from '../hooks/useOrderPrint';
import { formatTimestamp } from '@/shared/utils/date';
import { formatCurrency } from '@/shared/utils/format';
import {
  OrderStatusEnum,
  ShippingStatusEnum,
  PaymentStatusEnum,
  PaymentMethodEnum
} from '../services/business-api.service';
import { ApiUserOrder } from '../services/business-api.service';

// Interfaces for order detail data
interface OrderProduct {
  name: string;
  quantity: number;
  unitPrice: number;
  totalPrice: number;
  description?: string;
}

interface OrderProductInfo {
  products?: OrderProduct[];
}

/**
 * Interface cho sản phẩm từ API (có thể có nhiều format khác nhau)
 */
interface ApiOrderProduct {
  name?: string;
  productName?: string;
  sku?: string;
  quantity?: number;
  price?: number;
  unitPrice?: number;
  totalPrice?: number;
  description?: string;
  productId?: string;
  weight?: number;
  dimensions?: {
    length: number;
    width: number;
    height: number;
  };
  images?: Array<{
    key: string;
    position: number;
    url: string;
  }>;
}

/**
 * Interface cho thông tin logistics
 */
interface OrderLogisticInfo {
  recipientName?: string;
  recipientPhone?: string;
  deliveryAddress?: string;
  carrier?: string;
  trackingNumber?: string;
  estimatedDeliveryTime?: number;
  shippingNote?: string;
  serviceType?: string;
  shippingMethod?: string;
  shippingFee?: number;
  shippingError?: string;
  failedAt?: number;
  needsManualProcessing?: boolean;
}

/**
 * Trang chi tiết đơn hàng - Thiết kế hiện đại
 */
const OrderDetailPage: React.FC = () => {
  const navigate = useNavigate();
  const { t } = useTranslation(['business', 'common']);
  const { id } = useParams<{ id: string }>();
  const orderId = id ? parseInt(id) : 0;

  // State cho tabs
  const [activeTab, setActiveTab] = useState('overview');

  // State cho cancel modal
  const [showCancelModal, setShowCancelModal] = useState(false);
  const [cancelReason, setCancelReason] = useState('');

  // Lấy chi tiết đơn hàng
  const { data: orderResponse, isLoading, error } = useOrder(orderId);
  const order = orderResponse?.result as unknown as ApiUserOrder;

  // Hook in đơn hàng tự động
  const printAutoMutation = useOrderPrintAuto();

  // Hook hủy đơn hàng
  const cancelOrderMutation = useCancelOrder();

  // Xử lý in đơn hàng tự động
  const handlePrintAuto = () => {
    if (!orderId || orderId <= 0) {
      return;
    }
    printAutoMutation.mutate({ orderId });
  };

  // Xử lý hủy đơn hàng - mở modal
  const handleCancelOrder = () => {
    if (!orderId || orderId <= 0) {
      return;
    }
    setShowCancelModal(true);
  };

  // Xử lý đóng modal hủy đơn hàng
  const handleCloseCancelModal = () => {
    setShowCancelModal(false);
    setCancelReason('');
  };

  // Xử lý xác nhận hủy đơn hàng
  const handleConfirmCancel = () => {
    if (!orderId || orderId <= 0) {
      return;
    }

    const requestData: { orderId: number; reason?: string } = {
      orderId: orderId,
    };

    // Chỉ thêm reason nếu có nội dung
    if (cancelReason.trim()) {
      requestData.reason = cancelReason.trim();
    }

    cancelOrderMutation.mutate(requestData);
    handleCloseCancelModal();
  };

  // Xử lý loading
  if (isLoading) {
    return (
      <div className="flex justify-center items-center min-h-[400px]">
        <Loading size="lg" />
      </div>
    );
  }

  // Xử lý lỗi
  if (error || !order) {
    return (
      <div className="flex flex-col items-center justify-center min-h-[400px] space-y-4">
        <Icon name="alert-circle" className="w-12 h-12 text-red-500" />
        <Typography variant="h6" className="text-red-600">
          {t('business:order.detail.error', 'Không thể tải thông tin đơn hàng')}
        </Typography>
        <Button onClick={() => navigate('/business/order')} variant="outline">
          {t('common:back', 'Quay lại')}
        </Button>
      </div>
    );
  }

  // Helper functions để render status với Chip
  const getOrderStatusInfo = (status: OrderStatusEnum) => {
    const statusMap = {
      [OrderStatusEnum.PENDING]: {
        text: t('business:order.status.pending', 'Chờ xử lý'),
        variant: 'warning' as const
      },
      [OrderStatusEnum.CONFIRMED]: {
        text: t('business:order.status.confirmed', 'Đã xác nhận'),
        variant: 'info' as const
      },
      [OrderStatusEnum.PROCESSING]: {
        text: t('business:order.status.processing', 'Đang xử lý'),
        variant: 'primary' as const
      },
      [OrderStatusEnum.COMPLETED]: {
        text: t('business:order.status.completed', 'Hoàn thành'),
        variant: 'success' as const
      },
      [OrderStatusEnum.CANCELLED]: {
        text: t('business:order.status.cancelled', 'Đã hủy'),
        variant: 'danger' as const
      },
    };
    return statusMap[status] || { text: status, variant: 'default' as const };
  };

  const getShippingStatusInfo = (status: ShippingStatusEnum) => {
    const statusMap = {
      [ShippingStatusEnum.PENDING]: {
        text: t('business:order.shipping.pending', 'Chờ vận chuyển'),
        variant: 'warning' as const
      },
      [ShippingStatusEnum.PREPARING]: {
        text: t('business:order.shipping.preparing', 'Đang chuẩn bị'),
        variant: 'info' as const
      },
      [ShippingStatusEnum.SHIPPED]: {
        text: t('business:order.shipping.shipped', 'Đã gửi'),
        variant: 'primary' as const
      },
      [ShippingStatusEnum.IN_TRANSIT]: {
        text: t('business:order.shipping.inTransit', 'Đang vận chuyển'),
        variant: 'primary' as const
      },
      [ShippingStatusEnum.SORTING]: {
        text: t('business:order.shipping.sorting', 'Đang phân loại'),
        variant: 'info' as const
      },
      [ShippingStatusEnum.DELIVERED]: {
        text: t('business:order.shipping.delivered', 'Đã giao'),
        variant: 'success' as const
      },
      [ShippingStatusEnum.DELIVERY_FAILED]: {
        text: t('business:order.shipping.deliveryFailed', 'Giao hàng thất bại'),
        variant: 'danger' as const
      },
      [ShippingStatusEnum.RETURNING]: {
        text: t('business:order.shipping.returning', 'Đang hoàn trả'),
        variant: 'warning' as const
      },
      [ShippingStatusEnum.CANCELLED]: {
        text: t('business:order.shipping.cancelled', 'Đã hủy'),
        variant: 'danger' as const
      },
    };
    return statusMap[status] || { text: status, variant: 'default' as const };
  };

  const getPaymentStatusInfo = (status: PaymentStatusEnum) => {
    const statusMap = {
      [PaymentStatusEnum.PENDING]: {
        text: t('business:order.payment.pending', 'Chờ thanh toán'),
        variant: 'warning' as const
      },
      [PaymentStatusEnum.PAID]: {
        text: t('business:order.payment.paid', 'Đã thanh toán'),
        variant: 'success' as const
      },
      [PaymentStatusEnum.FAILED]: {
        text: t('business:order.payment.failed', 'Thanh toán thất bại'),
        variant: 'danger' as const
      },
      [PaymentStatusEnum.REFUNDED]: {
        text: t('business:order.payment.refunded', 'Đã hoàn tiền'),
        variant: 'info' as const
      },
    };
    return statusMap[status] || { text: status, variant: 'default' as const };
  };

  const getPaymentMethodText = (method: PaymentMethodEnum) => {
    const methodMap = {
      [PaymentMethodEnum.CASH]: t('business:order.payment.method.cash', 'Tiền mặt'),
      [PaymentMethodEnum.BANKING]: t('business:order.payment.method.banking', 'Chuyển khoản ngân hàng'),
      [PaymentMethodEnum.CREDIT_CARD]: t('business:order.payment.method.creditCard', 'Thẻ tín dụng'),
      [PaymentMethodEnum.E_WALLET]: t('business:order.payment.method.eWallet', 'Ví điện tử'),
    };
    return methodMap[method] || method;
  };

  const orderStatusInfo = getOrderStatusInfo(order.orderStatus as OrderStatusEnum);
  const shippingStatusInfo = getShippingStatusInfo(order.shippingStatus as ShippingStatusEnum);
  const paymentStatusInfo = getPaymentStatusInfo(order.billInfo?.paymentStatus as PaymentStatusEnum || PaymentStatusEnum.PENDING);

  // Helper function để tính progress của order
  const getOrderProgress = () => {
    const statusOrder = [
      OrderStatusEnum.PENDING,
      OrderStatusEnum.CONFIRMED,
      OrderStatusEnum.PROCESSING,
      OrderStatusEnum.COMPLETED
    ];
    const currentIndex = statusOrder.indexOf(order.orderStatus as OrderStatusEnum);
    return currentIndex >= 0 ? ((currentIndex + 1) / statusOrder.length) * 100 : 0;
  };

  // Helper function để kiểm tra xem đơn hàng có thể hủy không
  const canCancelOrder = () => {
    const currentStatus = order.orderStatus as OrderStatusEnum;
    // Chỉ cho phép hủy khi đơn hàng ở trạng thái PENDING, CONFIRMED hoặc PROCESSING
    // Không cho phép hủy khi đã COMPLETED hoặc đã CANCELLED
    return currentStatus === OrderStatusEnum.PENDING ||
           currentStatus === OrderStatusEnum.CONFIRMED ||
           currentStatus === OrderStatusEnum.PROCESSING;
  };

  return (
    <div className="space-y-6 p-2">
      {/* Header hiện đại */}
      <div className="bg-gradient-to-r from-primary/5 to-primary/10 rounded-xl p-6">
        <div className="flex flex-col lg:flex-row lg:items-center justify-between gap-4">
          <div className="space-y-3">
            <div className="flex items-center space-x-3">
              <Typography variant="h4" className="font-bold">
                {t('business:order.detail.title', 'Chi tiết đơn hàng')} #{order.id}
              </Typography>
              <Chip variant={orderStatusInfo.variant} size="md">
                {orderStatusInfo.text}
              </Chip>
            </div>
            <Typography variant="body1" className="text-muted-foreground">
              {t('business:order.detail.createdAt', 'Tạo lúc')}: {formatTimestamp(order.createdAt)}
            </Typography>

            {/* Progress bar cho order status */}
            <div className="mt-4">
              <div className="flex items-center justify-between mb-2">
                <Typography variant="body2" className="font-medium">
                  {t('business:order.detail.progress', 'Tiến độ đơn hàng')}
                </Typography>
                <Typography variant="body2" className="text-muted-foreground">
                  {Math.round(getOrderProgress())}%
                </Typography>
              </div>
              <Progress
                value={getOrderProgress()}
                color="primary"
                size="md"
                className="w-full max-w-md"
              />
            </div>
          </div>

          <div className="flex items-center space-x-3">
            <Button
              variant="outline"
              size="sm"
              onClick={handlePrintAuto}
              disabled={printAutoMutation.isPending}
            >
              {printAutoMutation.isPending ? (
                <>
                  <Icon name="loader" className="w-4 h-4 mr-2 animate-spin" />
                  {t('business:order.printing', 'Đang in...')}
                </>
              ) : (
                <>
                  <Icon name="printer" className="w-4 h-4 mr-2" />
                  {t('common:print', 'In')}
                </>
              )}
            </Button>
            <Button variant="outline" size="sm">
              <Icon name="download" className="w-4 h-4 mr-2" />
              {t('common:export', 'Xuất')}
            </Button>
            {canCancelOrder() && (
              <Button
                variant="outline"
                size="sm"
                onClick={handleCancelOrder}
                disabled={cancelOrderMutation.isPending}
                className="text-red-600 border-red-300 hover:bg-red-50 hover:border-red-400 dark:text-red-400 dark:border-red-600 dark:hover:bg-red-900/20"
              >
                {cancelOrderMutation.isPending ? (
                  <>
                    <Icon name="loader" className="w-4 h-4 mr-2 animate-spin" />
                    {t('business:order.cancelling', 'Đang hủy...')}
                  </>
                ) : (
                  <>
                    <Icon name="x-circle" className="w-4 h-4 mr-2" />
                    {t('business:order.cancel', 'Hủy đơn hàng')}
                  </>
                )}
              </Button>
            )}
          </div>
        </div>
      </div>

      {/* Tabs Navigation - Modern Design */}
      <div className="mb-6">
        <div className="bg-white dark:bg-gray-800 rounded-xl shadow-sm">
          <div className="flex space-x-1">
            {[
              {
                key: 'overview',
                label: t('business:order.detail.tabs.overview', 'Tổng quan'),
                icon: <Icon name="eye" className="w-4 h-4" />,
                badge: null
              },
              {
                key: 'products',
                label: t('business:order.detail.tabs.products', 'Sản phẩm'),
                icon: <Icon name="package" className="w-4 h-4" />,
                badge: Array.isArray(order.productInfo) ? order.productInfo.length.toString() : '0'
              },
              {
                key: 'tracking',
                label: t('business:order.detail.tabs.tracking', 'Theo dõi'),
                icon: <Icon name="truck" className="w-4 h-4" />,
                badge: null
              },
              {
                key: 'payment',
                label: t('business:order.detail.tabs.payment', 'Thanh toán'),
                icon: <Icon name="credit-card" className="w-4 h-4" />,
                badge: null
              }
            ].map((tab) => (
              <button
                key={tab.key}
                onClick={() => setActiveTab(tab.key)}
                className={`
                  flex items-center space-x-2 px-4 py-2.5 rounded-lg font-medium text-sm transition-all duration-200 ease-in-out
                  ${activeTab === tab.key
                    ? 'bg-primary text-white shadow-md transform scale-[1.02]'
                    : 'text-gray-600 dark:text-gray-400 hover:text-gray-900 dark:hover:text-gray-200 hover:bg-gray-100 dark:hover:bg-gray-700'
                  }
                `}
              >
                <span className={`${activeTab === tab.key ? 'text-white' : ''}`}>
                  {tab.icon}
                </span>
                <span>{tab.label}</span>
                {tab.badge && (
                  <span className={`
                    px-2 py-0.5 text-xs rounded-full font-semibold
                    ${activeTab === tab.key
                      ? 'bg-white/20 text-white'
                      : 'bg-primary text-white'
                    }
                  `}>
                    {tab.badge}
                  </span>
                )}
              </button>
            ))}
          </div>
        </div>
      </div>

      {/* Tab Content */}
      <div className={`grid grid-cols-1 gap-6 ${activeTab === 'overview' ? 'xl:grid-cols-4' : ''}`}>
        {/* Main Content */}
        <div className={`space-y-6 ${activeTab === 'overview' ? 'xl:col-span-3' : ''}`}>
          {activeTab === 'overview' && (
            <>
              {/* Thông tin đơn hàng */}
              <div className="space-y-6">
                <Typography variant="h5" className="font-semibold flex items-center">
                  <Icon name="file-text" className="w-5 h-5 mr-2" />
                  {t('business:order.detail.orderInfo', 'Thông tin đơn hàng')}
                </Typography>
                <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-3 gap-6">
                  <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="w-10 h-10 bg-primary/10 rounded-lg flex items-center justify-center">
                        <Icon name="hash" className="w-5 h-5 text-primary" />
                      </div>
                      <div>
                        <Typography variant="body2" className="text-muted-foreground">
                          {t('business:order.detail.orderId', 'Mã đơn hàng')}
                        </Typography>
                        <Typography variant="body1" className="font-bold text-lg">
                          #{order.id}
                        </Typography>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="w-10 h-10 bg-blue-500/10 rounded-lg flex items-center justify-center">
                        <Icon name="calendar" className="w-5 h-5 text-blue-500" />
                      </div>
                      <div>
                        <Typography variant="body2" className="text-muted-foreground">
                          {t('business:order.detail.createdAt', 'Ngày tạo')}
                        </Typography>
                        <Typography variant="body1" className="font-medium">
                          {formatTimestamp(order.createdAt)}
                        </Typography>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="w-10 h-10 bg-green-500/10 rounded-lg flex items-center justify-center">
                        <Icon name="clock" className="w-5 h-5 text-green-500" />
                      </div>
                      <div>
                        <Typography variant="body2" className="text-muted-foreground">
                          {t('business:order.detail.updatedAt', 'Cập nhật lần cuối')}
                        </Typography>
                        <Typography variant="body1" className="font-medium">
                          {formatTimestamp(order.updatedAt)}
                        </Typography>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="w-10 h-10 bg-purple-500/10 rounded-lg flex items-center justify-center">
                        <Icon name="globe" className="w-5 h-5 text-purple-500" />
                      </div>
                      <div>
                        <Typography variant="body2" className="text-muted-foreground">
                          {t('business:order.detail.source', 'Nguồn đơn hàng')}
                        </Typography>
                        <Typography variant="body1" className="font-medium capitalize">
                          {order.source || t('business:order.detail.unknownSource', 'Không xác định')}
                        </Typography>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="w-10 h-10 bg-orange-500/10 rounded-lg flex items-center justify-center">
                        <Icon name="truck" className="w-5 h-5 text-orange-500" />
                      </div>
                      <div>
                        <Typography variant="body2" className="text-muted-foreground">
                          {t('business:order.detail.hasShipping', 'Có vận chuyển')}
                        </Typography>
                        <div className="flex items-center space-x-2">
                          <Typography variant="body1" className="font-medium">
                            {order.hasShipping ? t('common:yes', 'Có') : t('common:no', 'Không')}
                          </Typography>
                          {order.hasShipping && (
                            <Badge variant="success" dot />
                          )}
                        </div>
                      </div>
                    </div>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
                    <div className="flex items-center space-x-3 mb-2">
                      <div className="w-10 h-10 bg-indigo-500/10 rounded-lg flex items-center justify-center">
                        <Icon name="user" className="w-5 h-5 text-indigo-500" />
                      </div>
                      <div>
                        <Typography variant="body2" className="text-muted-foreground">
                          {t('business:order.detail.customerId', 'ID khách hàng')}
                        </Typography>
                        <Typography variant="body1" className="font-medium">
                          #{order.userConvertCustomerId || 'N/A'}
                        </Typography>
                      </div>
                    </div>
                  </div>
                </div>
              </div>
            </>
          )}
        </div>

        {/* Sidebar - Moved to Overview Tab */}
        {activeTab === 'overview' && (
          <div className="xl:col-span-1 space-y-6">
              {/* Quick Status Overview */}
              <div className="space-y-6">
                <Typography variant="h5" className="font-semibold flex items-center">
                  <Icon name="activity" className="w-5 h-5 mr-2" />
                  {t('business:order.detail.quickStatus', 'Trạng thái nhanh')}
                </Typography>
                <div className="space-y-4">
                  <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <Typography variant="body2" className="text-muted-foreground">
                        {t('business:order.detail.orderStatus', 'Trạng thái đơn hàng')}
                      </Typography>
                      <Chip variant={orderStatusInfo.variant} size="sm">
                        {orderStatusInfo.text}
                      </Chip>
                    </div>
                    <Progress
                      value={getOrderProgress()}
                      color="primary"
                      size="sm"
                      className="mt-2"
                    />
                  </div>

                  {order.hasShipping && (
                    <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
                      <div className="flex items-center justify-between mb-2">
                        <Typography variant="body2" className="text-muted-foreground">
                          {t('business:order.detail.shippingStatus', 'Trạng thái vận chuyển')}
                        </Typography>
                        <Chip variant={shippingStatusInfo.variant} size="sm">
                          {shippingStatusInfo.text}
                        </Chip>
                      </div>
                    </div>
                  )}

                  <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
                    <div className="flex items-center justify-between">
                      <Typography variant="body2" className="text-muted-foreground">
                        {t('business:order.detail.paymentStatus', 'Trạng thái thanh toán')}
                      </Typography>
                      <Chip variant={paymentStatusInfo.variant} size="sm">
                        {paymentStatusInfo.text}
                      </Chip>
                    </div>
                  </div>
                </div>
              </div>

              {/* Payment Summary */}
              <div className="space-y-6">
                <Typography variant="h5" className="font-semibold flex items-center">
                  <Icon name="dollar-sign" className="w-5 h-5 mr-2" />
                  {t('business:order.detail.paymentSummary', 'Tóm tắt thanh toán')}
                </Typography>
                <div className="space-y-4">
                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <Typography variant="body2" className="text-muted-foreground">
                        {t('business:order.detail.subtotal', 'Tạm tính')}
                      </Typography>
                      <Typography variant="body2" className="font-medium">
                        {formatCurrency(order.billInfo?.subtotal || 0)}
                      </Typography>
                    </div>

                    <div className="flex justify-between items-center">
                      <Typography variant="body2" className="text-muted-foreground">
                        {t('business:order.detail.shippingFee', 'Phí vận chuyển')}
                      </Typography>
                      <Typography variant="body2" className="font-medium">
                        {formatCurrency(order.billInfo?.shippingFee || 0)}
                      </Typography>
                    </div>

                    {order.billInfo?.discount && order.billInfo.discount > 0 && (
                      <div className="flex justify-between items-center">
                        <Typography variant="body2" className="text-muted-foreground">
                          {t('business:order.detail.discount', 'Giảm giá')}
                        </Typography>
                        <Typography variant="body2" className="font-medium text-red-600">
                          -{formatCurrency(order.billInfo.discount)}
                        </Typography>
                      </div>
                    )}
                  </div>

                  <Divider />

                  <div className="bg-primary/5 rounded-lg p-4">
                    <div className="flex justify-between items-center">
                      <Typography variant="body1" className="font-semibold">
                        {t('business:order.detail.total', 'Tổng cộng')}
                      </Typography>
                      <Typography variant="h6" className="font-bold text-primary">
                        {formatCurrency(order.billInfo?.total || 0)}
                      </Typography>
                    </div>
                  </div>

                  <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3">
                    <Typography variant="body2" className="text-muted-foreground mb-1">
                      {t('business:order.detail.paymentMethod', 'Phương thức thanh toán')}
                    </Typography>
                    <div className="flex items-center space-x-2">
                      <Icon name="credit-card" className="w-4 h-4 text-blue-500" />
                      <Typography variant="body2" className="font-medium">
                        {getPaymentMethodText(order.billInfo?.paymentMethod as PaymentMethodEnum || PaymentMethodEnum.CASH)}
                      </Typography>
                    </div>
                  </div>
                </div>
              </div>

              {/* Shipping Information */}
              {order.hasShipping && order.logisticInfo && (
                <div className="space-y-6">
                  <Typography variant="h5" className="font-semibold flex items-center">
                    <Icon name="truck" className="w-5 h-5 mr-2" />
                    {t('business:order.detail.shipping', 'Thông tin vận chuyển')}
                  </Typography>
                  <div className="space-y-6">
                    {/* Recipient Information */}
                    <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4">
                      <Typography variant="body1" className="font-semibold text-blue-900 dark:text-blue-100 mb-3 flex items-center">
                        <Icon name="user" className="w-4 h-4 mr-2" />
                        {t('business:order.detail.recipientInfo', 'Thông tin người nhận')}
                      </Typography>
                      <div className="space-y-2">
                        <div className="flex items-center space-x-2">
                          <Icon name="user" className="w-4 h-4 text-blue-600" />
                          <Typography variant="body2" className="font-medium">
                            {(order.logisticInfo as OrderLogisticInfo)?.recipientName || 'N/A'}
                          </Typography>
                        </div>
                        <div className="flex items-center space-x-2">
                          <Icon name="phone" className="w-4 h-4 text-blue-600" />
                          <Typography variant="body2">
                            {(order.logisticInfo as OrderLogisticInfo)?.recipientPhone || 'N/A'}
                          </Typography>
                        </div>
                        <div className="flex items-start space-x-2">
                          <Icon name="map-pin" className="w-4 h-4 text-blue-600 mt-0.5" />
                          <Typography variant="body2" className="break-words">
                            {(order.logisticInfo as OrderLogisticInfo)?.deliveryAddress || 'N/A'}
                          </Typography>
                        </div>
                      </div>
                    </div>

                    {/* Shipping Details */}
                    <div className="space-y-3">
                      <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3">
                        <div className="flex items-center space-x-3">
                          <Icon name="truck" className="w-4 h-4 text-orange-500" />
                          <div>
                            <Typography variant="body2" className="text-muted-foreground">
                              {t('business:order.detail.carrier', 'Đơn vị vận chuyển')}
                            </Typography>
                            <Typography variant="body2" className="font-medium">
                              {(order.logisticInfo as OrderLogisticInfo)?.carrier || 'N/A'}
                            </Typography>
                          </div>
                        </div>
                      </div>

                      {(order.logisticInfo as OrderLogisticInfo)?.serviceType && (
                        <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3">
                          <div className="flex items-center space-x-3">
                            <Icon name="package" className="w-4 h-4 text-purple-500" />
                            <div>
                              <Typography variant="body2" className="text-muted-foreground">
                                {t('business:order.detail.serviceType', 'Loại dịch vụ')}
                              </Typography>
                              <Typography variant="body2" className="font-medium">
                                {(order.logisticInfo as OrderLogisticInfo).serviceType}
                              </Typography>
                            </div>
                          </div>
                        </div>
                      )}

                      <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3">
                        <div className="flex items-center space-x-3">
                          <Icon name="dollar-sign" className="w-4 h-4 text-green-500" />
                          <div>
                            <Typography variant="body2" className="text-muted-foreground">
                              {t('business:order.detail.shippingFee', 'Phí vận chuyển')}
                            </Typography>
                            <Typography variant="body2" className="font-bold text-green-600">
                              {formatCurrency((order.logisticInfo as OrderLogisticInfo)?.shippingFee || 0)}
                            </Typography>
                          </div>
                        </div>
                      </div>
                    </div>

                    {/* Shipping Notes */}
                    {(order.logisticInfo as OrderLogisticInfo)?.shippingNote && (
                      <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4">
                        <Typography variant="body2" className="text-yellow-800 dark:text-yellow-200 font-medium mb-1 flex items-center">
                          <Icon name="message-circle" className="w-4 h-4 mr-2" />
                          {t('business:order.detail.shippingNote', 'Ghi chú vận chuyển')}
                        </Typography>
                        <Typography variant="body2" className="text-yellow-700 dark:text-yellow-300 italic">
                          "{(order.logisticInfo as OrderLogisticInfo)?.shippingNote}"
                        </Typography>
                      </div>
                    )}

                    {/* Shipping Errors */}
                    {(order.logisticInfo as OrderLogisticInfo)?.shippingError && (
                      <Alert
                        type="error"
                        title={t('business:order.detail.shippingError', 'Lỗi vận chuyển')}
                        message={(order.logisticInfo as OrderLogisticInfo)?.shippingError || ''}
                        description={
                          (order.logisticInfo as OrderLogisticInfo)?.failedAt
                            ? `${t('business:order.detail.failedAt', 'Thời gian lỗi')}: ${formatTimestamp((order.logisticInfo as OrderLogisticInfo).failedAt)}`
                            : undefined
                        }
                        showIcon
                      />
                    )}

                    {(order.logisticInfo as OrderLogisticInfo)?.needsManualProcessing && (
                      <Alert
                        type="warning"
                        message={t('business:order.detail.needsManualProcessing', 'Cần xử lý thủ công')}
                        description="Đơn hàng này cần được xử lý thủ công bởi nhân viên."
                        showIcon
                      />
                    )}
                  </div>
                </div>
              )}
            </div>
          )}

        {activeTab === 'products' && (
          <div className="space-y-6">
            <Typography variant="h5" className="font-semibold flex items-center">
              <Icon name="package" className="w-5 h-5 mr-2" />
              {t('business:order.detail.products', 'Sản phẩm')}
            </Typography>
            <div className="space-y-4">
                {(() => {
                  // Kiểm tra nếu productInfo là array và có dữ liệu
                  if (Array.isArray(order.productInfo) && order.productInfo.length > 0) {
                    return order.productInfo.map((product: ApiOrderProduct, index: number) => (
                      <div key={index} className="bg-white dark:bg-gray-800">
                        <div className="flex items-start gap-6">
                          {/* Hình ảnh sản phẩm */}
                          <div className="flex-shrink-0">
                            {product.images && product.images.length > 0 ? (
                              <img
                                src={product.images[0]?.url || `https://your-cdn-domain.com/${product.images[0]?.key}`}
                                alt={product.name || product.productName || 'Product image'}
                                className="w-20 h-20 object-cover rounded-lg"
                                onError={(e) => {
                                  (e.target as HTMLImageElement).style.display = 'none';
                                }}
                              />
                            ) : (
                              <div className="w-20 h-20 bg-gray-100 dark:bg-gray-700 rounded-lg flex items-center justify-center">
                                <Icon name="package" className="w-8 h-8 text-gray-400" />
                              </div>
                            )}
                          </div>

                          <div className="flex-1 min-w-0">
                            <div className="flex items-start justify-between mb-3">
                              <div>
                                <Typography variant="h6" className="font-semibold mb-1 text-gray-900 dark:text-gray-100">
                                  {product.name || product.productName || t('business:order.detail.unknownProduct', 'Sản phẩm không xác định')}
                                </Typography>
                                {product.productId && (
                                  <Typography variant="body2" className="text-muted-foreground">
                                    ID: {product.productId}
                                  </Typography>
                                )}
                              </div>
                              <div className="text-right">
                                <Typography variant="h6" className="font-bold text-primary">
                                  {formatCurrency(product.totalPrice || (product.price || product.unitPrice || 0) * (product.quantity || 1))}
                                </Typography>
                                <Typography variant="body2" className="text-muted-foreground">
                                  {formatCurrency(product.price || product.unitPrice || 0)} x {product.quantity || 1}
                                </Typography>
                              </div>
                            </div>

                            {product.description && (
                              <Typography variant="body2" className="text-muted-foreground mb-4 line-clamp-2">
                                {product.description}
                              </Typography>
                            )}

                            <div className="grid grid-cols-2 md:grid-cols-4 gap-4">
                              <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                                <Typography variant="body2" className="text-muted-foreground mb-1">
                                  {t('business:order.detail.quantity', 'Số lượng')}
                                </Typography>
                                <Typography variant="body1" className="font-medium">
                                  {product.quantity || 1}
                                </Typography>
                              </div>

                              <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                                <Typography variant="body2" className="text-muted-foreground mb-1">
                                  {t('business:order.detail.unitPrice', 'Đơn giá')}
                                </Typography>
                                <Typography variant="body1" className="font-medium">
                                  {formatCurrency(product.price || product.unitPrice || 0)}
                                </Typography>
                              </div>

                              {product.weight && (
                                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                                  <Typography variant="body2" className="text-muted-foreground mb-1">
                                    {t('business:order.detail.weight', 'Trọng lượng')}
                                  </Typography>
                                  <Typography variant="body1" className="font-medium">
                                    {product.weight}g
                                  </Typography>
                                </div>
                              )}

                              {product.dimensions && (
                                <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg p-3">
                                  <Typography variant="body2" className="text-muted-foreground mb-1">
                                    {t('business:order.detail.dimensions', 'Kích thước')}
                                  </Typography>
                                  <Typography variant="body1" className="font-medium">
                                    {product.dimensions.length}×{product.dimensions.width}×{product.dimensions.height}cm
                                  </Typography>
                                </div>
                              )}
                            </div>
                          </div>
                        </div>
                      </div>
                    ));
                  }

                  // Kiểm tra nếu productInfo có cấu trúc legacy với products array
                  const legacyProducts = (order.productInfo as OrderProductInfo | undefined)?.products;
                  if (legacyProducts && legacyProducts.length > 0) {
                    return legacyProducts.map((product: OrderProduct, index: number) => (
                      <div key={index} className="bg-white dark:bg-gray-800 rounded-xl p-6 shadow-sm">
                        <div className="flex items-center justify-between">
                          <div className="flex-1">
                            <Typography variant="h6" className="font-semibold mb-2">{product.name}</Typography>
                            <div className="flex items-center space-x-4 text-sm">
                              <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg px-3 py-1">
                                <Typography variant="body2" className="text-muted-foreground">
                                  {t('business:order.detail.quantity', 'Số lượng')}: <span className="font-medium">{product.quantity}</span>
                                </Typography>
                              </div>
                              <div className="bg-gray-50 dark:bg-gray-700/50 rounded-lg px-3 py-1">
                                <Typography variant="body2" className="text-muted-foreground">
                                  {t('business:order.detail.unitPrice', 'Đơn giá')}: <span className="font-medium">{formatCurrency(product.unitPrice)}</span>
                                </Typography>
                              </div>
                            </div>
                            {product.description && (
                              <Typography variant="body2" className="text-muted-foreground mt-3 line-clamp-2">{product.description}</Typography>
                            )}
                          </div>
                          <div className="text-right ml-6">
                            <Typography variant="h6" className="font-bold text-primary">
                              {formatCurrency(product.totalPrice)}
                            </Typography>
                          </div>
                        </div>
                      </div>
                    ));
                  }

                  // Không có sản phẩm
                  return (
                    <div className="text-center py-12">
                      <Icon name="package" className="w-16 h-16 text-gray-300 mx-auto mb-4" />
                      <Typography variant="h6" className="text-muted-foreground mb-2">
                        {t('business:order.detail.noProducts', 'Không có thông tin sản phẩm')}
                      </Typography>
                      <Typography variant="body2" className="text-muted-foreground">
                        Đơn hàng này chưa có sản phẩm nào được thêm vào.
                      </Typography>
                    </div>
                  );
                })()}
            </div>
          </div>
        )}
        {/* Tab Tracking */}
        {activeTab === 'tracking' && order.hasShipping && (
          <div className="space-y-6">
            <Typography variant="h5" className="font-semibold flex items-center">
              <Icon name="truck" className="w-5 h-5 mr-2" />
              {t('business:order.detail.tracking', 'Theo dõi đơn hàng')}
            </Typography>
              {/* Order Status Timeline */}
              <div className="mb-8">
                <Typography variant="h6" className="mb-4 flex items-center">
                  <Icon name="clock" className="w-5 h-5 mr-2 text-blue-500" />
                  {t('business:order.detail.orderTimeline', 'Timeline đơn hàng')}
                </Typography>
                <div className="space-y-4">
                  {[
                    { status: OrderStatusEnum.PENDING, icon: 'clock', color: 'yellow' },
                    { status: OrderStatusEnum.CONFIRMED, icon: 'check-circle', color: 'blue' },
                    { status: OrderStatusEnum.PROCESSING, icon: 'package', color: 'purple' },
                    { status: OrderStatusEnum.COMPLETED, icon: 'check', color: 'green' }
                  ].map((step, index) => {
                    const isCompleted = Object.values(OrderStatusEnum).indexOf(order.orderStatus as OrderStatusEnum) > index;
                    const isCurrent = order.orderStatus === step.status;
                    const statusInfo = getOrderStatusInfo(step.status);

                    return (
                      <div key={step.status} className="flex items-center space-x-4">
                        <div className={`
                          w-10 h-10 rounded-full flex items-center justify-center border-2 transition-all
                          ${isCompleted
                            ? 'bg-green-500 border-green-500 text-white'
                            : isCurrent
                              ? 'bg-blue-500 border-blue-500 text-white'
                              : 'bg-gray-100 border-gray-300 text-gray-400'
                          }
                        `}>
                          <Icon name={step.icon as 'clock' | 'check-circle' | 'package' | 'check'} className="w-5 h-5" />
                        </div>
                        <div className="flex-1">
                          <Typography variant="body1" className={`font-medium ${
                            isCompleted ? 'text-green-600' : isCurrent ? 'text-blue-600' : 'text-gray-500'
                          }`}>
                            {statusInfo.text}
                          </Typography>
                          {isCurrent && (
                            <Typography variant="body2" className="text-muted-foreground">
                              {formatTimestamp(order.updatedAt)}
                            </Typography>
                          )}
                        </div>
                      </div>
                    );
                  })}
                </div>
              </div>

              {/* Shipping Timeline */}
              {order.shippingStatus && (
                <div>
                  <Typography variant="h6" className="mb-4 flex items-center">
                    <Icon name="truck" className="w-5 h-5 mr-2 text-orange-500" />
                    {t('business:order.detail.shippingTimeline', 'Timeline vận chuyển')}
                  </Typography>
                  <div className="space-y-4">
                    {[
                      { status: ShippingStatusEnum.PENDING, icon: 'clock', color: 'yellow' },
                      { status: ShippingStatusEnum.PREPARING, icon: 'package', color: 'blue' },
                      { status: ShippingStatusEnum.SHIPPED, icon: 'truck', color: 'purple' },
                      { status: ShippingStatusEnum.IN_TRANSIT, icon: 'map-pin', color: 'orange' },
                      { status: ShippingStatusEnum.DELIVERED, icon: 'check-circle', color: 'green' }
                    ].map((step, index) => {
                      const isCompleted = Object.values(ShippingStatusEnum).indexOf(order.shippingStatus as ShippingStatusEnum) > index;
                      const isCurrent = order.shippingStatus === step.status;
                      const statusInfo = getShippingStatusInfo(step.status);

                      return (
                        <div key={step.status} className="flex items-center space-x-4">
                          <div className={`
                            w-10 h-10 rounded-full flex items-center justify-center border-2 transition-all
                            ${isCompleted
                              ? 'bg-green-500 border-green-500 text-white'
                              : isCurrent
                                ? 'bg-orange-500 border-orange-500 text-white'
                                : 'bg-gray-100 border-gray-300 text-gray-400'
                            }
                          `}>
                            <Icon name={step.icon as 'clock' | 'package' | 'truck' | 'map-pin' | 'check-circle'} className="w-5 h-5" />
                          </div>
                          <div className="flex-1">
                            <Typography variant="body1" className={`font-medium ${
                              isCompleted ? 'text-green-600' : isCurrent ? 'text-orange-600' : 'text-gray-500'
                            }`}>
                              {statusInfo.text}
                            </Typography>
                            {isCurrent && (
                              <Typography variant="body2" className="text-muted-foreground">
                                {formatTimestamp(order.updatedAt)}
                              </Typography>
                            )}
                          </div>
                        </div>
                      );
                    })}
                  </div>
                </div>
              )}
          </div>
        )}

        {/* Tab Payment */}
        {activeTab === 'payment' && (
          <div className="space-y-6">
            <Typography variant="h5" className="font-semibold flex items-center">
              <Icon name="credit-card" className="w-5 h-5 mr-2" />
              {t('business:order.detail.paymentDetails', 'Chi tiết thanh toán')}
            </Typography>
            <Card
              title={t('business:order.detail.paymentDetails', 'Chi tiết thanh toán')}
              icon={<Icon name="credit-card" className="w-5 h-5" />}
              variant="elevated"
              className="h-fit"
            >
              <div className="space-y-6">
                {/* Payment Summary */}
                <div className="bg-gradient-to-r from-primary/5 to-primary/10 rounded-lg p-6">
                  <div className="flex items-center justify-between mb-4">
                    <Typography variant="h6" className="font-semibold">
                      {t('business:order.detail.paymentSummary', 'Tóm tắt thanh toán')}
                    </Typography>
                    <Chip variant={paymentStatusInfo.variant} size="md">
                      {paymentStatusInfo.text}
                    </Chip>
                  </div>

                  <div className="space-y-3">
                    <div className="flex justify-between items-center">
                      <Typography variant="body2" className="text-muted-foreground">
                        {t('business:order.detail.subtotal', 'Tạm tính')}
                      </Typography>
                      <Typography variant="body1" className="font-medium">
                        {formatCurrency(order.billInfo?.subtotal || 0)}
                      </Typography>
                    </div>

                    <div className="flex justify-between items-center">
                      <Typography variant="body2" className="text-muted-foreground">
                        {t('business:order.detail.shippingFee', 'Phí vận chuyển')}
                      </Typography>
                      <Typography variant="body1" className="font-medium">
                        {formatCurrency(order.billInfo?.shippingFee || 0)}
                      </Typography>
                    </div>

                    {order.billInfo?.discount && order.billInfo.discount > 0 && (
                      <div className="flex justify-between items-center">
                        <Typography variant="body2" className="text-muted-foreground">
                          {t('business:order.detail.discount', 'Giảm giá')}
                        </Typography>
                        <Typography variant="body1" className="font-medium text-red-600">
                          -{formatCurrency(order.billInfo.discount)}
                        </Typography>
                      </div>
                    )}

                    <Divider />

                    <div className="flex justify-between items-center">
                      <Typography variant="h6" className="font-bold">
                        {t('business:order.detail.total', 'Tổng cộng')}
                      </Typography>
                      <Typography variant="h5" className="font-bold text-primary">
                        {formatCurrency(order.billInfo?.total || 0)}
                      </Typography>
                    </div>
                  </div>
                </div>

                {/* Payment Method */}
                <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
                  <Typography variant="body1" className="font-medium mb-2">
                    {t('business:order.detail.paymentMethod', 'Phương thức thanh toán')}
                  </Typography>
                  <div className="flex items-center space-x-3">
                    <div className="w-10 h-10 bg-blue-500/10 rounded-lg flex items-center justify-center">
                      <Icon name="credit-card" className="w-5 h-5 text-blue-500" />
                    </div>
                    <Typography variant="body1">
                      {getPaymentMethodText(order.billInfo?.paymentMethod as PaymentMethodEnum || PaymentMethodEnum.CASH)}
                    </Typography>
                  </div>
                </div>
              </div>
            </Card>
          </div>
        )}

        {/* Sidebar - Chỉ hiển thị khi ở tab overview */}
        {activeTab === 'overview' && (
          <div className="xl:col-span-1 space-y-6">
            {/* Quick Status Overview */}
            <Card
              title={t('business:order.detail.quickStatus', 'Trạng thái nhanh')}
              icon={<Icon name="activity" className="w-5 h-5" />}
              variant="elevated"
              className="h-fit"
            >
              <div className="space-y-4">
                <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
                  <div className="flex items-center justify-between mb-2">
                    <Typography variant="body2" className="text-muted-foreground">
                      {t('business:order.detail.orderStatus', 'Trạng thái đơn hàng')}
                    </Typography>
                    <Chip variant={orderStatusInfo.variant} size="sm">
                      {orderStatusInfo.text}
                    </Chip>
                  </div>
                  <Progress
                    value={getOrderProgress()}
                    color="primary"
                    size="md"
                    className="w-full max-w-md"
                  />
                </div>

                {order.hasShipping && (
                  <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
                    <div className="flex items-center justify-between mb-2">
                      <Typography variant="body2" className="text-muted-foreground">
                        {t('business:order.detail.shippingStatus', 'Trạng thái vận chuyển')}
                      </Typography>
                      <Chip variant={shippingStatusInfo.variant} size="sm">
                        {shippingStatusInfo.text}
                      </Chip>
                    </div>
                  </div>
                )}

                <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-4">
                  <div className="flex items-center justify-between">
                    <Typography variant="body2" className="text-muted-foreground">
                      {t('business:order.detail.paymentStatus', 'Trạng thái thanh toán')}
                    </Typography>
                    <Chip variant={paymentStatusInfo.variant} size="sm">
                      {paymentStatusInfo.text}
                    </Chip>
                  </div>
                </div>
              </div>
            </Card>

            {/* Payment Summary Card */}
            <Card
              title={t('business:order.detail.paymentSummary', 'Tóm tắt thanh toán')}
              icon={<Icon name="dollar-sign" className="w-5 h-5" />}
              variant="elevated"
              className="h-fit"
            >
              <div className="space-y-4">
                <div className="space-y-3">
                  <div className="flex justify-between items-center">
                    <Typography variant="body2" className="text-muted-foreground">
                      {t('business:order.detail.subtotal', 'Tạm tính')}
                    </Typography>
                    <Typography variant="body2" className="font-medium">
                      {formatCurrency(order.billInfo?.subtotal || 0)}
                    </Typography>
                  </div>

                  <div className="flex justify-between items-center">
                    <Typography variant="body2" className="text-muted-foreground">
                      {t('business:order.detail.shippingFee', 'Phí vận chuyển')}
                    </Typography>
                    <Typography variant="body2" className="font-medium">
                      {formatCurrency(order.billInfo?.shippingFee || 0)}
                    </Typography>
                  </div>

                  {order.billInfo?.discount && order.billInfo.discount > 0 && (
                    <div className="flex justify-between items-center">
                      <Typography variant="body2" className="text-muted-foreground">
                        {t('business:order.detail.discount', 'Giảm giá')}
                      </Typography>
                      <Typography variant="body2" className="font-medium text-red-600">
                        -{formatCurrency(order.billInfo.discount)}
                      </Typography>
                    </div>
                  )}
                </div>

                <Divider />

                <div className="bg-primary/5 rounded-lg p-4">
                  <div className="flex justify-between items-center">
                    <Typography variant="body1" className="font-semibold">
                      {t('business:order.detail.total', 'Tổng cộng')}
                    </Typography>
                    <Typography variant="h6" className="font-bold text-primary">
                      {formatCurrency(order.billInfo?.total || 0)}
                    </Typography>
                  </div>
                </div>

                <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3">
                  <Typography variant="body2" className="text-muted-foreground mb-1">
                    {t('business:order.detail.paymentMethod', 'Phương thức thanh toán')}
                  </Typography>
                  <div className="flex items-center space-x-2">
                    <Icon name="credit-card" className="w-4 h-4 text-blue-500" />
                    <Typography variant="body2" className="font-medium">
                      {getPaymentMethodText(order.billInfo?.paymentMethod as PaymentMethodEnum || PaymentMethodEnum.CASH)}
                    </Typography>
                  </div>
                </div>
              </div>
            </Card>

            {/* Customer Information */}
            {order.userConvertCustomer && (
              <Card
                title={t('business:order.detail.customer', 'Thông tin khách hàng')}
                icon={<Icon name="user" className="w-5 h-5" />}
                variant="elevated"
                className="h-fit"
              >
                <div className="space-y-4">
                  <div className="flex items-center space-x-4">
                    <Avatar
                      src={order.userConvertCustomer?.avatar || ''}
                      alt={order.userConvertCustomer?.name || 'Customer'}
                      size="xl"
                    />
                    <div className="flex-1">
                      <Typography variant="h6" className="font-semibold">
                        {order.userConvertCustomer?.name || t('business:order.detail.unknownCustomer', 'Không xác định')}
                      </Typography>
                      <Typography variant="body2" className="text-muted-foreground">
                        ID: #{order.userConvertCustomerId}
                      </Typography>
                    </div>
                  </div>

                  <div className="space-y-3">
                    {order.userConvertCustomer?.email?.primary && (
                      <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3">
                        <div className="flex items-center space-x-3">
                          <Icon name="mail" className="w-4 h-4 text-blue-500" />
                          <div>
                            <Typography variant="body2" className="text-muted-foreground">
                              Email
                            </Typography>
                            <Typography variant="body2" className="font-medium">
                              {order.userConvertCustomer.email.primary}
                            </Typography>
                            {order.userConvertCustomer.email.secondary && (
                              <Typography variant="body2" className="text-muted-foreground text-sm">
                                {order.userConvertCustomer.email.secondary}
                              </Typography>
                            )}
                          </div>
                        </div>
                      </div>
                    )}

                    {order.userConvertCustomer?.phone && (
                      <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3">
                        <div className="flex items-center space-x-3">
                          <Icon name="phone" className="w-4 h-4 text-green-500" />
                          <div>
                            <Typography variant="body2" className="text-muted-foreground">
                              {t('business:order.detail.phone', 'Số điện thoại')}
                            </Typography>
                            <Typography variant="body2" className="font-medium">
                              {order.userConvertCustomer?.phone}
                            </Typography>
                          </div>
                        </div>
                      </div>
                    )}
                  </div>
                </div>
              </Card>
            )}

            {/* Shipping Information */}
            {order.hasShipping && order.logisticInfo && (
              <Card
                title={t('business:order.detail.shipping', 'Thông tin vận chuyển')}
                icon={<Icon name="truck" className="w-5 h-5" />}
                variant="elevated"
                className="h-fit"
              >
              <div className="space-y-6">
                {/* Recipient Information */}
                <div className="bg-blue-50 dark:bg-blue-900/20 rounded-lg p-4 border border-blue-200 dark:border-blue-800">
                  <Typography variant="body1" className="font-semibold text-blue-900 dark:text-blue-100 mb-3 flex items-center">
                    <Icon name="user" className="w-4 h-4 mr-2" />
                    {t('business:order.detail.recipientInfo', 'Thông tin người nhận')}
                  </Typography>
                  <div className="space-y-2">
                    <div className="flex items-center space-x-2">
                      <Icon name="user" className="w-4 h-4 text-blue-600" />
                      <Typography variant="body2" className="font-medium">
                        {(order.logisticInfo as OrderLogisticInfo)?.recipientName || 'N/A'}
                      </Typography>
                    </div>
                    <div className="flex items-center space-x-2">
                      <Icon name="phone" className="w-4 h-4 text-blue-600" />
                      <Typography variant="body2">
                        {(order.logisticInfo as OrderLogisticInfo)?.recipientPhone || 'N/A'}
                      </Typography>
                    </div>
                    <div className="flex items-start space-x-2">
                      <Icon name="map-pin" className="w-4 h-4 text-blue-600 mt-0.5" />
                      <Typography variant="body2" className="break-words">
                        {(order.logisticInfo as OrderLogisticInfo)?.deliveryAddress || 'N/A'}
                      </Typography>
                    </div>
                  </div>
                </div>

                {/* Shipping Details */}
                <div className="space-y-3">
                  <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3">
                    <div className="flex items-center space-x-3">
                      <Icon name="truck" className="w-4 h-4 text-orange-500" />
                      <div>
                        <Typography variant="body2" className="text-muted-foreground">
                          {t('business:order.detail.carrier', 'Đơn vị vận chuyển')}
                        </Typography>
                        <Typography variant="body2" className="font-medium">
                          {(order.logisticInfo as OrderLogisticInfo)?.carrier || 'N/A'}
                        </Typography>
                      </div>
                    </div>
                  </div>

                  {(order.logisticInfo as OrderLogisticInfo)?.serviceType && (
                    <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3">
                      <div className="flex items-center space-x-3">
                        <Icon name="package" className="w-4 h-4 text-purple-500" />
                        <div>
                          <Typography variant="body2" className="text-muted-foreground">
                            {t('business:order.detail.serviceType', 'Loại dịch vụ')}
                          </Typography>
                          <Typography variant="body2" className="font-medium">
                            {(order.logisticInfo as OrderLogisticInfo).serviceType}
                          </Typography>
                        </div>
                      </div>
                    </div>
                  )}

                  <div className="bg-gray-50 dark:bg-gray-800/50 rounded-lg p-3">
                    <div className="flex items-center space-x-3">
                      <Icon name="dollar-sign" className="w-4 h-4 text-green-500" />
                      <div>
                        <Typography variant="body2" className="text-muted-foreground">
                          {t('business:order.detail.shippingFee', 'Phí vận chuyển')}
                        </Typography>
                        <Typography variant="body2" className="font-bold text-green-600">
                          {formatCurrency((order.logisticInfo as OrderLogisticInfo)?.shippingFee || 0)}
                        </Typography>
                      </div>
                    </div>
                  </div>
                </div>

                {/* Shipping Notes */}
                {(order.logisticInfo as OrderLogisticInfo)?.shippingNote && (
                  <div className="bg-yellow-50 dark:bg-yellow-900/20 rounded-lg p-4 border border-yellow-200 dark:border-yellow-800">
                    <Typography variant="body2" className="text-yellow-800 dark:text-yellow-200 font-medium mb-1 flex items-center">
                      <Icon name="message-circle" className="w-4 h-4 mr-2" />
                      {t('business:order.detail.shippingNote', 'Ghi chú vận chuyển')}
                    </Typography>
                    <Typography variant="body2" className="text-yellow-700 dark:text-yellow-300 italic">
                      "{(order.logisticInfo as OrderLogisticInfo)?.shippingNote}"
                    </Typography>
                  </div>
                )}

                {/* Shipping Errors */}
                {(order.logisticInfo as OrderLogisticInfo)?.shippingError && (
                  <Alert
                    type="error"
                    title={t('business:order.detail.shippingError', 'Lỗi vận chuyển')}
                    message={(order.logisticInfo as OrderLogisticInfo)?.shippingError || ''}
                    description={
                      (order.logisticInfo as OrderLogisticInfo)?.failedAt
                        ? `${t('business:order.detail.failedAt', 'Thời gian lỗi')}: ${formatTimestamp((order.logisticInfo as OrderLogisticInfo).failedAt)}`
                        : undefined
                    }
                    showIcon
                  />
                )}

                {(order.logisticInfo as OrderLogisticInfo)?.needsManualProcessing && (
                  <Alert
                    type="warning"
                    message={t('business:order.detail.needsManualProcessing', 'Cần xử lý thủ công')}
                    description="Đơn hàng này cần được xử lý thủ công bởi nhân viên."
                    showIcon
                  />
                )}
              </div>
            </Card>
          )}
          </div>
        )}
      </div>

      {/* Cancel Order Modal */}
      <Modal
        isOpen={showCancelModal}
        onClose={handleCloseCancelModal}
        title={t('business:order.cancelModal.title', 'Hủy đơn hàng')}
        size="md"
        footer={
          <div className="flex justify-end space-x-3">
            <Button
              variant="outline"
              onClick={handleCloseCancelModal}
              disabled={cancelOrderMutation.isPending}
            >
              {t('common:cancel', 'Hủy')}
            </Button>
            <Button
              variant="danger"
              onClick={handleConfirmCancel}
              disabled={cancelOrderMutation.isPending}
            >
              {cancelOrderMutation.isPending ? (
                <>
                  <Icon name="loader" className="w-4 h-4 mr-2 animate-spin" />
                  {t('business:order.cancelling', 'Đang hủy...')}
                </>
              ) : (
                <>
                  <Icon name="x-circle" className="w-4 h-4 mr-2" />
                  {t('business:order.confirmCancel', 'Xác nhận hủy')}
                </>
              )}
            </Button>
          </div>
        }
      >
        <div className="space-y-4">
          <div className="flex items-start space-x-3">
            <div className="flex-shrink-0">
              <div className="w-10 h-10 bg-red-100 dark:bg-red-900/20 rounded-full flex items-center justify-center">
                <Icon name="warning" className="w-5 h-5 text-red-600 dark:text-red-400" />
              </div>
            </div>
            <div className="flex-1">
              <Typography variant="body1" className="font-medium mb-2">
                {t('business:order.cancelModal.message', 'Bạn có chắc chắn muốn hủy đơn hàng này không?')}
              </Typography>
            </div>
          </div>

          <div className="space-y-2">
            <Typography variant="body2" className="font-medium">
              {t('business:order.cancelModal.reasonLabel', 'Lý do hủy đơn hàng')}
              <span className="text-muted-foreground ml-1">
                ({t('common:optional', 'Tùy chọn')})
              </span>
            </Typography>
            <Textarea
              value={cancelReason}
              onChange={(e) => setCancelReason(e.target.value)}
              placeholder={t('business:order.cancelModal.reasonPlaceholder', 'Nhập lý do hủy đơn hàng (tùy chọn)...')}
              rows={3}
              maxLength={500}
              showCount
              fullWidth
              disabled={cancelOrderMutation.isPending}
            />
          </div>
        </div>
      </Modal>
    </div>
  );
};

export default OrderDetailPage;
