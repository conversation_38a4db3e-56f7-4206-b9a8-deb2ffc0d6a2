import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { AudienceService } from '../services/audience.service';
import { Audience, AudienceQueryParams, AudienceStatus, AudienceType, ContactData, UpdateAudienceRequest } from '../types/audience.types';

/**
 * Interface cho tham số filter của audience hook
 */
interface AudienceFilterParams {
  page?: number;
  limit?: number;
  search?: string;
  status?: AudienceStatus | string;
  type?: AudienceType | string;
  [key: string]: unknown;
}

// Key cho React Query
const AUDIENCE_QUERY_KEY = 'audiences';

/**
 * Hook để lấy danh sách audience
 */
export const useAudiences = (params?: AudienceFilterParams) => {
  return useQuery({
    queryKey: [AUDIENCE_QUERY_KEY, params],
    queryFn: () => {
      // Chuyển đổi params từ AudienceFilterParams sang AudienceQueryParams
      if (!params) {
        return AudienceService.getAudiences(undefined);
      }

      const queryParams: Partial<AudienceQueryParams> = {};

      // Only include properties that have values
      if (params.page !== undefined) {
        queryParams.page = params.page;
      }

      if (params.limit !== undefined) {
        queryParams.limit = params.limit;
      }

      if (params.search && params.search !== '') {
        queryParams.search = params.search;
      }

      // Only include status if it has a value
      if (params.status && params.status !== '') {
        queryParams.status = params.status as AudienceStatus;
      }

      // Only include type if it has a value
      if (params.type && params.type !== '') {
        queryParams.type = params.type as AudienceType;
      }

      return AudienceService.getAudiences(queryParams as AudienceQueryParams);
    },
    select: data => {
      // Transform API response to match expected format
      if (data.result && data.result.data) {
        const contactData = data.result.data as ContactData[];
        // Transform ContactData to Audience format for compatibility
        const transformedItems = contactData.map((contact, index): Audience => {
          const audience: Audience = {
            id: Number(contact.id) || index + 1,
            name: contact.email || `Contact ${contact.id || index + 1}`,
            type: AudienceType.CUSTOMER,
            status: AudienceStatus.ACTIVE,
            totalContacts: 1,
            attributes: contact.customFields?.map((field, fieldIndex) => ({
              id: fieldIndex.toString(),
              name: field.name || `Field ${fieldIndex}`,
              value: field.value || '',
            })) || [],
            createdAt: contact.createdAt,
            updatedAt: contact.updatedAt,
          };

          // Only include optional fields if they have values
          if (contact.email) {
            audience.email = contact.email;
          }

          if (contact.phone) {
            audience.phone = contact.phone;
          }

          return audience;
        });

        return {
          items: transformedItems,
          meta: {
            totalItems: data.result.meta?.totalItems || contactData.length,
            itemCount: contactData.length,
            itemsPerPage: data.result.meta?.itemsPerPage || 10,
            totalPages: data.result.meta?.totalPages || Math.ceil(contactData.length / 10),
            currentPage: data.result.meta?.currentPage || 1,
          },
        };
      }

      return {
        items: [],
        meta: { totalItems: 0, itemCount: 0, itemsPerPage: 10, totalPages: 0, currentPage: 1 },
      };
    },
  });
};

/**
 * Hook để lấy chi tiết audience
 */
export const useAudience = (id: string) => {
  return useQuery({
    queryKey: [AUDIENCE_QUERY_KEY, id],
    queryFn: () => AudienceService.getAudienceById(Number(id)),
    enabled: !!id,
  });
};

/**
 * Hook để tạo audience mới
 */
export const useCreateAudience = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: Omit<Audience, 'id' | 'createdAt' | 'updatedAt' | 'totalContacts'>) =>
      AudienceService.createAudience(data),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [AUDIENCE_QUERY_KEY] });
    },
  });
};

/**
 * Hook để cập nhật audience
 */
export const useUpdateAudience = (id: string) => {
  const queryClient = useQueryClient();
  const numericId = Number(id);

  return useMutation({
    mutationFn: (data: UpdateAudienceRequest) => AudienceService.updateAudience(numericId, data),
    onSuccess: updatedAudience => {
      queryClient.setQueryData([AUDIENCE_QUERY_KEY, id], updatedAudience);
      queryClient.invalidateQueries({ queryKey: [AUDIENCE_QUERY_KEY] });
    },
  });
};

/**
 * Hook để xóa audience
 */
export const useDeleteAudience = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => AudienceService.deleteAudience(Number(id)),
    onSuccess: (_, id) => {
      queryClient.removeQueries({ queryKey: [AUDIENCE_QUERY_KEY, id] });
      queryClient.invalidateQueries({ queryKey: [AUDIENCE_QUERY_KEY] });
    },
  });
};

/**
 * Hook để xóa nhiều audience
 */
export const useDeleteMultipleAudiences = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (ids: number[]) => AudienceService.deleteMultipleAudiences(ids),
    onSuccess: () => {
      queryClient.invalidateQueries({ queryKey: [AUDIENCE_QUERY_KEY] });
    },
  });
};
