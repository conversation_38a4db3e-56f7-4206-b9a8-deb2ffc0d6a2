import { DashboardWidget, DashboardLayout, MenuItem } from '../types';

// Mock data service for dashboard
export class DashboardService {
  // Mock data for widgets
  static getMockWidgets(menuItem: MenuItem): DashboardWidget[] {
    const baseWidgets: DashboardWidget[] = [
      {
        id: `${menuItem.id}-overview`,
        title: `Tổng quan ${menuItem.title}`,
        type: 'metric',
        x: 0,
        y: 0,
        w: 12,
        h: 2,
        minW: 6,
        minH: 2,
        isEmpty: true
      },
      {
        id: `${menuItem.id}-chart`,
        title: `Biểu đồ ${menuItem.title}`,
        type: 'chart',
        x: 0,
        y: 2,
        w: 8,
        h: 4,
        minW: 6,
        minH: 3,
        isEmpty: true
      },
      {
        id: `${menuItem.id}-table`,
        title: `Bảng dữ liệu ${menuItem.title}`,
        type: 'table',
        x: 8,
        y: 2,
        w: 4,
        h: 4,
        minW: 4,
        minH: 3,
        isEmpty: true
      }
    ];

    return baseWidgets;
  }

  // Mock data for different widget types
  static getMockChartData(type: 'line' | 'bar' | 'pie' | 'area' = 'line') {
    switch (type) {
      case 'line':
        return {
          labels: ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'],
          datasets: [{
            label: 'Doanh thu',
            data: [12000, 19000, 15000, 25000, 22000, 30000],
            borderColor: 'rgb(59, 130, 246)',
            backgroundColor: 'rgba(59, 130, 246, 0.1)'
          }]
        };
      case 'bar':
        return {
          labels: ['Sản phẩm A', 'Sản phẩm B', 'Sản phẩm C', 'Sản phẩm D'],
          datasets: [{
            label: 'Số lượng bán',
            data: [120, 190, 150, 250],
            backgroundColor: ['#3B82F6', '#10B981', '#F59E0B', '#EF4444']
          }]
        };
      case 'pie':
        return {
          labels: ['Desktop', 'Mobile', 'Tablet'],
          datasets: [{
            data: [60, 30, 10],
            backgroundColor: ['#3B82F6', '#10B981', '#F59E0B']
          }]
        };
      default:
        return null;
    }
  }

  // Mock table data
  static getMockTableData() {
    return [
      { id: 1, name: 'Sản phẩm A', sales: 1200, revenue: '24,000,000 VND' },
      { id: 2, name: 'Sản phẩm B', sales: 950, revenue: '19,000,000 VND' },
      { id: 3, name: 'Sản phẩm C', sales: 800, revenue: '16,000,000 VND' },
      { id: 4, name: 'Sản phẩm D', sales: 650, revenue: '13,000,000 VND' },
      { id: 5, name: 'Sản phẩm E', sales: 500, revenue: '10,000,000 VND' }
    ];
  }

  // Mock metrics data
  static getMockMetrics() {
    return [
      {
        title: 'Tổng doanh thu',
        value: '125,000,000 VND',
        change: 12.5,
        changeType: 'increase' as const,
        icon: 'dollar-sign'
      },
      {
        title: 'Đơn hàng',
        value: '1,234',
        change: 8.2,
        changeType: 'increase' as const,
        icon: 'shopping-cart'
      },
      {
        title: 'Khách hàng mới',
        value: '456',
        change: -2.1,
        changeType: 'decrease' as const,
        icon: 'users'
      },
      {
        title: 'Tỷ lệ chuyển đổi',
        value: '3.2%',
        change: 0.5,
        changeType: 'increase' as const,
        icon: 'trending-up'
      }
    ];
  }

  // Save dashboard layout
  static async saveDashboardLayout(layout: DashboardLayout): Promise<boolean> {
    // Mock save operation
    console.log('Saving dashboard layout:', layout);
    return new Promise((resolve) => {
      setTimeout(() => resolve(true), 1000);
    });
  }

  // Load dashboard layout
  static async loadDashboardLayout(layoutId: string): Promise<DashboardLayout | null> {
    // Mock load operation
    console.log('Loading dashboard layout:', layoutId);
    return new Promise((resolve) => {
      setTimeout(() => resolve(null), 500);
    });
  }
}

export default DashboardService;
