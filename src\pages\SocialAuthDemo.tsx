import React, { useState } from 'react';
import { FaFacebook } from 'react-icons/fa';
import { SiGoogle, Si<PERSON>alo } from 'react-icons/si';
import { Card, Button, Typography } from '@/shared/components/common';
import { useGoogleAuthUrl, useFacebookAuthUrl, useZaloAuthUrl } from '@/modules/auth/hooks/useAuthQuery';
import { env } from '@/shared/utils';

/**
 * Demo page để test social authentication
 */
const SocialAuthDemo: React.FC = () => {
  const [logs, setLogs] = useState<string[]>([]);

  // Hooks for social login
  const googleAuthUrl = useGoogleAuthUrl();
  const facebookAuthUrl = useFacebookAuthUrl();
  const zaloAuthUrl = useZaloAuthUrl();

  const addLog = (message: string) => {
    setLogs(prev => [...prev, `${new Date().toLocaleTimeString()}: ${message}`]);
  };

  // Handle Google auth URL
  const handleGoogleAuthUrl = () => {
    addLog('Requesting Google auth URL...');
    googleAuthUrl.mutate(env.googleRedirectUri, {
      onSuccess: response => {
        addLog(`Google auth URL received: ${response.result?.url}`);
        if (response.result?.url) {
          addLog('Redirecting to Google...');
          window.location.href = response.result.url;
        }
      },
      onError: error => {
        addLog(`Google auth URL error: ${error.message}`);
      },
    });
  };

  // Handle Facebook auth URL
  const handleFacebookAuthUrl = () => {
    addLog('Requesting Facebook auth URL...');
    facebookAuthUrl.mutate(env.facebookRedirectUri, {
      onSuccess: response => {
        addLog(`Facebook auth URL received: ${response.result?.url}`);
        if (response.result?.url) {
          addLog('Redirecting to Facebook...');
          window.location.href = response.result.url;
        }
      },
      onError: error => {
        addLog(`Facebook auth URL error: ${error.message}`);
      },
    });
  };

  // Handle Zalo auth URL
  const handleZaloAuthUrl = () => {
    addLog('Requesting Zalo auth URL...');
    zaloAuthUrl.mutate(env.zaloRedirectUri, {
      onSuccess: response => {
        addLog(`Zalo auth URL received: ${response.result?.url}`);
        if (response.result?.url) {
          addLog('Redirecting to Zalo...');
          window.location.href = response.result.url;
        }
      },
      onError: error => {
        addLog(`Zalo auth URL error: ${error.message}`);
      },
    });
  };

  const clearLogs = () => {
    setLogs([]);
  };

  return (
    <div className="w-full bg-background text-foreground p-6">
      <div className="max-w-4xl mx-auto space-y-6">
        <Typography variant="h1" className="text-center">
          Social Authentication Demo
        </Typography>

        <div className="grid grid-cols-1 lg:grid-cols-2 gap-6">
          {/* Controls */}
          <Card className="p-6">
            <Typography variant="h2" className="mb-4">
              Test Social Auth URLs
            </Typography>

            <div className="space-y-4">
              <div>
                <Typography variant="h3" className="mb-2">
                  Environment Variables
                </Typography>
                <div className="text-sm space-y-1 bg-gray-100 dark:bg-gray-800 p-3 rounded">
                  <div>Google Redirect URI: <code>{env.googleRedirectUri}</code></div>
                  <div>Facebook Redirect URI: <code>{env.facebookRedirectUri}</code></div>
                  <div>Zalo Redirect URI: <code>{env.zaloRedirectUri}</code></div>
                </div>
              </div>

              <div className="space-y-3">
                <Button
                  variant="outline"
                  className="w-full flex items-center justify-center gap-2"
                  onClick={handleGoogleAuthUrl}
                  disabled={googleAuthUrl.isPending}
                >
                  <SiGoogle className="h-4 w-4 text-red-500" />
                  {googleAuthUrl.isPending ? 'Getting Google URL...' : 'Test Google Auth URL'}
                </Button>

                <Button
                  variant="outline"
                  className="w-full flex items-center justify-center gap-2"
                  onClick={handleFacebookAuthUrl}
                  disabled={facebookAuthUrl.isPending}
                >
                  <FaFacebook className="h-4 w-4 text-blue-600" />
                  {facebookAuthUrl.isPending ? 'Getting Facebook URL...' : 'Test Facebook Auth URL'}
                </Button>

                <Button
                  variant="outline"
                  className="w-full flex items-center justify-center gap-2"
                  onClick={handleZaloAuthUrl}
                  disabled={zaloAuthUrl.isPending}
                >
                  <SiZalo className="h-4 w-4 text-blue-500" />
                  {zaloAuthUrl.isPending ? 'Getting Zalo URL...' : 'Test Zalo Auth URL'}
                </Button>
              </div>
            </div>
          </Card>

          {/* Logs */}
          <Card className="p-6">
            <div className="flex items-center justify-between mb-4">
              <Typography variant="h2">
                Logs
              </Typography>
              <Button variant="ghost" size="sm" onClick={clearLogs}>
                Clear
              </Button>
            </div>

            <div className="bg-gray-100 dark:bg-gray-800 p-3 rounded h-64 overflow-y-auto">
              {logs.length === 0 ? (
                <div className="text-gray-500 text-sm">No logs yet...</div>
              ) : (
                <div className="space-y-1">
                  {logs.map((log, index) => (
                    <div key={index} className="text-sm font-mono">
                      {log}
                    </div>
                  ))}
                </div>
              )}
            </div>
          </Card>
        </div>

        {/* API Endpoints */}
        <Card className="p-6">
          <Typography variant="h2" className="mb-4">
            API Endpoints Being Called
          </Typography>
          <div className="space-y-2 text-sm">
            <div>
              <strong>Google:</strong> <code>GET /v1/auth/google/auth-url?redirectUri={env.googleRedirectUri}</code>
            </div>
            <div>
              <strong>Facebook:</strong> <code>GET /v1/auth/facebook/auth-url?redirectUri={env.facebookRedirectUri}</code>
            </div>
            <div>
              <strong>Zalo:</strong> <code>GET /v1/auth/zalo/auth-url?redirectUri={env.zaloRedirectUri}</code>
            </div>
          </div>
        </Card>
      </div>
    </div>
  );
};

export default SocialAuthDemo;
