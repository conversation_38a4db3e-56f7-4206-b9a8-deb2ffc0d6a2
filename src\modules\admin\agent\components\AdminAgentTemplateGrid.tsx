import React, { useMemo } from 'react';
import { useTranslation } from 'react-i18next';
import { EmptyState, Loading, Pagination } from '@/shared/components/common';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';
import AdminAgentTemplateCard from './AdminAgentTemplateCard';
import { useAdminAgentTemplates, useAdminAgentTemplatesTrash } from '../agent-template/hooks/useAgentTemplate';
import { AgentTemplateListItem, AgentTemplateQueryParams } from '../agent-template/types/agent-template.types';

interface AdminAgentTemplateGridProps {
  searchTerm: string;
  page: number;
  limit: number;
  onPageChange: (page: number) => void;
  onLimitChange: (limit: number) => void;
  isTrashPage?: boolean;
}

/**
 * Component hiển thị danh sách agent template dưới dạng grid
 */
const AdminAgentTemplateGrid: React.FC<AdminAgentTemplateGridProps> = ({
  searchTerm,
  page,
  limit,
  onPageChange,
  onLimitChange,
  isTrashPage = false,
}) => {
  const { t } = useTranslation(['admin', 'common']);

  // Query params
  const queryParams: AgentTemplateQueryParams = {
    page,
    limit,
    search: searchTerm || '',
  };

  // Lấy danh sách agent templates
  const { data: templatesResponse, isLoading, error, refetch } = isTrashPage
    ? useAdminAgentTemplatesTrash(queryParams)
    : useAdminAgentTemplates(queryParams);

  // Transform API data to component format
  const templates: AgentTemplateListItem[] = useMemo(() => {
    console.log('🔍 [AdminAgentTemplateGrid] Raw templatesResponse:', templatesResponse);

    if (!templatesResponse) {
      console.log('🔍 [AdminAgentTemplateGrid] No data available');
      return [];
    }

    const responseData = templatesResponse;
    console.log('🔍 [AdminAgentTemplateGrid] Response data:', responseData);

    if (!responseData?.items) {
      console.log('🔍 [AdminAgentTemplateGrid] No items in response data');
      return [];
    }

    const mappedTemplates = responseData.items.map((item: AgentTemplateListItem) => {
      const mapped = {
        id: item.id,
        name: item.name,
        avatar: item.avatar,
        typeId: item.typeId,
        typeName: item.typeName,
        modelId: item.modelId,
        isForSale: item.isForSale,
        createdAt: item.createdAt,
        updatedAt: item.updatedAt,
      };
      console.log('🔍 [AdminAgentTemplateGrid] Original item:', item);
      console.log('🔍 [AdminAgentTemplateGrid] Mapped template:', mapped);
      return mapped;
    });

    console.log('🔍 [AdminAgentTemplateGrid] Final templates:', mappedTemplates);
    return mappedTemplates;
  }, [templatesResponse]);

  const totalItems = useMemo(() => {
    if (!templatesResponse) return 0;
    const responseData = templatesResponse;
    return responseData?.meta?.totalItems || 0;
  }, [templatesResponse]);

  // Hiển thị loading
  if (isLoading) {
    return (
      <div className="flex justify-center items-center h-64">
        <Loading size="lg" />
      </div>
    );
  }

  // Hiển thị lỗi
  if (error) {
    return (
      <EmptyState
        icon="alert-triangle"
        title={t('admin:agent.template.loadError', 'Lỗi tải dữ liệu')}
        description={error.message || t('admin:agent.template.loadError', 'Không thể tải danh sách template')}
        actions={
          <button
            onClick={() => refetch()}
            className="px-4 py-2 bg-primary text-white rounded-md hover:bg-primary-dark"
          >
            {t('common:retry', 'Thử lại')}
          </button>
        }
      />
    );
  }

  // Hiển thị empty state
  if (templates.length === 0) {
    return (
      <EmptyState
        icon="template"
        title={
          searchTerm
            ? t('admin:agent.template.noSearchResults', 'Không tìm thấy template nào')
            : t('admin:agent.template.noTemplates', 'Chưa có template nào')
        }
        description={
          searchTerm
            ? t('admin:agent.template.noSearchResultsDescription', 'Thử thay đổi từ khóa tìm kiếm')
            : t('admin:agent.template.noTemplatesDescription', 'Hiện tại chưa có template nào trong hệ thống')
        }
      />
    );
  }

  return (
    <>
      {/* Grid hiển thị templates */}
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 2, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 1, lg: 1, xl: 2 }}
        gap={{ xs: 4, md: 5, lg: 6 }}
      >
        {templates.map(template => (
          <div key={template.id} className="h-full">
            <AdminAgentTemplateCard
              template={template}
              allTemplates={templates}
              isTrashPage={isTrashPage}
              onSuccess={refetch}
            />
          </div>
        ))}
      </ResponsiveGrid>

      {/* Pagination */}
      {totalItems > limit && (
        <div className="mt-6 flex justify-end">
          <Pagination
            currentPage={page}
            totalItems={totalItems}
            itemsPerPage={limit}
            onPageChange={onPageChange}
            onItemsPerPageChange={onLimitChange}
            itemsPerPageOptions={[10, 20, 50, 100]}
            showItemsPerPageSelector={true}
            showPageInfo={true}
            variant="compact"
            borderless={true}
          />
        </div>
      )}
    </>
  );
};

export default AdminAgentTemplateGrid;
