import { Loading } from '@/shared/components';
import AdminLayout from '@/shared/layouts/AdminLayout';
import { Suspense, lazy } from 'react';
import { RouteObject } from 'react-router-dom';

// Lazy load config pages
const SystemConfigPage = lazy(() => import('../pages/SystemConfigPage'));

/**
 * Admin Config module routes
 */
const configRouter: RouteObject[] = [
  {
    path: '/admin/config/system',
    element: (
      <AdminLayout title="Cấu hình hệ thống">
        <Suspense fallback={<Loading />}>
          <SystemConfigPage />
        </Suspense>
      </AdminLayout>
    ),
  },
];

export default configRouter;
