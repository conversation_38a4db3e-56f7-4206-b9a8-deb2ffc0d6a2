{"agent": {"management": {"title": "<PERSON><PERSON><PERSON><PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> lý tổng quan hệ thống <PERSON>"}, "notification": {"createSuccess": "{{entityName}} đ<PERSON> đ<PERSON><PERSON><PERSON> tạo thành công", "updateSuccess": "{{entityName}} đ<PERSON> đ<PERSON><PERSON><PERSON> cập nhật thành công", "deleteSuccess": "{{entityName}} đ<PERSON> đư<PERSON>c xóa thành công", "restoreSuccess": "{{entityName}} đ<PERSON> đ<PERSON><PERSON><PERSON> khôi phục thành công", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo {{entityName}}", "updateError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật {{entityName}}", "deleteError": "Có lỗi xảy ra khi xóa {{entityName}}", "restoreError": "<PERSON><PERSON> lỗi xảy ra khi khôi phục {{entityName}}", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách {{entityName}}", "uploadSuccess": "<PERSON><PERSON><PERSON> lên thành công", "uploadSuccessWithName": "<PERSON><PERSON><PERSON> lên {{fileName}} thành công", "uploadError": "<PERSON><PERSON> lỗi xảy ra khi tải lên", "validationError": "<PERSON><PERSON> li<PERSON><PERSON> không hợp lệ", "permissionError": "<PERSON><PERSON>n không có quyền thực hiện thao tác này", "networkError": "Lỗi kết nối mạng. <PERSON><PERSON> lòng thử lại", "processing": "<PERSON><PERSON> {{action}}..."}, "rank": {"title": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON>", "description": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> h<PERSON>", "pageTitle": "<PERSON><PERSON><PERSON><PERSON> <PERSON>", "addRank": "<PERSON><PERSON><PERSON><PERSON>", "editRank": "Chỉnh s<PERSON><PERSON>", "searchPlaceholder": "<PERSON><PERSON><PERSON> kiếm cấp bậc...", "noSearchResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy cấp bậc phù hợp với từ khóa tìm kiếm.", "createFirst": "<PERSON><PERSON><PERSON> đ<PERSON><PERSON> tiên", "sortBy": "<PERSON><PERSON><PERSON> xếp theo", "createSuccess": "<PERSON><PERSON><PERSON><PERSON> công", "createSuccessMessage": "<PERSON><PERSON><PERSON> bậc agent đ<PERSON> đ<PERSON><PERSON><PERSON> tạo thành công", "updateSuccess": "<PERSON><PERSON><PERSON><PERSON> công", "updateSuccessMessage": "<PERSON><PERSON><PERSON> bậc agent đ<PERSON> đ<PERSON><PERSON><PERSON> cập nhật thành công", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "editAction": "Chỉnh sửa", "deleteAction": "Xóa", "confirmDelete": "<PERSON><PERSON><PERSON> nhận x<PERSON><PERSON> cấp bậc", "deleteMessage": "Bạn có chắc chắn muốn xóa cấp bậc này không? Hành động này không thể hoàn tác.", "deleteSuccess": "<PERSON><PERSON><PERSON> c<PERSON>p b<PERSON>c thành công", "deleteError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi xóa cấp bậc", "list": {"title": "<PERSON><PERSON> <PERSON><PERSON>", "noRanks": "<PERSON><PERSON><PERSON><PERSON> có cấp b<PERSON>c n<PERSON>o", "noRanksDescription": "<PERSON><PERSON><PERSON> tại chưa có cấp bậc nào trong hệ thống.", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách cấp bậc. <PERSON><PERSON> lòng thử lại.", "loading": "<PERSON><PERSON> tải danh s<PERSON>ch cấp bậc...", "refreshing": "<PERSON><PERSON> làm mới dữ liệu..."}, "form": {"basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "name": "<PERSON><PERSON><PERSON> c<PERSON>p bậc", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên cấp bậc", "description": "<PERSON><PERSON>", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả cấp bậc", "expRange": "<PERSON><PERSON><PERSON><PERSON>h nghiệm", "minExp": "<PERSON><PERSON> nghiệm tối thiểu", "maxExp": "<PERSON><PERSON> nghiệm tối đa", "badge": "<PERSON><PERSON>", "badgeUpload": "<PERSON><PERSON><PERSON> lên huy hi<PERSON>u", "badgeHelp": "Hỗ trợ định dạng: JPG, PNG (chỉ 1 ảnh)", "currentBadge": "<PERSON><PERSON> <PERSON><PERSON><PERSON> hi<PERSON> tại", "currentBadgeNote": "<PERSON><PERSON><PERSON> lên <PERSON>nh mới để thay thế", "active": "<PERSON><PERSON><PERSON>", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON>", "creating": "<PERSON><PERSON> t<PERSON>o cấp bậc...", "createSuccess": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> b<PERSON>c thành công", "createError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi tạo cấp bậc", "updateError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật cấp bậc"}, "validation": {"nameRequired": "<PERSON><PERSON><PERSON> c<PERSON><PERSON> b<PERSON><PERSON> là bắt buộc", "descriptionRequired": "<PERSON><PERSON> tả là b<PERSON><PERSON> buộc", "minExpInvalid": "<PERSON><PERSON> nghiệm tối thiểu phải >= 0", "minExpInteger": "<PERSON><PERSON> nghiệm tối thiểu phải là số nguyên", "maxExpInvalid": "<PERSON><PERSON> nghiệm tối đa phải > 0", "maxExpInteger": "<PERSON><PERSON> nghiệm tối đa phải là số nguyên", "expRangeInvalid": "<PERSON><PERSON> nghiệm tối đa phải lớn hơn kinh nghiệm tối thiểu", "expRangeOverlap": "<PERSON><PERSON><PERSON><PERSON> kinh nghiệm chồng chéo với khoảng kinh nghiệm của cấp b<PERSON><PERSON> kh<PERSON>c"}, "edit": {"notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy cấp bậc"}, "sort": {"name": "<PERSON><PERSON><PERSON>", "minExp": "<PERSON><PERSON> nghiệm tối thiểu", "maxExp": "<PERSON><PERSON> nghiệm tối đa", "createdAt": "<PERSON><PERSON><PERSON>"}}, "system": {"title": "Q<PERSON>ản lý System Agent", "description": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON> h<PERSON> thống", "pageTitle": "Q<PERSON>ản lý System Agent", "addAgent": "Thêm Agent mới", "editAgent": "Chỉnh sửa Agent System", "searchPlaceholder": "<PERSON><PERSON><PERSON> Agent...", "noSearchResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy Agent phù hợp với từ khóa tìm kiếm.", "createFirst": "Tạo System Agent đ<PERSON>u tiên", "viewTrash": "<PERSON><PERSON> th<PERSON>", "backToMain": "Quay lại danh s<PERSON>ch ch<PERSON>h", "createSuccess": "<PERSON><PERSON><PERSON><PERSON> công", "createSuccessMessage": "Agent system đã đư<PERSON><PERSON> tạo thành công", "updateSuccess": "<PERSON><PERSON><PERSON><PERSON> công", "cancel": "<PERSON><PERSON><PERSON>", "updateAgent": "<PERSON><PERSON><PERSON>", "updateSuccessMessage": "Agent system đã đ<PERSON><PERSON><PERSON> cập nhật thành công"}, "user": {"title": "Admin - Agent User", "description": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON> ng<PERSON><PERSON> dùng"}, "type": {"title": "<PERSON><PERSON><PERSON><PERSON> lý <PERSON> Agent", "description": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "pageTitle": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "addType": "<PERSON><PERSON><PERSON><PERSON> Agent mới", "editType": "Chỉnh s<PERSON><PERSON>", "searchPlaceholder": "<PERSON><PERSON><PERSON> k<PERSON> lo<PERSON> agent...", "createFirst": "<PERSON><PERSON><PERSON> Agent đ<PERSON><PERSON> tiên", "createSuccess": "<PERSON><PERSON><PERSON><PERSON> công", "createSuccessMessage": "Loại agent đ<PERSON> đ<PERSON><PERSON><PERSON> tạo thành công", "updateSuccess": "<PERSON><PERSON><PERSON><PERSON> công", "updateSuccessMessage": "Loại agent đ<PERSON> đ<PERSON><PERSON><PERSON> cập nhật thành công", "cancel": "<PERSON><PERSON><PERSON>", "updateType": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "viewTrash": "<PERSON><PERSON> th<PERSON>", "backToMain": "Quay lại danh s<PERSON>ch ch<PERSON>h", "noSearchResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy loại agent phù hợp với từ khóa tìm kiếm.", "deleteConfirmTitle": "<PERSON><PERSON><PERSON>n x<PERSON><PERSON> lo<PERSON> agent", "deleteConfirmMessage": "Bạn có chắc chắn muốn xóa loại agent nà<PERSON>? Hành động này sẽ chuyển loại agent vào thùng rác và có thể khôi phục lại.", "deleteSuccess": "<PERSON><PERSON><PERSON> thành công", "deleteSuccessMessage": "Loại agent đ<PERSON> đ<PERSON><PERSON><PERSON> xóa thành công", "restoreSuccess": "<PERSON><PERSON><PERSON><PERSON> phục thành công", "restoreSuccessMessage": "Loại agent đ<PERSON> đ<PERSON><PERSON><PERSON> khôi phục thành công", "selectTypeToDelete": "Chọn loại agent đ<PERSON>a", "selectTypeToDeleteDescription": "Chọn loại agent mà bạn muốn xóa. Loại agent sẽ được chuyển vào thùng rác và có thể khôi phục lại.", "deleteWithMigration": "<PERSON>óa với chuyển đổi", "deleteWithMigrationDescription": "Xóa loại agent v<PERSON> chuyển tất cả agents thu<PERSON><PERSON> lo<PERSON> này sang loại mới đư<PERSON><PERSON> chọn.", "newTypeAgent": "Loại agent mới", "selectNewType": "Chọn loại agent mới", "selectNewTypeDescription": "Chọn loại agent mớ<PERSON> để chuyển đổi các agents hi<PERSON><PERSON> tại.", "noAvailableTypes": "<PERSON>hông có loại agent n<PERSON><PERSON> kh<PERSON>c để chuyển đổi. Bạn chỉ có thể xóa mà không chuyển đổi.", "deleteOnly": "Chỉ xóa", "deleteError": "<PERSON><PERSON> lỗi xảy ra khi xóa loại agent", "restoreError": "<PERSON><PERSON> lỗi xảy ra khi khôi phục loại agent"}, "strategy": {"title": "<PERSON><PERSON><PERSON>n lý Agent Strategy", "description": "<PERSON><PERSON><PERSON><PERSON> lý chiế<PERSON> l<PERSON> agent", "pageTitle": "<PERSON><PERSON><PERSON><PERSON> l<PERSON> l<PERSON>", "addStrategy": "<PERSON><PERSON><PERSON><PERSON> mới", "editStrategy": "Chỉnh s<PERSON>a <PERSON>", "searchPlaceholder": "<PERSON><PERSON><PERSON> kiếm chiến l<PERSON>...", "noSearchResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy chiến lư<PERSON><PERSON> phù hợp với từ khóa tìm kiếm.", "createFirst": "<PERSON><PERSON><PERSON> l<PERSON> Agent đ<PERSON><PERSON> tiên", "createSuccess": "<PERSON><PERSON><PERSON><PERSON> công", "createSuccessMessage": "Chiến lược agent đ<PERSON> đ<PERSON><PERSON><PERSON> tạo thành công", "updateSuccess": "<PERSON><PERSON><PERSON><PERSON> công", "updateSuccessMessage": "Chiến lược agent đ<PERSON> đ<PERSON><PERSON><PERSON> cập nhật thành công", "cancel": "<PERSON><PERSON><PERSON>", "updateStrategy": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "viewTrash": "<PERSON><PERSON> th<PERSON>", "backToMain": "Quay lại danh s<PERSON>ch ch<PERSON>h", "list": {"title": "<PERSON><PERSON> <PERSON><PERSON><PERSON>", "noStrategies": "<PERSON><PERSON><PERSON><PERSON> có chiến l<PERSON> n<PERSON>o", "noStrategiesDescription": "<PERSON><PERSON><PERSON> tại chưa có chiến lư<PERSON><PERSON> nào trong hệ thống.", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách chiến lư<PERSON><PERSON>. <PERSON><PERSON> lòng thử lại.", "loading": "<PERSON><PERSON> tải danh s<PERSON>ch chiến l<PERSON>...", "refreshing": "<PERSON><PERSON> làm mới dữ liệu..."}, "card": {"edit": "Chỉnh sửa", "delete": "Xóa", "restore": "<PERSON><PERSON><PERSON><PERSON>h<PERSON>c", "confirmDelete": "<PERSON><PERSON><PERSON> nh<PERSON>n x<PERSON><PERSON> l<PERSON>", "deleteMessage": "Bạn có chắc chắn muốn xóa chiến lược này không? Hành động này không thể hoàn tác.", "deleteSuccess": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> thành công", "deleteError": "<PERSON><PERSON> lỗi xảy ra khi xóa chiến lư<PERSON>c", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t chi<PERSON>n lư<PERSON><PERSON> thành công", "updateError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật chiến lược", "restoreSuccess": "<PERSON><PERSON><PERSON><PERSON> ph<PERSON><PERSON> chiến lư<PERSON><PERSON> thành công", "restoreError": "<PERSON><PERSON> lỗi xảy ra khi khôi phục chiến lư<PERSON>c"}, "form": {"basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "name": "<PERSON><PERSON><PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên chi<PERSON>", "avatar": "Avatar", "avatarUpload": "<PERSON><PERSON><PERSON> lên avatar", "avatarHelp": "Hỗ trợ định dạng: JPG, PNG (chỉ 1 ảnh)", "modelConfig": "<PERSON><PERSON><PERSON>", "temperature": "Temperature", "maxTokens": "<PERSON>", "instruction": "Hướng dẫn", "instructionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> hướ<PERSON> dẫn cho chiến lược", "content": "<PERSON><PERSON><PERSON> dung c<PERSON>c b<PERSON>", "contentStep": "<PERSON><PERSON><PERSON><PERSON> {step}", "contentPlaceholder": "<PERSON><PERSON><PERSON><PERSON> n<PERSON>i dung bước {step}", "addStep": "<PERSON><PERSON><PERSON><PERSON>", "removeStep": "<PERSON><PERSON><PERSON>", "exampleDefault": "<PERSON><PERSON> dụ mặc định", "exampleStep": "<PERSON><PERSON> dụ {step}", "examplePlaceholder": "<PERSON><PERSON><PERSON><PERSON> ví dụ cho bước {step}", "addExample": "Thêm ví dụ", "removeExample": "Xóa ví dụ", "systemModel": "System Model", "provider": "Loại Provider", "selectProvider": "Chọn provider", "selectProviderFirst": "V<PERSON> lòng chọn provider trước", "model": "Model", "selectSystemModel": "Chọn system model", "selectModel": "<PERSON><PERSON><PERSON> model", "create": "<PERSON><PERSON><PERSON>", "update": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "creating": "<PERSON><PERSON> tạo chi<PERSON> l<PERSON>...", "updating": "<PERSON><PERSON> cập nhật chiế<PERSON> l<PERSON>...", "createSuccess": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> thành công", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo chiến lư<PERSON>c", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t chi<PERSON>n lư<PERSON><PERSON> thành công", "updateError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật chiến lược", "uploadingAvatar": "<PERSON><PERSON> tải lên avatar...", "uploadAvatarSuccess": "<PERSON><PERSON><PERSON> lên avatar thành công", "uploadAvatarError": "<PERSON><PERSON> lỗi xảy ra khi tải lên avatar", "loadSystemModelsError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách system models", "currentAvatar": "Avatar hi<PERSON>n tại"}, "trash": {"title": "<PERSON><PERSON><PERSON><PERSON> - <PERSON><PERSON><PERSON>", "noStrategies": "<PERSON><PERSON><PERSON><PERSON> có chiến lư<PERSON>c nào trong thùng rác", "noStrategiesDescription": "<PERSON>h<PERSON><PERSON> rác trống. <PERSON><PERSON><PERSON> chiến lược đã xóa sẽ xuất hiện ở đây.", "noSearchResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy chiến lư<PERSON><PERSON> nào phù hợp", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách chiến lư<PERSON><PERSON> đã xóa"}, "validation": {"nameRequired": "<PERSON><PERSON><PERSON> ch<PERSON><PERSON><PERSON> l<PERSON><PERSON><PERSON> là bắt buộc", "instructionRequired": "Hướng dẫn là bắt buộc", "systemModelRequired": "System model l<PERSON> b<PERSON><PERSON> bu<PERSON>c", "providerRequired": "<PERSON>ui lòng chọn provider", "contentRequired": "<PERSON>ộ<PERSON> dung các b<PERSON><PERSON><PERSON> là bắt buộc", "contentStepRequired": "<PERSON><PERSON><PERSON> dung bước {step} là bắt buộc", "exampleRequired": "<PERSON><PERSON> dụ mặc định là bắt buộc", "exampleStepRequired": "<PERSON><PERSON> dụ bước {step} là bắt buộc"}}, "trash": {"title": "Thùng rác - Loại Agent", "noAgents": "<PERSON><PERSON><PERSON>ng có loại agent nào trong thùng rác", "noAgentsDescription": "<PERSON>h<PERSON><PERSON> rác trống. Các lo<PERSON>i agent đã xóa sẽ xuất hiện ở đây.", "restoreAgent": "Khôi phục lo<PERSON>i agent", "permanentDelete": "<PERSON>ó<PERSON> v<PERSON><PERSON> vi<PERSON>n"}, "list": {"title": "<PERSON><PERSON>", "noTypes": "<PERSON><PERSON><PERSON>ng có loại agent nào", "noTypesDescription": "<PERSON><PERSON><PERSON> tại chưa có loại agent n<PERSON><PERSON> trong hệ thống.", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách loại agent. <PERSON><PERSON> lòng thử lại.", "loading": "<PERSON><PERSON> tải danh s<PERSON>ch lo<PERSON> agent...", "refreshing": "<PERSON><PERSON> làm mới dữ liệu..."}, "template": {"title": "Agent Template", "description": "<PERSON><PERSON><PERSON><PERSON> lý các template agent có sẵn", "pageTitle": "<PERSON><PERSON><PERSON><PERSON> l<PERSON>", "noTemplates": "Chưa có template nào", "noTemplatesDescription": "<PERSON><PERSON><PERSON> tại chưa có template nào trong hệ thống.", "noSearchResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy template nào phù hợp", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách template. <PERSON><PERSON> lòng thử lại.", "loading": "<PERSON><PERSON> tải danh sách template...", "refreshing": "<PERSON><PERSON> làm mới dữ liệu...", "viewTrash": "<PERSON><PERSON> th<PERSON>", "backToMain": "Quay lại danh s<PERSON>ch ch<PERSON>h", "card": {"model": "Model", "forSale": "Hỗ trợ bán", "supported": "Có hỗ trợ", "notSupported": "Không hỗ trợ", "delete": "Xóa", "restore": "<PERSON><PERSON><PERSON><PERSON>h<PERSON>c", "confirmDelete": "<PERSON><PERSON><PERSON>n x<PERSON><PERSON>", "deleteMessage": "Bạn có chắc chắn muốn xóa template này không? Hành động này không thể hoàn tác.", "deleteSuccess": "Xóa template thành công", "deleteError": "Có lỗi xảy ra khi xóa template", "restoreSuccess": "<PERSON><PERSON><PERSON><PERSON> phục template thành công", "restoreError": "Có lỗi xảy ra khi khôi phục template"}, "trash": {"title": "<PERSON><PERSON><PERSON><PERSON> - Agent Template", "noTemplates": "<PERSON><PERSON><PERSON>ng có template nào trong thùng rác", "noTemplatesDescription": "Th<PERSON><PERSON> rác trống. Các template đã xóa sẽ xuất hiện ở đây.", "noSearchResults": "<PERSON><PERSON><PERSON><PERSON> tìm thấy template nào phù hợp", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách template đã xóa"}}, "card": {"edit": "Chỉnh sửa", "delete": "Xóa"}, "form": {"basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "name": "<PERSON><PERSON><PERSON>", "nameCode": "<PERSON><PERSON> đ<PERSON>nh danh", "nameCodePlaceholder": "<PERSON><PERSON><PERSON><PERSON> mã định danh", "instruction": "Hướng dẫn", "instructionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> dẫn cho loại agent", "avatar": "Avatar", "avatarUpload": "<PERSON><PERSON><PERSON> lên avatar", "avatarHelp": "Hỗ trợ định dạng: JPG, PNG (chỉ 1 ảnh)", "currentAvatar": "Avatar hi<PERSON>n tại", "currentAvatarNote": "<PERSON><PERSON><PERSON> lên <PERSON>nh mới để thay thế", "modelConfig": "<PERSON><PERSON><PERSON>", "temperature": "Temperature", "topP": "Top P", "topK": "Top K", "maxTokens": "<PERSON>", "provider": "Loại Provider", "resources": "<PERSON><PERSON><PERSON>", "model": "Model", "selectModel": "<PERSON><PERSON><PERSON> model", "vectorStore": "Vector Store", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên <PERSON> agent", "description": "<PERSON><PERSON>", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả lo<PERSON>i agent", "defaultConfig": "<PERSON><PERSON><PERSON> hình mặc định", "enableAgentProfileCustomization": "<PERSON> phép tùy chỉnh hồ sơ agent", "enableOutputToMessenger": "<PERSON> phép xuất ra Messenger", "enableOutputToWebsiteLiveChat": "Cho phép xuất ra Website Live Chat", "enableTaskConversionTracking": "<PERSON> chuyển đổi tác vụ", "enableResourceUsage": "Sử dụng tài nguyên", "enableDynamicStrategyExecution": "<PERSON><PERSON><PERSON><PERSON> thi chiến l<PERSON><PERSON><PERSON> động", "enableMultiAgentCollaboration": "<PERSON><PERSON><PERSON> t<PERSON>c đa agent", "enableOutputToZaloOA": "Cho phép xuất ra Zalo OA", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "selectStatus": "<PERSON><PERSON><PERSON> trạng thái", "draft": "Nháp", "approved": "Đ<PERSON>", "agentSystems": "Agent Systems", "selectAgentSystems": "Chọn agent systems", "agentSystemsConfig": {"title": "Agent Systems", "noSystemsSelected": "Chưa có agent system nào đ<PERSON><PERSON><PERSON> chọn", "systemCount": "<PERSON><PERSON> chọn {{count}} agent system", "addSystem": "Thêm Agent System", "selectedSystems": "Agent Systems đã chọn", "removeSystem": "Xóa agent system", "removeSystemSuccess": "Xóa agent system thành công", "removeSystemError": "Có lỗi xảy ra khi xóa agent system", "confirmDeleteSystem": "Bạn có chắc chắn muốn xóa agent system \"{{systemName}\" khỏi loại agent không?", "deleteSystemWarning": "<PERSON><PERSON><PERSON> động này không thể hoàn tác.", "createdAt": "<PERSON><PERSON><PERSON>"}, "agentSystemSlideIn": {"title": "Chọn Agent System", "close": "Đ<PERSON><PERSON>", "cancel": "<PERSON><PERSON><PERSON>", "save": "<PERSON><PERSON><PERSON>", "system": "<PERSON><PERSON> th<PERSON>", "status": "<PERSON><PERSON><PERSON><PERSON> thái", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "createdAt": "<PERSON><PERSON><PERSON>", "filterBy": "<PERSON><PERSON><PERSON> theo", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "updateSystemsSuccess": "Cập nhật agent systems thành công", "updateSystemsError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật agent systems", "addSystemsToListSuccess": "Thêm agent systems vào danh sách thành công", "cannotSaveInThisMode": "<PERSON><PERSON><PERSON><PERSON> thể lưu ở chế độ này"}, "create": "Tạo <PERSON>i Agent", "creating": "<PERSON><PERSON> tạo loại agent...", "createSuccess": "Tạo loại agent thà<PERSON> công", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo lo<PERSON>i agent", "updating": "<PERSON><PERSON> cập nh<PERSON>t lo<PERSON> agent...", "loadAgentSystemsError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách agent systems"}, "validation": {"nameRequired": "Tên loại agent l<PERSON> b<PERSON><PERSON> b<PERSON>", "descriptionRequired": "<PERSON><PERSON> tả là b<PERSON><PERSON> buộc", "statusRequired": "<PERSON>r<PERSON><PERSON> thái là bắ<PERSON> bu<PERSON>c", "agentSystemsRequired": "Ít nhất một agent system là bắt buộc"}, "common": {"confirmDelete": "<PERSON><PERSON><PERSON>n x<PERSON>a", "cancel": "<PERSON><PERSON><PERSON>", "delete": "Xóa", "error": "Lỗi", "locale": "vi-VN", "success": "<PERSON><PERSON><PERSON><PERSON> công", "loading": "<PERSON><PERSON> tả<PERSON>...", "save": "<PERSON><PERSON><PERSON>", "close": "Đ<PERSON><PERSON>", "edit": "Chỉnh sửa", "view": "Xem", "back": "Quay lại", "next": "<PERSON><PERSON><PERSON><PERSON> theo", "previous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "search": "<PERSON><PERSON><PERSON>", "filter": "<PERSON><PERSON><PERSON>", "all": "<PERSON><PERSON><PERSON> c<PERSON>", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "draft": "Nháp", "approved": "Đ<PERSON>", "create": "Tạo", "update": "<PERSON><PERSON><PERSON>", "refresh": "<PERSON><PERSON><PERSON>", "restore": "<PERSON><PERSON><PERSON><PERSON>h<PERSON>c"}, "deleteConfirmTitle": "<PERSON><PERSON><PERSON>n x<PERSON><PERSON> lo<PERSON> agent", "deleteConfirmMessage": "Bạn có chắc chắn muốn xóa loại agent nà<PERSON>? Hành động này sẽ chuyển loại agent vào thùng rác và có thể khôi phục lại.", "deleteSuccess": "<PERSON><PERSON><PERSON> thành công", "deleteSuccessMessage": "Loại agent đ<PERSON> đ<PERSON><PERSON><PERSON> xóa thành công", "deleteError": "<PERSON><PERSON> lỗi xảy ra khi xóa loại agent", "selectTypeToDelete": "Chọn loại agent đ<PERSON>a", "deleteWithMigration": "<PERSON>óa với chuyển đổi", "deleteWithMigrationDescription": "Xóa loại agent v<PERSON> chuyển tất cả agents thu<PERSON><PERSON> lo<PERSON> này sang loại mới đư<PERSON><PERSON> chọn.", "newTypeAgent": "Loại agent mới", "selectNewType": "Chọn loại agent mới", "selectNewTypeDescription": "Chọn loại agent mớ<PERSON> để chuyển đổi các agents hi<PERSON><PERSON> tại.", "noAvailableTypes": "<PERSON>hông có loại agent n<PERSON><PERSON> kh<PERSON>c để chuyển đổi. Bạn chỉ có thể xóa mà không chuyển đổi.", "deleteOnly": "Chỉ xóa"}, "list": {"title": "<PERSON><PERSON>", "noAgents": "K<PERSON>ông có Agent nào", "noAgentsDescription": "<PERSON><PERSON><PERSON> tại chưa có Agent nào trong hệ thống.", "loadError": "<PERSON><PERSON><PERSON><PERSON> thể tải danh sách Agent. <PERSON><PERSON> lòng thử lại.", "loading": "<PERSON><PERSON> tải danh s<PERSON>ch Agent...", "refreshing": "<PERSON><PERSON> làm mới dữ liệu..."}, "card": {"supervisor": "<PERSON><PERSON><PERSON><PERSON> s<PERSON>t viên", "active": "<PERSON><PERSON><PERSON> đ<PERSON>", "inactive": "<PERSON><PERSON><PERSON><PERSON> hoạt động", "activate": "<PERSON><PERSON><PERSON>", "deactivate": "<PERSON><PERSON> hi<PERSON> h<PERSON>a", "edit": "Chỉnh sửa", "delete": "Xóa", "restore": "<PERSON><PERSON><PERSON><PERSON>h<PERSON>c", "confirmDelete": "<PERSON><PERSON><PERSON>n x<PERSON>a <PERSON>", "deleteMessage": "Bạn có chắc chắn muốn xóa Agent này không? Hành động này không thể hoàn tác.", "deleteSuccess": "Xóa Agent thành công", "deleteError": "Có lỗi xảy ra khi xóa Agent", "updateSuccess": "<PERSON><PERSON><PERSON> nh<PERSON>t Agent th<PERSON><PERSON> công", "updateError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi cập nhật Agent", "setSupervisor": "Set làm Supervisor", "removeSupervisor": "Bỏ quyền Supervisor", "setSupervisorSuccess": "<PERSON><PERSON> set làm supervisor th<PERSON><PERSON> công", "removeSupervisorSuccess": "<PERSON><PERSON> bỏ quyền supervisor thà<PERSON> công", "supervisorError": "<PERSON><PERSON> lỗi x<PERSON>y ra khi thay đổi quyền supervisor", "restoreSuccess": "<PERSON><PERSON> khôi phục agent thà<PERSON> công", "restoreError": "<PERSON><PERSON> lỗi xảy ra khi khôi phục agent"}, "trash": {"noAgents": "Không có agent nào trong thùng rác", "noAgentsDescription": "<PERSON>h<PERSON><PERSON> rác trống. Các agent đã xóa sẽ xuất hiện ở đây."}, "edit": {"notFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy agent"}, "pagination": {"itemsPerPage": "<PERSON><PERSON> mục mỗi trang", "showingItems": "<PERSON><PERSON>n thị {from} - {to} trong tổng số {total} mục", "page": "<PERSON><PERSON>", "of": "c<PERSON>a", "previous": "<PERSON><PERSON><PERSON><PERSON><PERSON>", "next": "<PERSON><PERSON><PERSON><PERSON> theo"}, "form": {"basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "name": "<PERSON><PERSON>n <PERSON>", "namePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên agent", "nameCode": "<PERSON><PERSON> đ<PERSON>nh danh", "nameCodePlaceholder": "<PERSON><PERSON><PERSON><PERSON> mã định danh", "instruction": "Hướng dẫn", "instructionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> dẫn cho agent", "description": "<PERSON><PERSON>", "descriptionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> mô tả agent", "avatar": "Avatar", "avatarUpload": "<PERSON><PERSON><PERSON> lên avatar", "avatarHelp": "Hỗ trợ định dạng: JPG, PNG (chỉ 1 ảnh)", "modelConfig": "<PERSON><PERSON><PERSON>", "temperature": "Temperature", "topP": "Top P", "topK": "Top K", "maxTokens": "<PERSON>", "provider": "Loại Provider", "resources": "<PERSON><PERSON><PERSON>", "model": "Model", "selectModel": "<PERSON><PERSON><PERSON> model", "vectorStore": "Vector Store", "selectVectorStore": "Chọn vector store", "isSupervisor": "Là Supervisor", "create": "Tạo Agent", "creating": "Đang tạo Agent...", "createSuccess": "Tạo Agent thành công", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo Agent", "uploadingAvatar": "<PERSON><PERSON> tải lên avatar...", "uploadAvatarSuccess": "<PERSON><PERSON><PERSON> lên avatar thành công", "uploadAvatarError": "<PERSON><PERSON> lỗi xảy ra khi tải lên avatar"}, "validation": {"nameRequired": "Tên agent <PERSON><PERSON><PERSON><PERSON>", "nameCodeRequired": "<PERSON><PERSON> đ<PERSON>nh danh là b<PERSON><PERSON> buộc", "nameCodeFormat": "<PERSON><PERSON> định danh chỉ đư<PERSON><PERSON> chứa chữ thường, s<PERSON>, dấu gạch dưới và dấu gạch ngang", "instructionRequired": "Hướng dẫn là bắt buộc", "descriptionRequired": "<PERSON><PERSON> tả là b<PERSON><PERSON> buộc", "modelRequired": "<PERSON> <PERSON><PERSON> b<PERSON><PERSON> bu<PERSON>", "modelIdInvalid": "Model ID phải là UUID hợp lệ"}, "agentTemplate": {"addAgentTemplate": "Thêm Agent Template", "selectType": "<PERSON><PERSON><PERSON>", "selectTypeDescription": "Chọn loại agent ph<PERSON> hợp cho template của bạn", "basicInfo": "<PERSON><PERSON><PERSON><PERSON> tin cơ bản", "configuration": "<PERSON><PERSON><PERSON> h<PERSON>nh", "agentName": "<PERSON><PERSON>n <PERSON>", "agentNamePlaceholder": "<PERSON><PERSON><PERSON><PERSON> tên agent template", "selectedType": "<PERSON><PERSON>i đã chọn", "pleaseSelectType": "<PERSON><PERSON> lòng chọn lo<PERSON> agent", "createSuccess": "Tạo agent template thành công", "createError": "<PERSON><PERSON> lỗi xảy ra khi tạo agent template", "createdAt": "<PERSON><PERSON><PERSON>", "noTypeAgents": "<PERSON><PERSON><PERSON>ng có loại agent nào", "modelConfig": "<PERSON><PERSON><PERSON>", "profileConfig": "<PERSON><PERSON><PERSON>", "convertConfig": "<PERSON><PERSON><PERSON> h<PERSON>nh <PERSON> đổi", "provider": "Provider", "model": "Model", "instruction": "Hướng dẫn", "instructionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> h<PERSON> dẫn cho model...", "advancedConfig": "<PERSON><PERSON><PERSON> chỉnh nâng cao", "advancedSettings": "Cài đặt nâng cao", "maxTokens": "<PERSON>", "temperature": "Temperature", "topP": "Top P", "topK": "Top K", "birthDate": "<PERSON><PERSON><PERSON>", "gender": "<PERSON><PERSON><PERSON><PERSON> t<PERSON>h", "male": "Nam", "female": "<PERSON><PERSON>", "other": "K<PERSON><PERSON><PERSON>", "language": "<PERSON><PERSON><PERSON>", "education": "<PERSON><PERSON><PERSON><PERSON> độ học vấn", "highSchool": "<PERSON><PERSON> học", "college": "<PERSON> đẳng", "university": "<PERSON><PERSON><PERSON>", "postgraduate": "<PERSON><PERSON> <PERSON><PERSON><PERSON> học", "country": "Quốc gia", "position": "<PERSON><PERSON><PERSON> v<PERSON>", "positionPlaceholder": "<PERSON><PERSON><PERSON><PERSON> v<PERSON>", "skills": "<PERSON><PERSON> n<PERSON>ng", "skillPlaceholder": "<PERSON><PERSON><PERSON><PERSON> kỹ năng và nhấn Enter", "personality": "<PERSON><PERSON><PERSON>", "personalityPlaceholder": "<PERSON><PERSON> t<PERSON> t<PERSON>h c<PERSON>ch của agent", "configureFields": "<PERSON><PERSON><PERSON> hình các trườ<PERSON>", "configureFieldsDescription": "<PERSON><PERSON><PERSON><PERSON> lập các trường dữ liệu để thu thập thông tin từ người dùng", "addField": "<PERSON><PERSON><PERSON><PERSON> tr<PERSON>", "editField": "Chỉnh sửa trường", "fieldName": "<PERSON><PERSON><PERSON> tr<PERSON>", "fieldType": "Loại trường", "fieldDescription": "<PERSON><PERSON> tả trường", "type": "<PERSON><PERSON><PERSON>", "required": "<PERSON><PERSON><PERSON> b<PERSON>", "createTitle": "Tạo Agent Template", "createSubtitle": "Tạo template mới với loại: {{typeName}}", "createSuccessMessage": "Agent template đã đ<PERSON><PERSON><PERSON> tạo thành công", "typeNotFound": "<PERSON><PERSON><PERSON><PERSON> tìm thấy lo<PERSON>i agent", "selectTypeAgain": "<PERSON><PERSON>n lại lo<PERSON>i agent"}}