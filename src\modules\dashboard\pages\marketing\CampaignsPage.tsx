import React from 'react';
import { DashboardCard } from '../../components';
import { DashboardWidget } from '../../types';

const CampaignsPage: React.FC = () => {
  const widgets: DashboardWidget[] = [
    {
      id: 'campaign-overview',
      title: 'Tổng quan chiến dịch',
      type: 'metric',
      x: 0,
      y: 0,
      w: 12,
      h: 2,
      minW: 6,
      minH: 2,
      isEmpty: true
    },
    {
      id: 'campaign-performance',
      title: '<PERSON><PERSON><PERSON> suất chiến dịch',
      type: 'chart',
      x: 0,
      y: 2,
      w: 6,
      h: 4,
      minW: 4,
      minH: 3,
      isEmpty: true
    },
    {
      id: 'campaign-roi',
      title: 'ROI Chiến dịch',
      type: 'chart',
      x: 6,
      y: 2,
      w: 6,
      h: 4,
      minW: 4,
      minH: 3,
      isEmpty: true
    }
  ];

  return (
    <div className="h-full">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold text-foreground"><PERSON><PERSON><PERSON> dịch Marketing</h1>
        <p className="text-muted-foreground mt-1">
          <PERSON>õ<PERSON> hi<PERSON> suất và ROI của các chiến dịch marketing
        </p>
      </div>
      
      <DashboardCard
        widgets={widgets}
        isDraggable={true}
        isResizable={true}
      />
    </div>
  );
};

export default CampaignsPage;
