import React, { useState, useEffect, useMemo, useCallback } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Card,
  Table,
  Chip,
  IconCard,
  ActionMenu,
  ActionMenuItem,
} from '@/shared/components/common';
import { ConfirmDeleteModal } from '@/modules/admin/marketplace/components/modals';
import { SortOrder } from '@/shared/components/common/Table/types';
import { SortDirection } from '@/shared/dto/request/query.dto';

import MenuIconBar, { ColumnVisibility } from '@/modules/components/menu-bar/MenuIconBar';
import { ActiveFilters } from '@/modules/components/filters';
import SlideInForm from '@/shared/components/common/SlideInForm';
import useSlideForm from '@/shared/hooks/useSlideForm';
import { useActiveFilters } from '@/shared/hooks/filters';
import { URLForm, CrawlUrlForm, CrawlUrlFormValues } from '@/modules/admin/data/components/forms';
import { Tooltip as CustomTooltip } from '@/shared/components/common';

// Import hooks từ module URL
import {
  useUrlsQuery,
  useCreateUrlMutation,
  useUpdateUrlMutation,
  useDeleteUrlMutation,
  useToggleUrlStatusMutation,
  useCrawlUrlMutation,
} from '@/modules/admin/data/url/hooks/useUrlQuery';

// Import types từ module URL
import {
  Url,
  UrlSearchParams,
  CreateUrlParams,
  UpdateUrlParams,
} from '@/modules/admin/data/url/types/url.types';
import { formatDate } from '@/shared/utils/format';

/**
 * Trang quản lý URL
 */
const URLPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  const [urls, setUrls] = useState<Url[]>([]);

  const [currentPage, setCurrentPage] = useState(1);
  const [itemsPerPage, setItemsPerPage] = useState(10);
  const [totalItems, setTotalItems] = useState(0);
  const [urlToDelete, setUrlToDelete] = useState<Url | null>(null);
  const [urlToView, setUrlToView] = useState<Url | null>(null);
  const [showDeleteConfirm, setShowDeleteConfirm] = useState(false);
  const [selectedRowKeys, setSelectedRowKeys] = useState<React.Key[]>([]);
  const [showBulkDeleteConfirm, setShowBulkDeleteConfirm] = useState(false);

  const [isCrawling, setIsCrawling] = useState(false);

  // State cho tìm kiếm
  const [searchTerm, setSearchTerm] = useState('');

  // State cho filter ownedByEnum
  const [ownedByFilter, setOwnedByFilter] = useState<'all' | 'ADMIN' | 'USER'>('all');

  // State cho sắp xếp
  const [sortBy, setSortBy] = useState<string>('');
  const [sortDirection, setSortDirection] = useState<SortDirection>(SortDirection.DESC);

  // State cho hiển thị cột
  const [visibleColumns, setVisibleColumns] = useState<ColumnVisibility[]>([]);

  // Sử dụng hook animation cho form tạo mới
  const {
    isVisible: isCreateFormVisible,
    showForm: showCreateForm,
    hideForm: hideCreateForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form xem chi tiết

  // Sử dụng hook animation cho form chỉnh sửa
  const {
    isVisible: isEditFormVisible,
    showForm: showEditSlideForm,
    hideForm: hideEditForm,
  } = useSlideForm();

  // Sử dụng hook animation cho form crawl URL
  const {
    isVisible: isCrawlFormVisible,
    showForm: showCrawlForm,
    hideForm: hideCrawlForm,
  } = useSlideForm();

  // Tạo query params cho API
  const queryParams = useMemo<UrlSearchParams>(() => {
    const params: UrlSearchParams = {
      page: currentPage,
      limit: itemsPerPage,
      keyword: searchTerm || undefined,
      sortBy: sortBy || undefined,
      sortDirection: sortDirection === SortDirection.ASC ? 'ASC' : 'DESC',
    };

    // Thêm filter ownedByEnum nếu không phải 'all'
    if (ownedByFilter !== 'all') {
      params.ownedByEnum = ownedByFilter;
    }

    return params;
  }, [currentPage, itemsPerPage, searchTerm, sortBy, sortDirection, ownedByFilter]);

  // Hooks để gọi API
  const { data: urlsData, isLoading: isLoadingUrls, error: urlsError } = useUrlsQuery(queryParams);

  const { mutateAsync: createUrl } = useCreateUrlMutation();
  const { mutateAsync: updateUrl } = useUpdateUrlMutation();
  const { mutateAsync: deleteUrl } = useDeleteUrlMutation();
  const { mutateAsync: toggleUrlStatus } = useToggleUrlStatusMutation();
  const { mutateAsync: crawlUrl } = useCrawlUrlMutation();

  // Xử lý submit form tạo URL
  const handleSubmitCreateUrl = useCallback(
    async (values: Record<string, unknown>) => {
      console.log('Admin URLPage: Starting form submission with values:', values);

      try {
        // Chuẩn bị dữ liệu cho API
        const urlData: CreateUrlParams = {
          url: values['url'] as string,
          title: values['title'] as string,
          content: values['content'] as string,
          type: values['type'] as string,
          tags: values['tags'] as string[],
          isActive: values['isActive'] as boolean,
        };

        console.log('Admin URLPage: Calling createUrl API with data:', urlData);
        // Gọi API tạo URL - throw error để URLForm xử lý
        await createUrl(urlData);
        console.log('Admin URLPage: createUrl API call successful');
        hideCreateForm();
      } catch (error) {
        console.error('Admin URLPage: Error during form submission:', error);
        // Define error interface for type safety
        interface ApiErrorWithCode {
          code?: number;
          message?: string;
          response?: {
            data?: {
              code?: number;
              message?: string;
            };
            status?: number;
          };
        }

        const typedError = error as ApiErrorWithCode;

        console.error('Admin URLPage: Error structure:', {
          error,
          errorCode: typedError?.code,
          errorMessage: typedError?.message,
          responseData: typedError?.response?.data,
          responseStatus: typedError?.response?.status,
        });

        // Kiểm tra lỗi 30004 từ nhiều nguồn có thể
        // Error structure có thể là: error.response.data.code hoặc error.code
        const errorData = typedError?.response?.data || typedError;
        const errorCode = errorData?.code;
        const errorMessage = errorData?.message || 'URL này đã tồn tại trong hệ thống của bạn';

        console.log('Admin URLPage: Parsed error data:', { errorData, errorCode, errorMessage });

        // Nếu là lỗi 30004 (URL đã tồn tại), throw lại để form xử lý
        if (errorCode === 30004) {
          console.log('Admin URLPage: Throwing 30004 error for form to handle');
          const formError = { code: 30004, message: errorMessage };
          throw formError; // Để form xử lý hiển thị lỗi trên trường URL
        }

        // Các lỗi khác throw lại
        throw error;
      }
    },
    [createUrl, hideCreateForm]
  );

  // Xử lý lỗi từ form tạo URL
  const handleCreateUrlError = useCallback((error: unknown) => {
    console.error('[URLPage] Error creating URL:', error);
    // Có thể thêm thông báo lỗi chung ở đây nếu cần
  }, []);

  // Xử lý submit form chỉnh sửa URL
  const handleSubmitEditUrl = useCallback(
    async (values: Record<string, unknown>) => {
      if (!urlToView) return;

      console.log('Admin URLPage: Starting edit form submission with values:', values);

      try {
        // Chuẩn bị dữ liệu cho API
        const urlData: UpdateUrlParams = {
          url: values['url'] as string,
          title: values['title'] as string,
          content: values['content'] as string,
          type: values['type'] as string,
          tags: values['tags'] as string[],
          isActive: values['isActive'] as boolean,
        };

        console.log('Admin URLPage: Calling updateUrl API with data:', urlData);
        // Gọi API cập nhật URL - throw error để URLForm xử lý
        await updateUrl({ id: urlToView.id, urlData });
        console.log('Admin URLPage: updateUrl API call successful');
        hideEditForm();
        setUrlToView(null);
      } catch (error) {
        console.error('Admin URLPage: Error during edit form submission:', error);
        // Define error interface for type safety (reuse same interface)
        interface ApiErrorWithCode {
          code?: number;
          message?: string;
          response?: {
            data?: {
              code?: number;
              message?: string;
            };
            status?: number;
          };
        }

        const typedError = error as ApiErrorWithCode;

        console.error('Admin URLPage: Edit error structure:', {
          error,
          errorCode: typedError?.code,
          errorMessage: typedError?.message,
          responseData: typedError?.response?.data,
          responseStatus: typedError?.response?.status,
        });

        // Kiểm tra lỗi 30004 từ nhiều nguồn có thể
        const errorData = typedError?.response?.data || typedError;
        const errorCode = errorData?.code;
        const errorMessage = errorData?.message || 'URL này đã tồn tại trong hệ thống của bạn';

        console.log('Admin URLPage: Parsed edit error data:', {
          errorData,
          errorCode,
          errorMessage,
        });

        // Nếu là lỗi 30004 (URL đã tồn tại), throw lại để form xử lý
        if (errorCode === 30004) {
          console.log('Admin URLPage: Throwing 30004 error for edit form to handle');
          const formError = { code: 30004, message: errorMessage };
          throw formError; // Để form xử lý hiển thị lỗi trên trường URL
        }

        // Các lỗi khác throw lại
        throw error;
      }
    },
    [updateUrl, hideEditForm, urlToView]
  );

  // Xử lý lỗi từ form chỉnh sửa URL
  const handleEditUrlError = useCallback((error: unknown) => {
    console.error('[URLPage] Error updating URL:', error);
    // Có thể thêm thông báo lỗi chung ở đây nếu cần
  }, []);

  // Cập nhật state khi có dữ liệu từ API
  useEffect(() => {
    if (urlsData) {
      setUrls(urlsData.items);
      setTotalItems(urlsData.meta.totalItems);
    }
  }, [urlsData, urlsError]);

  // Xử lý thay đổi trang
  const handlePageChange = useCallback(
    (page: number, newPageSize: number) => {
      setCurrentPage(page);
      if (newPageSize !== itemsPerPage) {
        setItemsPerPage(newPageSize);
        setCurrentPage(1); // Reset về trang 1 khi thay đổi số mục trên trang
      }
    },
    [itemsPerPage]
  );

  // Xử lý tìm kiếm
  const handleSearch = useCallback((term: string) => {
    setSearchTerm(term);
    setCurrentPage(1); // Reset về trang 1 khi tìm kiếm
  }, []);

  // Xử lý thay đổi sắp xếp
  const handleSortChange = useCallback((column: string | null, order: SortOrder) => {
    setSortBy(column || '');
    setSortDirection(order === 'asc' ? SortDirection.ASC : SortDirection.DESC);
  }, []);

  // Xử lý hủy xóa
  const handleCancelDelete = useCallback(() => {
    setShowDeleteConfirm(false);
    setUrlToDelete(null);
  }, []);

  // Xử lý xác nhận xóa
  const handleConfirmDelete = useCallback(async () => {
    if (!urlToDelete) return;

    try {
      await deleteUrl([urlToDelete.id]); // Truyền mảng ids
      setShowDeleteConfirm(false);
      setUrlToDelete(null);
    } catch (error) {
      console.error('[URLPage] Error deleting URLs:', error);
    }
  }, [urlToDelete, deleteUrl]);

  // Xử lý xóa nhiều
  const handleShowBulkDeleteConfirm = useCallback(() => {
    if (selectedRowKeys.length === 0) {
      return;
    }
    setShowBulkDeleteConfirm(true);
  }, [selectedRowKeys]);

  const handleCancelBulkDelete = useCallback(() => {
    setShowBulkDeleteConfirm(false);
  }, []);

  const handleConfirmBulkDelete = useCallback(async () => {
    if (selectedRowKeys.length === 0) return;

    try {
      await deleteUrl(selectedRowKeys as string[]);
      setShowBulkDeleteConfirm(false);
      setSelectedRowKeys([]);
    } catch (error) {
      console.error('[URLPage] Error deleting URLs (bulk):', error);
    }
  }, [selectedRowKeys, deleteUrl]);

  // Xử lý đảo ngược trạng thái kích hoạt
  const handleToggleStatus = useCallback(
    async (url: Url) => {
      try {
        await toggleUrlStatus(url.id);
      } catch (error) {
        console.error('[URLPage] Error toggling URL status:', error);
      }
    },
    [toggleUrlStatus]
  );

  // Xử lý hiển thị form crawl URL
  const handleShowCrawlForm = useCallback(() => {
    showCrawlForm();
  }, [showCrawlForm]);

  // Xử lý submit form crawl URL
  const handleSubmitCrawlUrl = useCallback(
    async (values: CrawlUrlFormValues) => {
      try {
        setIsCrawling(true);

        // Chuẩn bị dữ liệu cho API
        const crawlData = {
          url: values.url,
          depth: values.depth,
          maxUrls: values.maxUrls || 1,
          ignoreRobotsTxt: values.ignoreRobotsTxt,
        };

        // Gọi API crawl URL
        await crawlUrl(crawlData);

        // Đóng form
        hideCrawlForm();
      } catch (error) {
        console.error('Error crawling URL:', error);
      } finally {
        setIsCrawling(false);
      }
    },
    [crawlUrl, hideCrawlForm, setIsCrawling]
  );

  // Xử lý thay đổi hiển thị cột
  const handleColumnVisibilityChange = useCallback((columns: ColumnVisibility[]) => {
    setVisibleColumns(columns);
  }, []);

  // Xử lý hiển thị form chỉnh sửa URL
  const handleShowEditForm = useCallback(
    (url: Url) => {
      setUrlToView(url);
      showEditSlideForm();
    },
    [showEditSlideForm]
  );

  // Xử lý thay đổi filter ownedByEnum
  const handleOwnedByFilterChange = useCallback((filterId: string) => {
    setOwnedByFilter(filterId as 'all' | 'ADMIN' | 'USER');
    setCurrentPage(1); // Reset về trang 1 khi thay đổi filter
  }, []);

  // Tạo filter options cho ownedByEnum
  const filterOptions = useMemo(
    () => [
      {
        id: 'all',
        label: t('admin:data.url.filter.all', 'Tất cả'),
        icon: 'list',
        onClick: () => handleOwnedByFilterChange('all'),
      },
      {
        id: 'admin',
        label: t('admin:data.url.filter.admin', 'Admin'),
        icon: 'settings',
        onClick: () => handleOwnedByFilterChange('ADMIN'),
      },
      {
        id: 'user',
        label: t('admin:data.url.filter.user', 'User'),
        icon: 'user',
        onClick: () => handleOwnedByFilterChange('USER'),
      },
    ],
    [t, handleOwnedByFilterChange]
  );

  // Sử dụng hook useActiveFilters để quản lý các hàm xử lý bộ lọc
  const { handleClearSearch, handleClearFilter, handleClearSort, handleClearAll, getFilterLabel } =
    useActiveFilters({
      handleSearch: handleSearch,
      setSelectedFilterId: (filterId: string) => handleOwnedByFilterChange(filterId),
      setDateRange: () => {}, // URL page không sử dụng date range
      handleSortChange: handleSortChange,
      selectedFilterValue: ownedByFilter,
      filterValueLabelMap: {
        ADMIN: t('admin:data.url.filter.admin', 'Admin'),
        USER: t('admin:data.url.filter.user', 'User'),
      },
      t,
    });

  // Định nghĩa các cột cho bảng
  const columns = useMemo(() => {
    const allColumns = [
      {
        key: 'url',
        title: t('admin:data.url.table.url', 'URL'),
        dataIndex: 'url',
        width: '20%',
        sortable: true,
        render: (value: unknown) => (
          <CustomTooltip content={t('common.copy', 'Sao chép')}>
            <IconCard
              icon="copy"
              variant="default"
              size="sm"
              className="ml-2"
              onClick={() => {
                navigator.clipboard.writeText(String(value || ''));
              }}
            />
          </CustomTooltip>
        ),
      },
      {
        key: 'title',
        title: t('admin:data.url.table.title', 'Tiêu đề'),
        dataIndex: 'title',
        width: '20%',
        sortable: true,
      },

      {
        key: 'isActive',
        title: t('admin:data.url.table.status', 'Trạng thái'),
        dataIndex: 'isActive',
        width: '10%',
        sortable: true,
        render: (value: unknown) => {
          const isActive = value as boolean;
          return (
            <Chip
              size="sm"
              variant={isActive ? 'success' : 'danger'}
              className="whitespace-nowrap min-w-0 flex-shrink-0"
            >
              <span className="truncate">
                {isActive
                  ? t('admin:data.common.active', 'Kích hoạt')
                  : t('admin:data.common.inactive', 'Vô hiệu')}
              </span>
            </Chip>
          );
        },
      },
      {
        key: 'createdAt',
        title: t('admin:data.url.table.createdAt', 'Ngày tạo'),
        dataIndex: 'createdAt',
        width: '15%',
        sortable: true,
        render: (value: unknown) => <span>{formatDate(value as number)}</span>,
      },
      {
        key: 'actions',
        title: t('', ''),
        width: '120px',
        render: (_: unknown, record: Url) => {
          // Tạo danh sách các action items
          const actionItems: ActionMenuItem[] = [
            ...(record.ownedByEnum === 'ADMIN'
              ? [
                  {
                    id: 'edit',
                    label: t('common.edit', 'Sửa'),
                    icon: 'edit',
                    onClick: () => handleShowEditForm(record),
                  },
                ]
              : []),
            {
              id: 'toggle',
              label: record.isActive
                ? t('common.deactivate', 'Vô hiệu')
                : t('common.activate', 'Kích hoạt'),
              icon: record.isActive ? 'toggle-off' : 'toggle-on',
              onClick: () => handleToggleStatus(record),
            },
          ];

          return (
            <ActionMenu
              items={actionItems}
              menuTooltip={t('common:moreActions', 'Thêm thao tác')}
              iconSize="sm"
              iconVariant="default"
              placement="bottom"
              menuWidth="180px"
              showAllInMenu={true}
              preferRight={true}
            />
          );
        },
      },
    ];

    // Lọc các cột dựa trên visibleColumns
    if (visibleColumns.length === 0) {
      return allColumns;
    }

    const visibleColumnIds = visibleColumns.filter(col => col.visible).map(col => col.id);

    // Luôn hiển thị cột actions
    return allColumns.filter(col => col.key === 'actions' || visibleColumnIds.includes(col.key));
  }, [t, visibleColumns, handleShowEditForm, handleToggleStatus]);

  // Lọc các cột hiển thị
  const filteredColumns = useMemo(() => {
    // Nếu chưa có visibleColumns, hiển thị tất cả
    if (visibleColumns.length === 0) {
      // Tạo visibleColumns từ columns
      setVisibleColumns([
        { id: 'all', label: t('common.all', 'Tất cả'), visible: true },
        ...columns.map(col => ({
          id: col.key,
          label: typeof col.title === 'string' ? col.title : col.key,
          visible: true,
        })),
      ]);
      return columns;
    }

    // Nếu "Tất cả" được chọn, hiển thị tất cả
    const allSelected = visibleColumns.find(col => col.id === 'all')?.visible;
    if (allSelected) {
      return columns;
    }

    // Lọc theo các cột được chọn
    return columns.filter(
      col => col.key === 'actions' || visibleColumns.find(vc => vc.id === col.key)?.visible
    );
  }, [columns, visibleColumns, t]);

  return (
    <div>
      <MenuIconBar
        onSearch={handleSearch}
        onAdd={() => showCreateForm()}
        onColumnVisibilityChange={handleColumnVisibilityChange}
        columns={visibleColumns}
        showDateFilter={true}
        items={filterOptions}
        showColumnFilter={true}
        additionalIcons={[
          {
            icon: 'link',
            tooltip: t('data:url.crawl', 'Crawl URL'),
            variant: 'primary',
            onClick: handleShowCrawlForm,
          },

          {
            icon: 'trash',
            tooltip: t('common:bulkDelete', 'Xóa nhiều'),
            variant: 'primary',
            onClick: handleShowBulkDeleteConfirm,
            className: 'text-red-500',
            condition: selectedRowKeys.length > 0,
          },
        ]}
      />

      {/* Hiển thị ActiveFilters */}
      <ActiveFilters
        searchTerm={searchTerm}
        onClearSearch={handleClearSearch}
        filterValue={ownedByFilter}
        filterLabel={getFilterLabel()}
        onClearFilter={handleClearFilter}
        sortBy={sortBy}
        sortDirection={sortDirection}
        onClearSort={handleClearSort}
        onClearAll={handleClearAll}
      />

      {/* SlideInForm cho form tạo mới */}
      <SlideInForm isVisible={isCreateFormVisible}>
        <URLForm
          onSubmit={handleSubmitCreateUrl}
          onCancel={hideCreateForm}
          onError={handleCreateUrlError}
        />
      </SlideInForm>

      {/* SlideInForm cho form chỉnh sửa */}
      <SlideInForm isVisible={isEditFormVisible}>
        {urlToView && (
          <URLForm
            initialValues={urlToView}
            onSubmit={handleSubmitEditUrl}
            onCancel={hideEditForm}
            onError={handleEditUrlError}
          />
        )}
      </SlideInForm>

      {/* SlideInForm cho form crawl URL */}
      <SlideInForm isVisible={isCrawlFormVisible}>
        <CrawlUrlForm
          onSubmit={handleSubmitCrawlUrl}
          onCancel={hideCrawlForm}
          isLoading={isCrawling}
        />
      </SlideInForm>

      <Card className="overflow-hidden">
        <Table<Url>
          columns={filteredColumns}
          data={urls}
          rowKey="id"
          loading={isLoadingUrls}
          sortable={true}
          selectable={true}
          rowSelection={{
            selectedRowKeys,
            onChange: keys => setSelectedRowKeys(keys),
          }}
          onSortChange={handleSortChange}
          defaultSort={{
            column: sortBy || '',
            order: sortDirection === SortDirection.ASC ? 'asc' : 'desc',
          }}
          pagination={{
            current: currentPage,
            pageSize: itemsPerPage,
            total: totalItems,
            onChange: handlePageChange,
            showSizeChanger: true,
            pageSizeOptions: [10, 20, 50, 100],
            showFirstLastButtons: true,
            showPageInfo: true,
          }}
        />
      </Card>

      {/* Modal xác nhận xóa */}
      <ConfirmDeleteModal
        isOpen={showDeleteConfirm}
        onClose={handleCancelDelete}
        onConfirm={handleConfirmDelete}
        title={t('admin:data.common.confirmDelete', 'Xác nhận xóa')}
        message={t('admin:data.url.confirmDeleteMessage', 'Bạn có chắc chắn muốn xóa URL này?')}
        itemName={urlToDelete?.url || ''}
      />

      {/* Modal xác nhận xóa nhiều */}
      <ConfirmDeleteModal
        isOpen={showBulkDeleteConfirm}
        onClose={handleCancelBulkDelete}
        onConfirm={handleConfirmBulkDelete}
        title={t('common.confirmDelete', 'Xác nhận xóa')}
        message={t(
          'admin:data.url.confirmBulkDeleteMessage',
          `Bạn có chắc chắn muốn xóa ${selectedRowKeys.length} URL đã chọn?`
        )}
      />
    </div>
  );
};

export default URLPage;
