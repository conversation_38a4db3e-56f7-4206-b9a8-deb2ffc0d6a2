{"agent": {"management": {"title": "Agent Management", "description": "Comprehensive Agent system management"}, "notification": {"createSuccess": "{{entityName}} has been created successfully", "updateSuccess": "{{entityName}} has been updated successfully", "deleteSuccess": "{{entityName}} has been deleted successfully", "restoreSuccess": "{{entityName}} has been restored successfully", "createError": "An error occurred while creating {{entityName}}", "updateError": "An error occurred while updating {{entityName}}", "deleteError": "An error occurred while deleting {{entityName}}", "restoreError": "An error occurred while restoring {{entityName}}", "loadError": "Unable to load {{entityName}} list", "uploadSuccess": "Upload successful", "uploadSuccessWithName": "{{fileName}} uploaded successfully", "uploadError": "An error occurred while uploading", "validationError": "Invalid data", "permissionError": "You do not have permission to perform this action", "networkError": "Network connection error. Please try again", "processing": "{{action}}..."}, "rank": {"title": "Agent Rank Management", "description": "Agent ranking management", "pageTitle": "Agent Rank Management", "addRank": "Add New Rank", "editRank": "Edit Agent Rank", "searchPlaceholder": "Search Ranks...", "noSearchResults": "No Ranks found matching your search criteria.", "createFirst": "Create First Agent Rank", "sortBy": "Sort by", "createSuccess": "Success", "createSuccessMessage": "Agent rank has been created successfully", "updateSuccess": "Success", "updateSuccessMessage": "Agent rank has been updated successfully", "active": "Active", "inactive": "Inactive", "editAction": "Edit", "deleteAction": "Delete", "confirmDelete": "Confirm Delete Rank", "deleteMessage": "Are you sure you want to delete this rank? This action cannot be undone.", "deleteSuccess": "Rank deleted successfully", "deleteError": "An error occurred while deleting the rank", "list": {"title": "Rank List", "noRanks": "No Ranks", "noRanksDescription": "There are currently no ranks in the system.", "loadError": "Unable to load rank list. Please try again.", "loading": "Loading rank list...", "refreshing": "Refreshing data..."}, "form": {"basicInfo": "Basic Information", "name": "Rank Name", "namePlaceholder": "Enter rank name", "description": "Description", "descriptionPlaceholder": "Enter rank description", "expRange": "Experience Range", "minExp": "Minimum Experience", "maxExp": "Maximum Experience", "badge": "Badge", "badgeUpload": "Upload badge", "badgeHelp": "Supported formats: JPG, PNG (single image only)", "currentBadge": "Current Badge", "currentBadgeNote": "Upload new image to replace", "active": "Active", "create": "Create Rank", "update": "Update", "creating": "Creating Rank...", "createSuccess": "Rank created successfully", "createError": "An error occurred while creating the rank", "updateError": "An error occurred while updating the rank"}, "validation": {"nameRequired": "Rank name is required", "descriptionRequired": "Description is required", "minExpInvalid": "Minimum experience must be >= 0", "minExpInteger": "Minimum experience must be an integer", "maxExpInvalid": "Maximum experience must be > 0", "maxExpInteger": "Maximum experience must be an integer", "expRangeInvalid": "Maximum experience must be greater than minimum experience", "expRangeOverlap": "Experience range overlaps with another rank's experience range"}, "edit": {"notFound": "Rank not found"}, "sort": {"name": "Name", "minExp": "Minimum Experience", "maxExp": "Maximum Experience", "createdAt": "Created Date"}}, "system": {"title": "System Agent Management", "description": "System Agent management", "pageTitle": "System Agent Management", "addAgent": "Add New Agent", "editAgent": "Edit Agent System", "searchPlaceholder": "Search Agents...", "noSearchResults": "No Agents found matching your search criteria.", "createFirst": "Create First System Agent", "viewTrash": "View Trash", "backToMain": "Back to Main List", "cancel": "Cancel", "updateAgent": "Update Agent", "createSuccess": "Success", "createSuccessMessage": "Agent system has been created successfully", "updateSuccess": "Success", "updateSuccessMessage": "Agent system has been updated successfully"}, "user": {"title": "Admin - Agent User", "description": "User Agent management"}, "type": {"title": "Agent Type Management", "description": "Agent Type management", "pageTitle": "Agent Type Management", "addType": "Add New Agent Type", "editType": "Edit Agent Type", "searchPlaceholder": "Search agent types...", "noSearchResults": "No agent types found matching your search criteria.", "createFirst": "Create First Agent Type", "createSuccess": "Success", "createSuccessMessage": "Agent type has been created successfully", "updateSuccess": "Success", "updateSuccessMessage": "Agent type has been updated successfully", "cancel": "Cancel", "updateType": "Update Agent Type", "viewTrash": "View Trash", "backToMain": "Back to Main List", "deleteConfirmTitle": "Confirm Delete Agent Type", "deleteConfirmMessage": "Are you sure you want to delete this agent type? This action will move the agent type to trash and can be restored.", "deleteSuccess": "Delete Successful", "deleteSuccessMessage": "Agent type has been deleted successfully", "restoreSuccess": "Restore Successful", "restoreSuccessMessage": "Agent type has been restored successfully", "selectTypeToDelete": "Select Agent Type to Delete", "selectTypeToDeleteDescription": "Select the agent type you want to delete. The agent type will be moved to trash and can be restored.", "deleteWithMigration": "Delete with Migration", "deleteWithMigrationDescription": "Delete agent type and migrate all agents of this type to the newly selected type.", "newTypeAgent": "New Agent Type", "selectNewType": "Select New Agent Type", "selectNewTypeDescription": "Select the new agent type to migrate current agents to.", "noAvailableTypes": "No other agent types available for migration. You can only delete without migration.", "deleteOnly": "Delete Only", "deleteError": "An error occurred while deleting agent type", "restoreError": "An error occurred while restoring agent type"}, "trash": {"title": "Trash - Agent Types", "noAgents": "No agent types in trash", "noAgentsDescription": "Trash is empty. Deleted agent types will appear here.", "restoreAgent": "Restore Agent Type", "permanentDelete": "Permanent Delete"}, "list": {"title": "Agent Type List", "noTypes": "No Agent Types", "noTypesDescription": "There are currently no agent types in the system.", "loadError": "Unable to load agent type list. Please try again.", "loading": "Loading agent type list...", "refreshing": "Refreshing data..."}, "card": {"edit": "Edit", "delete": "Delete", "confirmDelete": "Confirm Delete Agent Type", "deleteMessage": "Are you sure you want to delete this agent type? This action cannot be undone.", "deleteSuccess": "Agent type deleted successfully", "deleteError": "An error occurred while deleting the agent type", "updateSuccess": "Agent type updated successfully", "updateError": "An error occurred while updating the agent type"}, "form": {"basicInfo": "Basic Information", "name": "Agent Type Name", "nameCode": "Identifier Code", "nameCodePlaceholder": "Enter identifier code", "instruction": "Instructions", "instructionPlaceholder": "Enter instructions for agent type", "avatar": "Avatar", "avatarUpload": "Upload avatar", "avatarHelp": "Supported formats: JPG, PNG (single image only)", "currentAvatar": "Current Avatar", "currentAvatarNote": "Upload new image to replace", "modelConfig": "Model Configuration", "temperature": "Temperature", "topP": "Top P", "topK": "Top K", "maxTokens": "<PERSON>", "provider": "Provider Type", "resources": "Resources", "model": "Model", "selectModel": "Select model", "vectorStore": "Vector Store", "selectVectorStore": "Select vector store", "namePlaceholder": "Enter agent type name", "description": "Description", "descriptionPlaceholder": "Enter agent type description", "defaultConfig": "Default Configuration", "enableAgentProfileCustomization": "Enable Agent Profile Customization", "enableOutputToMessenger": "Enable Output to Messenger", "enableOutputToWebsiteLiveChat": "Enable Output to Website Live Chat", "enableTaskConversionTracking": "Enable Task Conversion Tracking", "enableResourceUsage": "Enable Resource Usage", "enableDynamicStrategyExecution": "Enable Dynamic Strategy Execution", "enableMultiAgentCollaboration": "Enable Multi-Agent Collaboration", "enableOutputToZaloOA": "Enable Output to Zalo OA", "status": "Status", "selectStatus": "Select status", "draft": "Draft", "approved": "Approved", "agentSystems": "Agent Systems", "selectAgentSystems": "Select agent systems", "agentSystemsConfig": {"title": "Agent Systems", "noSystemsSelected": "No agent systems selected", "systemCount": "{{count}} agent systems selected", "addSystem": "Add Agent System", "selectedSystems": "Selected Agent Systems", "removeSystem": "Remove agent system", "removeSystemSuccess": "Agent system removed successfully", "removeSystemError": "Error removing agent system", "confirmDeleteSystem": "Are you sure you want to remove agent system \"{{systemName}\" from this agent type?", "deleteSystemWarning": "This action cannot be undone.", "createdAt": "Created At"}, "agentSystemSlideIn": {"title": "Select Agent System", "close": "Close", "cancel": "Cancel", "save": "Save", "system": "System", "status": "Status", "active": "Active", "inactive": "Inactive", "createdAt": "Created At", "filterBy": "Filter by", "all": "All", "updateSystemsSuccess": "Agent systems updated successfully", "updateSystemsError": "Error updating agent systems", "addSystemsToListSuccess": "Agent systems added to list successfully", "cannotSaveInThisMode": "Cannot save in this mode"}, "create": "Create Agent Type", "creating": "Creating agent type...", "createSuccess": "Agent type created successfully", "createError": "An error occurred while creating the agent type", "loadAgentSystemsError": "Unable to load agent systems list"}, "validation": {"nameRequired": "Agent type name is required", "descriptionRequired": "Description is required", "statusRequired": "Status is required", "agentSystemsRequired": "At least one agent system is required"}, "common": {"confirmDelete": "Confirm Delete", "cancel": "Cancel", "delete": "Delete", "error": "Error", "locale": "en-US", "success": "Success", "loading": "Loading...", "save": "Save", "close": "Close", "edit": "Edit", "view": "View", "back": "Back", "next": "Next", "previous": "Previous", "search": "Search", "filter": "Filter", "all": "All", "active": "Active", "inactive": "Inactive", "draft": "Draft", "approved": "Approved", "create": "Create", "update": "Update", "refresh": "Refresh", "restore": "Rest<PERSON>"}, "deleteConfirmTitle": "Confirm Delete Agent Type", "deleteConfirmMessage": "Are you sure you want to delete this agent type? This action will move the agent type to trash and can be restored.", "deleteSuccess": "Delete Successful", "deleteSuccessMessage": "Agent type has been deleted successfully", "deleteError": "An error occurred while deleting agent type", "selectTypeToDelete": "Select agent type to delete", "deleteWithMigration": "Delete with Migration", "deleteWithMigrationDescription": "Delete agent type and migrate all agents of this type to the newly selected type.", "newTypeAgent": "New agent type", "selectNewType": "Select new agent type", "selectNewTypeDescription": "Select new agent type to migrate current agents.", "noAvailableTypes": "No other agent types available for migration. You can only delete without migration.", "deleteOnly": "Delete Only"}, "strategy": {"title": "Agent Strategy Management", "description": "Agent strategy management", "pageTitle": "Agent Strategy Management", "addStrategy": "Add New Strategy", "editStrategy": "Edit Agent Strategy", "searchPlaceholder": "Search strategies...", "noSearchResults": "No strategies found matching your search criteria.", "createFirst": "Create First Agent Strategy", "createSuccess": "Success", "createSuccessMessage": "Agent strategy has been created successfully", "updateSuccess": "Success", "updateSuccessMessage": "Agent strategy has been updated successfully", "cancel": "Cancel", "updateStrategy": "Update Strategy", "viewTrash": "View Trash", "backToMain": "Back to Main List", "list": {"title": "Strategy List", "noStrategies": "No Strategies", "noStrategiesDescription": "There are currently no strategies in the system.", "loadError": "Unable to load strategy list. Please try again.", "loading": "Loading strategy list...", "refreshing": "Refreshing data..."}, "card": {"edit": "Edit", "delete": "Delete", "restore": "Rest<PERSON>", "confirmDelete": "Confirm Delete Strategy", "deleteMessage": "Are you sure you want to delete this strategy? This action cannot be undone.", "deleteSuccess": "Strategy deleted successfully", "deleteError": "An error occurred while deleting the strategy", "updateSuccess": "Strategy updated successfully", "updateError": "An error occurred while updating the strategy", "restoreSuccess": "Strategy restored successfully", "restoreError": "An error occurred while restoring the strategy"}, "form": {"basicInfo": "Basic Information", "name": "Strategy Name", "namePlaceholder": "Enter strategy name", "avatar": "Avatar", "avatarUpload": "Upload avatar", "avatarHelp": "Supported formats: JPG, PNG (single image only)", "modelConfig": "Model Configuration", "temperature": "Temperature", "maxTokens": "<PERSON>", "instruction": "Instructions", "instructionPlaceholder": "Enter instructions for strategy", "content": "Step Content", "contentStep": "Step {step}", "contentPlaceholder": "Enter content for step {step}", "addStep": "Add Step", "removeStep": "Remove Step", "exampleDefault": "Default Examples", "exampleStep": "Example {step}", "examplePlaceholder": "Enter example for step {step}", "addExample": "Add Example", "removeExample": "Remove Example", "systemModel": "System Model", "provider": "Provider Type", "selectProvider": "Select provider", "selectProviderFirst": "Please select provider first", "model": "Model", "selectSystemModel": "Select system model", "selectModel": "Select model", "create": "Create Strategy", "update": "Update Strategy", "creating": "Creating strategy...", "updating": "Updating strategy...", "createSuccess": "Strategy created successfully", "createError": "An error occurred while creating the strategy", "updateSuccess": "Strategy updated successfully", "updateError": "An error occurred while updating the strategy", "uploadingAvatar": "Uploading avatar...", "uploadAvatarSuccess": "Avatar uploaded successfully", "uploadAvatarError": "An error occurred while uploading avatar", "loadSystemModelsError": "Unable to load system models list", "currentAvatar": "Current Avatar"}, "trash": {"title": "Trash - Agent Strategies", "noStrategies": "No strategies in trash", "noStrategiesDescription": "Trash is empty. Deleted strategies will appear here.", "noSearchResults": "No strategies found matching your search criteria", "loadError": "Unable to load deleted strategies list"}, "validation": {"nameRequired": "Strategy name is required", "instructionRequired": "Instructions are required", "systemModelRequired": "System model is required", "providerRequired": "Please select a provider", "contentRequired": "Step content is required", "contentStepRequired": "Content for step {step} is required", "exampleRequired": "Default examples are required", "exampleStepRequired": "Example for step {step} is required"}}, "list": {"title": "Agent List", "noAgents": "No Agents", "noAgentsDescription": "There are currently no Agents in the system.", "loadError": "Unable to load Agent list. Please try again.", "loading": "Loading Agent list...", "refreshing": "Refreshing data..."}, "card": {"supervisor": "Supervisor", "active": "Active", "inactive": "Inactive", "activate": "Activate", "deactivate": "Deactivate", "edit": "Edit", "delete": "Delete", "restore": "Rest<PERSON>", "confirmDelete": "Confirm Delete Agent", "deleteMessage": "Are you sure you want to delete this Agent? This action cannot be undone.", "deleteSuccess": "Agent deleted successfully", "deleteError": "An error occurred while deleting the Agent", "updateSuccess": "Agent updated successfully", "updateError": "An error occurred while updating the Agent", "setSupervisor": "Set as Supervisor", "removeSupervisor": "Remove Supervisor", "setSupervisorSuccess": "Successfully set as supervisor", "removeSupervisorSuccess": "Successfully removed supervisor privileges", "supervisorError": "An error occurred while changing supervisor privileges", "restoreSuccess": "Agent restored successfully", "restoreError": "An error occurred while restoring the agent"}, "trash": {"noAgents": "No agents in trash", "noAgentsDescription": "Trash is empty. Deleted agents will appear here."}, "edit": {"notFound": "Agent not found"}, "pagination": {"itemsPerPage": "Items per page", "showingItems": "Showing {from} - {to} of {total} items", "page": "Page", "of": "of", "previous": "Previous", "next": "Next"}, "form": {"basicInfo": "Basic Information", "name": "Agent Name", "namePlaceholder": "Enter agent name", "nameCode": "Identifier Code", "nameCodePlaceholder": "Enter identifier code", "instruction": "Instructions", "instructionPlaceholder": "Enter instructions for agent", "description": "Description", "descriptionPlaceholder": "Enter agent description", "avatar": "Avatar", "avatarUpload": "Upload avatar", "avatarHelp": "Supported formats: JPG, PNG (single image only)", "modelConfig": "Model Configuration", "temperature": "Temperature", "topP": "Top P", "topK": "Top K", "maxTokens": "<PERSON>", "provider": "Provider Type", "resources": "Resources", "model": "Model", "selectModel": "Select model", "vectorStore": "Vector Store", "selectVectorStore": "Select vector store", "isSupervisor": "Is Supervisor", "create": "Create Agent", "creating": "Creating Agent...", "createSuccess": "Agent created successfully", "createError": "An error occurred while creating the Agent", "uploadingAvatar": "Uploading avatar...", "uploadAvatarSuccess": "Avatar uploaded successfully", "uploadAvatarError": "An error occurred while uploading avatar"}, "validation": {"nameRequired": "Agent name is required", "nameCodeRequired": "Identifier code is required", "nameCodeFormat": "Identifier code can only contain lowercase letters, numbers, underscores and hyphens", "instructionRequired": "Instructions are required", "descriptionRequired": "Description is required", "modelRequired": "Model is required", "modelIdInvalid": "Model ID must be a valid UUID"}}