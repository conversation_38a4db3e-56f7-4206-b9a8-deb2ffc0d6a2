import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import { adminAgentTemplateService } from '../services/agent-template.service';
import {
  AgentTemplateDetail,
  CreateAgentTemplateParams,
  UpdateAgentTemplateParams,
  UpdateAgentTemplateStatusParams,
  AgentTemplateQueryParams,
  RestoreAgentTemplateParams,
} from '../types/agent-template.types';

// Query keys
export const ADMIN_AGENT_TEMPLATE_QUERY_KEYS = {
  all: ['admin', 'agent-template'] as const,
  lists: () => [...ADMIN_AGENT_TEMPLATE_QUERY_KEYS.all, 'list'] as const,
  list: (params: AgentTemplateQueryParams) =>
    [...ADMIN_AGENT_TEMPLATE_QUERY_KEYS.lists(), params] as const,
  details: () => [...ADMIN_AGENT_TEMPLATE_QUERY_KEYS.all, 'detail'] as const,
  detail: (id: string) => [...ADMIN_AGENT_TEMPLATE_QUERY_KEYS.details(), id] as const,
  trash: () => [...ADMIN_AGENT_TEMPLATE_QUERY_KEYS.all, 'trash'] as const,
  trashList: (params: AgentTemplateQueryParams) =>
    [...ADMIN_AGENT_TEMPLATE_QUERY_KEYS.trash(), params] as const,
  trashDetail: (id: string) => [...ADMIN_AGENT_TEMPLATE_QUERY_KEYS.trash(), 'detail', id] as const,
};

/**
 * Hook để lấy danh sách agent template
 */
export const useAdminAgentTemplates = (params: AgentTemplateQueryParams) => {
  return useQuery({
    queryKey: ADMIN_AGENT_TEMPLATE_QUERY_KEYS.list(params),
    queryFn: () => adminAgentTemplateService.getAgentTemplates(params),
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook để lấy danh sách agent template đã xóa
 */
export const useAdminAgentTemplatesTrash = (params: AgentTemplateQueryParams) => {
  return useQuery({
    queryKey: ADMIN_AGENT_TEMPLATE_QUERY_KEYS.trashList(params),
    queryFn: () => adminAgentTemplateService.getDeletedAgentTemplates(params),
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook để lấy thông tin chi tiết agent template
 */
export const useAdminAgentTemplateDetail = (id: string) => {
  return useQuery<AgentTemplateDetail>({
    queryKey: ADMIN_AGENT_TEMPLATE_QUERY_KEYS.detail(id),
    queryFn: () => adminAgentTemplateService.getAgentTemplateById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook để lấy thông tin chi tiết agent template đã xóa
 */
export const useAdminAgentTemplateTrashDetail = (id: string) => {
  return useQuery<AgentTemplateDetail>({
    queryKey: ADMIN_AGENT_TEMPLATE_QUERY_KEYS.trashDetail(id),
    queryFn: () => adminAgentTemplateService.getDeletedAgentTemplateById(id),
    enabled: !!id,
    staleTime: 5 * 60 * 1000,
  });
};

/**
 * Hook để tạo agent template mới
 */
export const useCreateAdminAgentTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (data: CreateAgentTemplateParams) =>
      adminAgentTemplateService.createAgentTemplate(data),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_TEMPLATE_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để cập nhật agent template
 */
export const useUpdateAdminAgentTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateAgentTemplateParams }) =>
      adminAgentTemplateService.updateAgentTemplate(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_TEMPLATE_QUERY_KEYS.detail(variables.id),
      });
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_TEMPLATE_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để cập nhật trạng thái agent template
 */
export const useUpdateAdminAgentTemplateStatus = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ id, data }: { id: string; data: UpdateAgentTemplateStatusParams }) =>
      adminAgentTemplateService.updateAgentTemplateStatus(id, data),
    onSuccess: (_, variables) => {
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_TEMPLATE_QUERY_KEYS.detail(variables.id),
      });
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_TEMPLATE_QUERY_KEYS.lists(),
      });
    },
  });
};

/**
 * Hook để xóa agent template
 */
export const useDeleteAdminAgentTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => adminAgentTemplateService.deleteAgentTemplate(id),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_TEMPLATE_QUERY_KEYS.lists(),
      });
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_TEMPLATE_QUERY_KEYS.trash(),
      });
    },
  });
};

/**
 * Hook để khôi phục agent template
 */
export const useRestoreAdminAgentTemplate = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (id: string) => adminAgentTemplateService.restoreAgentTemplate(id),
    onSuccess: () => {
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_TEMPLATE_QUERY_KEYS.lists(),
      });
      queryClient.invalidateQueries({
        queryKey: ADMIN_AGENT_TEMPLATE_QUERY_KEYS.trash(),
      });
    },
  });
};
