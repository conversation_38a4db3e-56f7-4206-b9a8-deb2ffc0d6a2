import React from 'react';
import { useTranslation } from 'react-i18next';
import {
  <PERSON><PERSON>,
  Card,
  Typography,
  Divider,
  Skeleton,
  ScrollArea,
  FormItem,
  Input,
} from '@/shared/components/common';
import { TransactionDto, TransactionStatus } from '../../types';
import { formatCurrency, formatDateTime } from '@/shared/utils/format';

interface TransactionViewFormProps {
  /**
   * Transaction data to display
   */
  transaction: TransactionDto | null;

  /**
   * Loading state
   */
  isLoading: boolean;

  /**
   * Error state
   */
  error: unknown;

  /**
   * Function to handle form cancellation/close
   */
  onClose: () => void;
}

/**
 * Form component for viewing transaction details
 */
const TransactionViewForm: React.FC<TransactionViewFormProps> = ({
  transaction,
  isLoading,
  error,
  onClose,
}) => {
  const { t } = useTranslation(['rpointAdmin', 'common']);

  // Hàm lấy class cho trạng thái
  const getStatusClass = (status: TransactionStatus) => {
    switch (status) {
      case TransactionStatus.CONFIRMED:
        return 'bg-green-100 text-green-800 dark:bg-green-900 dark:text-green-200';
      case TransactionStatus.PENDING:
        return 'bg-yellow-100 text-yellow-800 dark:bg-yellow-900 dark:text-yellow-200';
      case TransactionStatus.FAILED:
        return 'bg-red-100 text-red-800 dark:bg-red-900 dark:text-red-200';
      case TransactionStatus.REFUNDED:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
      default:
        return 'bg-gray-100 text-gray-800 dark:bg-gray-700 dark:text-gray-200';
    }
  };

  if (isLoading) {
    return (
      <Card className="w-full flex flex-col">
        <div className="flex justify-between items-center mb-4 sticky top-0 bg-white dark:bg-gray-800 z-10 py-2">
          <Skeleton className="h-8 w-64" />
          <Button variant="outline" onClick={onClose}>
            {t('common:close')}
          </Button>
        </div>
        <ScrollArea
          height="calc(100vh - 180px)"
          className="flex-1 pr-4"
          autoHide={false}
          direction="vertical"
        >
          <div className="space-y-6">
            <Skeleton className="h-6 w-32 mb-4" />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-full mb-2" />
            </div>

            <Divider className="my-4" />

            <Skeleton className="h-6 w-32 mb-4" />
            <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-full mb-2" />
              <Skeleton className="h-4 w-full mb-2" />
            </div>
          </div>
        </ScrollArea>
      </Card>
    );
  }

  if (error || !transaction) {
    return (
      <Card className="w-full flex flex-col">
        <div className="flex justify-between items-center mb-4 sticky top-0 bg-white dark:bg-gray-800 z-10 py-2">
          <Typography variant="h2" className="text-red-500">
            {t('common:error')}
          </Typography>
          <Button variant="outline" onClick={onClose}>
            {t('common:close')}
          </Button>
        </div>
        <ScrollArea
          height="calc(100vh - 180px)"
          className="flex-1 pr-4"
          autoHide={false}
          direction="vertical"
        >
          <Typography variant="body1">{t('common:errorLoadingData')}</Typography>
        </ScrollArea>
      </Card>
    );
  }

  return (
    <Card className="w-full flex flex-col">
      {/* Thông tin khách hàng */}
      <div className="mb-6">
        <Typography variant="h5" className="mb-4 text-xl font-bold">
          {t('rpointAdmin:transactions.detail.userInfo')}
        </Typography>
        {transaction.user ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem label={t('common:name')} name="userName">
              <Input value={transaction.user.fullName} readOnly fullWidth />
            </FormItem>

            <FormItem label={t('common:email')} name="userEmail">
              <Input value={transaction.user.email} readOnly fullWidth />
            </FormItem>

            <FormItem label={t('common:phone')} name="userPhone">
              <Input value={transaction.user.phone || '-'} readOnly fullWidth />
            </FormItem>

            <FormItem label={t('common:userId')} name="userId">
              <Input value={transaction.userId.toString()} readOnly fullWidth />
            </FormItem>
          </div>
        ) : (
          <Typography variant="body1">
            {t('common:userNotFound')} (ID: {transaction.userId})
          </Typography>
        )}
      </div>

      <Divider className="my-4" />

      {/* Thông tin giao dịch */}
      <div className="mb-6">
        <Typography variant="h5" className="mb-4 text-xl font-bold">
          {t('rpointAdmin:transactions.detail.transactionInfo')}
        </Typography>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem label={t('rpointAdmin:transactions.table.amount')} name="amount">
            <Input value={`${formatCurrency(transaction.amount)} VND`} readOnly fullWidth />
          </FormItem>

          <FormItem label={t('rpointAdmin:transactions.table.pointsAmount')} name="pointsAmount">
            <Input
              value={transaction.pointsAmount.toString()}
              readOnly
              fullWidth
              className="text-primary font-medium"
            />
          </FormItem>

          <FormItem label={t('rpointAdmin:transactions.table.status')} name="status">
            <div className="flex items-center">
              <div
                className={`px-3 py-1 rounded-full text-center text-sm font-medium ${getStatusClass(
                  transaction.status
                )}`}
              >
                {t(`rpointAdmin:transactions.status.${transaction.status}`)}
              </div>
            </div>
          </FormItem>

          <FormItem label={t('rpointAdmin:transactions.table.createdAt')} name="createdAt">
            <Input value={formatDateTime(transaction.createdAt)} readOnly fullWidth />
          </FormItem>

          {transaction.completedAt && (
            <FormItem label={t('rpointAdmin:transactions.table.completedAt')} name="completedAt">
              <Input value={formatDateTime(transaction.completedAt)} readOnly fullWidth />
            </FormItem>
          )}
        </div>
      </div>

      <Divider className="my-4" />

      {/* Thông tin gói point */}
      <div className="mb-6">
        <Typography variant="h5" className="mb-4 text-xl font-bold">
          {t('common:pointPackage')}
        </Typography>
        {transaction.point ? (
          <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
            <FormItem label={t('common:name')} name="pointName">
              <Input value={transaction.point.name} readOnly fullWidth />
            </FormItem>

            <FormItem label={t('rpointAdmin:points.table.cash')} name="pointCash">
              <Input value={`${formatCurrency(transaction.point.cash)} VND`} readOnly fullWidth />
            </FormItem>

            <FormItem label={t('rpointAdmin:points.table.rate')} name="pointRate">
              <Input value={`1:${transaction.point.rate}`} readOnly fullWidth />
            </FormItem>

            <FormItem label={t('common:packageId')} name="pointId">
              <Input value={transaction.pointId.toString()} readOnly fullWidth />
            </FormItem>
          </div>
        ) : (
          <Typography variant="body1">
            {t('common:packageNotFound')} (ID: {transaction.pointId})
          </Typography>
        )}
      </div>

      <Divider className="my-4" />

      {/* Thông tin thanh toán */}
      <div className="mb-6">
        <Typography variant="h5" className="mb-4 text-xl font-bold">
          {t('rpointAdmin:transactions.detail.paymentInfo')}
        </Typography>
        <div className="grid grid-cols-1 md:grid-cols-2 gap-4">
          <FormItem label={t('rpointAdmin:transactions.table.paymentMethod')} name="paymentMethod">
            <Input value={transaction.paymentMethod} readOnly fullWidth />
          </FormItem>

          <FormItem label={t('common:referenceId')} name="referenceId">
            <Input value={transaction.referenceId} readOnly fullWidth className="font-mono" />
          </FormItem>

          {transaction.balanceBefore !== undefined && (
            <FormItem label={t('common:balanceBefore')} name="balanceBefore">
              <Input value={transaction.balanceBefore.toString()} readOnly fullWidth />
            </FormItem>
          )}

          {transaction.balanceAfter !== undefined && (
            <FormItem label={t('common:balanceAfter')} name="balanceAfter">
              <Input value={transaction.balanceAfter.toString()} readOnly fullWidth />
            </FormItem>
          )}

          {transaction.couponId > 0 && (
            <>
              <FormItem label={t('common:couponId')} name="couponId">
                <Input value={transaction.couponId.toString()} readOnly fullWidth />
              </FormItem>

              <FormItem label={t('common:couponAmount')} name="couponAmount">
                <Input
                  value={`${formatCurrency(transaction.couponAmount)} VND`}
                  readOnly
                  fullWidth
                />
              </FormItem>
            </>
          )}
        </div>
      </div>
      <div className="flex justify-end items-center mb-4 sticky top-0 bg-white dark:bg-gray-800 z-10 py-2">
        <Button variant="outline" onClick={onClose}>
          {t('common:close')}
        </Button>
      </div>
    </Card>
  );
};

export default TransactionViewForm;
