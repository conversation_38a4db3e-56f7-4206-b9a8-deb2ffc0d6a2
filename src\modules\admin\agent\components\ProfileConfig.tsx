import React, { useState, useEffect } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Box,
  Card,
  CardContent,
  Typography,
  Grid,
  TextField,
  FormControl,
  InputLabel,
  Select,
  MenuItem,
  Chip,
  IconButton
} from '@mui/material';
import { Add as AddIcon, Delete as DeleteIcon } from '@mui/icons-material';

interface ProfileData {
  birthDate: string;
  gender: string;
  language: string;
  education: string;
  country: string;
  position: string;
  skills: string[];
  personality: string;
}

interface ProfileConfigProps {
  initialData?: ProfileData;
  onSave?: (data: ProfileData) => void;
}

const ProfileConfig: React.FC<ProfileConfigProps> = ({
  initialData,
  onSave
}) => {
  const { t } = useTranslation(['admin', 'common']);

  const [profileData, setProfileData] = useState<ProfileData>(initialData || {
    birthDate: '2000-08-01',
    gender: 'Nữ',
    language: 'English',
    education: 'Cao đẳng',
    country: 'United States',
    position: 'Agent chatbot',
    skills: ['Tư vấn', 'Hỗ trợ khách hàng', 'dev'],
    personality: '<PERSON><PERSON><PERSON><PERSON>, hi<PERSON>u qu<PERSON>, chính xác, khó'
  });

  const [skillInput, setSkillInput] = useState('');

  const handleChange = (field: keyof ProfileData, value: any) => {
    const newData = { ...profileData, [field]: value };
    setProfileData(newData);
    if (onSave) onSave(newData);
  };

  const handleAddSkill = () => {
    if (skillInput.trim() && !profileData.skills.includes(skillInput.trim())) {
      const newSkills = [...profileData.skills, skillInput.trim()];
      handleChange('skills', newSkills);
      setSkillInput('');
    }
  };

  const handleRemoveSkill = (skillToRemove: string) => {
    const newSkills = profileData.skills.filter(skill => skill !== skillToRemove);
    handleChange('skills', newSkills);
  };

  const handleSkillInputKeyDown = (e: React.KeyboardEvent) => {
    if (e.key === 'Enter') {
      e.preventDefault();
      handleAddSkill();
    }
  };

  useEffect(() => {
    if (onSave) onSave(profileData);
  }, []);

  return (
    <Box>
      <Typography variant="h6" gutterBottom>
        {t('admin:agentTemplate.profileConfig')}
      </Typography>

      <Card>
        <CardContent>
          <Grid container spacing={3}>
            {/* Birth Date */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                type="date"
                label={t('admin:agentTemplate.birthDate')}
                value={profileData.birthDate}
                onChange={(e) => handleChange('birthDate', e.target.value)}
                InputLabelProps={{
                  shrink: true,
                }}
              />
            </Grid>

            {/* Gender */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>{t('admin:agentTemplate.gender')}</InputLabel>
                <Select
                  value={profileData.gender}
                  onChange={(e) => handleChange('gender', e.target.value)}
                  label={t('admin:agentTemplate.gender')}
                >
                  <MenuItem value="Nam">{t('admin:agentTemplate.male')}</MenuItem>
                  <MenuItem value="Nữ">{t('admin:agentTemplate.female')}</MenuItem>
                  <MenuItem value="Khác">{t('admin:agentTemplate.other')}</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Language */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>{t('admin:agentTemplate.language')}</InputLabel>
                <Select
                  value={profileData.language}
                  onChange={(e) => handleChange('language', e.target.value)}
                  label={t('admin:agentTemplate.language')}
                >
                  <MenuItem value="Tiếng Việt">Tiếng Việt</MenuItem>
                  <MenuItem value="English">English</MenuItem>
                  <MenuItem value="中文">中文</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Education */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>{t('admin:agentTemplate.education')}</InputLabel>
                <Select
                  value={profileData.education}
                  onChange={(e) => handleChange('education', e.target.value)}
                  label={t('admin:agentTemplate.education')}
                >
                  <MenuItem value="Trung học">{t('admin:agentTemplate.highSchool')}</MenuItem>
                  <MenuItem value="Cao đẳng">{t('admin:agentTemplate.college')}</MenuItem>
                  <MenuItem value="Đại học">{t('admin:agentTemplate.university')}</MenuItem>
                  <MenuItem value="Sau đại học">{t('admin:agentTemplate.postgraduate')}</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Country */}
            <Grid item xs={12} md={6}>
              <FormControl fullWidth>
                <InputLabel>{t('admin:agentTemplate.country')}</InputLabel>
                <Select
                  value={profileData.country}
                  onChange={(e) => handleChange('country', e.target.value)}
                  label={t('admin:agentTemplate.country')}
                >
                  <MenuItem value="Việt Nam">Việt Nam</MenuItem>
                  <MenuItem value="United States">United States</MenuItem>
                  <MenuItem value="China">China</MenuItem>
                  <MenuItem value="Japan">Japan</MenuItem>
                </Select>
              </FormControl>
            </Grid>

            {/* Position */}
            <Grid item xs={12} md={6}>
              <TextField
                fullWidth
                label={t('admin:agentTemplate.position')}
                value={profileData.position}
                onChange={(e) => handleChange('position', e.target.value)}
                placeholder={t('admin:agentTemplate.positionPlaceholder')}
              />
            </Grid>

            {/* Skills */}
            <Grid item xs={12}>
              <Typography variant="subtitle2" gutterBottom>
                {t('admin:agentTemplate.skills')}
              </Typography>
              <Box sx={{ mb: 2 }}>
                {profileData.skills.map((skill, index) => (
                  <Chip
                    key={index}
                    label={skill}
                    onDelete={() => handleRemoveSkill(skill)}
                    sx={{ mr: 1, mb: 1 }}
                  />
                ))}
              </Box>
              <Box sx={{ display: 'flex', gap: 1 }}>
                <TextField
                  fullWidth
                  size="small"
                  placeholder={t('admin:agentTemplate.skillPlaceholder')}
                  value={skillInput}
                  onChange={(e) => setSkillInput(e.target.value)}
                  onKeyDown={handleSkillInputKeyDown}
                />
                <IconButton onClick={handleAddSkill} color="primary">
                  <AddIcon />
                </IconButton>
              </Box>
            </Grid>

            {/* Personality */}
            <Grid item xs={12}>
              <TextField
                fullWidth
                multiline
                rows={3}
                label={t('admin:agentTemplate.personality')}
                value={profileData.personality}
                onChange={(e) => handleChange('personality', e.target.value)}
                placeholder={t('admin:agentTemplate.personalityPlaceholder')}
              />
            </Grid>
          </Grid>
        </CardContent>
      </Card>
    </Box>
  );
};

export default ProfileConfig;
