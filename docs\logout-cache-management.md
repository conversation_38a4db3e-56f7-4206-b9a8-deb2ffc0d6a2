# Logout Cache Management

## Tổng quan

Khi người dùng đăng xuất khỏi ứng dụng, cần xóa tất cả dữ liệu cache trong TanStack Query để đảm bảo:
- <PERSON><PERSON><PERSON> mật dữ liệu người dùng
- Tr<PERSON>h hiển thị dữ liệu cũ khi đăng nhập lại
- Giải phóng bộ nhớ

## Implementation

### 1. Utility Functions

File: `src/shared/utils/logout-utils.ts`

```typescript
import { QueryClient } from '@tanstack/react-query';

/**
 * Xóa tất cả cache và dữ liệu khi đăng xuất
 */
export const clearAllDataOnLogout = (queryClient: QueryClient): void => {
  try {
    queryClient.clear();
    console.log('All TanStack Query cache cleared on logout');
  } catch (error) {
    console.error('Error clearing TanStack Query cache on logout:', error);
  }
};
```

### 2. Usage trong Components

#### ViewHeader.tsx (User Logout)
```typescript
import { clearAllDataOnLogout } from '@/shared/utils/logout-utils';

const logout = () => {
  logout(undefined, {
    onSuccess: () => {
      clearAllDataOnLogout(queryClient);
      clearAuth();
      navigate('/auth');
    },
    onError: (error) => {
      clearAllDataOnLogout(queryClient);
      clearAuth();
      navigate('/auth');
    },
  });
};
```

#### ViewHeaderAdmin.tsx (Admin Logout)
```typescript
import { clearAllDataOnLogout } from '@/shared/utils/logout-utils';

const logout = () => {
  logout(undefined, {
    onSuccess: () => {
      clearAllDataOnLogout(queryClient);
      clearAuth();
      navigate('/admin/auth');
    },
    onError: (error) => {
      clearAllDataOnLogout(queryClient);
      clearAuth();
      navigate('/admin/auth');
    },
  });
};
```

## Thứ tự thực hiện khi Logout

1. **Gọi API logout** - Thông báo server về việc đăng xuất
2. **Xóa TanStack Query cache** - `clearAllDataOnLogout(queryClient)`
3. **Xóa Redux store & localStorage** - `clearAuth()`
4. **Chuyển hướng** - `navigate('/auth')` hoặc `navigate('/admin/auth')`

## Lưu ý quan trọng

### ✅ Nên làm
- Luôn xóa cache trong cả `onSuccess` và `onError` của logout
- Sử dụng utility function `clearAllDataOnLogout()` để đảm bảo tính nhất quán
- Xóa cache trước khi gọi `clearAuth()`

### ❌ Không nên làm
- Không thêm xóa cache vào `clearAuth()` vì nó được gọi từ nhiều nơi khác
- Không bỏ qua việc xóa cache khi logout API thất bại
- Không xóa cache một cách chọn lọc khi logout (nên xóa tất cả)

## Testing

### Manual Testing
1. Đăng nhập vào ứng dụng
2. Thực hiện một số thao tác để tạo cache
3. Mở DevTools > Application > Storage để xem cache
4. Đăng xuất
5. Kiểm tra cache đã được xóa hoàn toàn

### Console Logs
Khi logout thành công, sẽ thấy logs:
```
All TanStack Query cache cleared on logout
Logged out - auth data cleared from Redux store and localStorage
```

## Troubleshooting

### Cache không được xóa
- Kiểm tra `useQueryClient()` hook có được gọi đúng không
- Xem console có lỗi khi gọi `queryClient.clear()` không
- Đảm bảo `clearAllDataOnLogout()` được gọi trong cả success và error handlers

### Memory Leaks
- Đảm bảo tất cả subscriptions được cleanup
- Kiểm tra không có references đến old cache data
- Monitor memory usage sau khi logout

## Future Enhancements

### Selective Cache Clearing
Nếu cần xóa cache một cách chọn lọc:

```typescript
export const clearSpecificCacheOnLogout = (
  queryClient: QueryClient, 
  queryKeys: string[][]
): void => {
  queryKeys.forEach(queryKey => {
    queryClient.removeQueries({ queryKey });
  });
};
```

### Cache Invalidation
Thay vì xóa hoàn toàn, có thể invalidate:

```typescript
export const invalidateAllQueriesOnLogout = (queryClient: QueryClient): void => {
  queryClient.invalidateQueries();
};
```
