import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Typography,
  Button,
  Input,
  Textarea,
  Card,
  Icon,
  Select,
  SelectOption
} from '@/shared/components/common';
import { AgentConfigAccordionProvider } from '@/modules/ai-agents/contexts/AgentConfigAccordionContext';
import { ConfigComponentWrapper } from '@/modules/ai-agents/components/agent-config/ConfigComponentWrapper';
import ProfileConfig from './ProfileConfig';
import ConvertConfig from './ConvertConfig';
import MemoryConfig from './MemoryConfig';

interface AdminAgentTemplateFormData {
  name: string;
  description: string;
  instruction: string;
  provider: string;
  model: string;
  profileData?: any;
  convertData?: any;
  memoryData?: any;
}

interface AdminAgentTemplateFormProps {
  typeAgent: {
    id: number;
    name: string;
    description: string;
  };
  onSuccess: (agentId: string) => void;
  onCancel: () => void;
}

const AdminAgentTemplateForm: React.FC<AdminAgentTemplateFormProps> = ({
  typeAgent,
  onSuccess,
  onCancel
}) => {
  const { t } = useTranslation(['admin', 'common']);
  const [loading, setLoading] = useState(false);
  
  const [formData, setFormData] = useState<AdminAgentTemplateFormData>({
    name: '',
    description: '',
    instruction: '',
    provider: 'OpenAI',
    model: '',
    profileData: null,
    convertData: null,
    memoryData: null
  });

  const handleInputChange = (field: keyof AdminAgentTemplateFormData, value: any) => {
    setFormData(prev => ({
      ...prev,
      [field]: value
    }));
  };

  const handleSubmit = async () => {
    try {
      setLoading(true);
      
      // TODO: Call API to create agent template
      console.log('Creating agent template with data:', {
        ...formData,
        typeAgentId: typeAgent.id
      });
      
      // Simulate API call
      await new Promise(resolve => setTimeout(resolve, 1000));
      
      // Mock success response
      onSuccess('mock-agent-id');
    } catch (error) {
      console.error('Error creating agent template:', error);
    } finally {
      setLoading(false);
    }
  };

  const isFormValid = formData.name.trim() && formData.instruction.trim();

  return (
    <AgentConfigAccordionProvider defaultOpenComponent="profile">
      <div className="space-y-6">
        {/* Basic Information */}
        <Card>
          <div className="p-6">
            <div className="flex items-center mb-6">
              <div className="w-16 h-16 bg-orange-400 rounded-full flex items-center justify-center mr-4">
                <Icon name="user" size="lg" className="text-white" />
              </div>
              <div>
                <Typography variant="h6" className="mb-1">
                  {t('admin:agentTemplate.agentName')}
                </Typography>
                <div className="bg-orange-100 text-orange-800 px-3 py-1 rounded-full text-sm">
                  {typeAgent.name}
                </div>
              </div>
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  {t('admin:agentTemplate.agentName')} *
                </label>
                <Input
                  value={formData.name}
                  onChange={(e) => handleInputChange('name', e.target.value)}
                  placeholder={t('admin:agentTemplate.agentNamePlaceholder')}
                  fullWidth
                />
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  {t('admin:agent.form.description')}
                </label>
                <Textarea
                  value={formData.description}
                  onChange={(e) => handleInputChange('description', e.target.value)}
                  placeholder={t('admin:agent.form.descriptionPlaceholder')}
                  rows={3}
                  fullWidth
                />
              </div>
            </div>
          </div>
        </Card>

        {/* Provider Selection */}
        <Card>
          <div className="p-6">
            <Typography variant="h6" className="mb-4">
              {t('admin:agentTemplate.provider')}
            </Typography>
            
            <div className="grid grid-cols-2 md:grid-cols-6 gap-3 mb-6">
              {['OpenAI', 'Anthropic', 'Google', 'Meta', 'DeepSeek', 'XAI'].map((provider) => (
                <button
                  key={provider}
                  onClick={() => handleInputChange('provider', provider)}
                  className={`p-3 border rounded-lg text-center transition-colors ${
                    formData.provider === provider
                      ? 'border-red-500 bg-red-50 text-red-700'
                      : 'border-gray-300 hover:border-gray-400'
                  }`}
                >
                  {provider}
                </button>
              ))}
            </div>

            <div className="space-y-4">
              <div>
                <label className="block text-sm font-medium mb-2">
                  Key LLM
                </label>
                <Select
                  value={formData.model}
                  onChange={(value) => handleInputChange('model', value)}
                  placeholder="Chọn model"
                  fullWidth
                >
                  <SelectOption value="gpt-4">GPT-4</SelectOption>
                  <SelectOption value="gpt-3.5-turbo">GPT-3.5 Turbo</SelectOption>
                  <SelectOption value="claude-3">Claude 3</SelectOption>
                </Select>
              </div>

              <div>
                <label className="block text-sm font-medium mb-2">
                  {t('admin:agentTemplate.instruction')} *
                </label>
                <Textarea
                  value={formData.instruction}
                  onChange={(e) => handleInputChange('instruction', e.target.value)}
                  placeholder={t('admin:agentTemplate.instructionPlaceholder')}
                  rows={4}
                  fullWidth
                />
              </div>
            </div>
          </div>
        </Card>

        {/* Profile Configuration */}
        <ConfigComponentWrapper
          componentId="profile"
          title={
            <div className="flex items-center">
              <Icon name="user" size="md" className="mr-2" />
              <span>{t('admin:agentTemplate.profileConfig')}</span>
            </div>
          }
        >
          <ProfileConfig
            onSave={(data) => handleInputChange('profileData', data)}
          />
        </ConfigComponentWrapper>

        {/* Convert Configuration */}
        <ConfigComponentWrapper
          componentId="convert"
          title={
            <div className="flex items-center">
              <Icon name="settings" size="md" className="mr-2" />
              <span>{t('admin:agentTemplate.convertConfig')}</span>
            </div>
          }
        >
          <ConvertConfig
            onSave={(data) => handleInputChange('convertData', data)}
          />
        </ConfigComponentWrapper>

        {/* Memory Configuration */}
        <ConfigComponentWrapper
          componentId="resources"
          title={
            <div className="flex items-center">
              <Icon name="database" size="md" className="mr-2" />
              <span>Memory Configuration</span>
            </div>
          }
        >
          <MemoryConfig
            onSave={(data) => handleInputChange('memoryData', data)}
          />
        </ConfigComponentWrapper>

        {/* Action Buttons */}
        <div className="flex justify-between pt-6">
          <Button
            variant="secondary"
            onClick={onCancel}
            disabled={loading}
          >
            {t('common:cancel')}
          </Button>
          
          <Button
            variant="primary"
            onClick={handleSubmit}
            disabled={!isFormValid || loading}
            loading={loading}
          >
            {t('common:save')}
          </Button>
        </div>
      </div>
    </AgentConfigAccordionProvider>
  );
};

export default AdminAgentTemplateForm;
