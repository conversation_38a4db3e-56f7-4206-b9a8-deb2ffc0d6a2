import React from 'react';
import { useTranslation } from 'react-i18next';
import { ModuleCard } from '@/modules/components/card';
import ResponsiveGrid from '@/shared/components/common/ResponsiveGrid/ResponsiveGrid';

/**
 * Trang tổng quan quản lý Agent
 */
const AgentManagementPage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common']);
  
  return (
    <div>
      <ResponsiveGrid
        maxColumns={{ xs: 1, sm: 2, md: 2, lg: 3, xl: 3 }}
        maxColumnsWithChatPanel={{ xs: 1, sm: 1, md: 2, lg: 2, xl: 3 }}
        gap={6}
      >
        {/* Agent Rank Card */}
        <ModuleCard
          title={t('admin:agent.rank.title')}
          description={t('admin:agent.rank.description')}
          icon="award"
          linkTo="/admin/agent/ranks"
        />

        {/* Agent System Card */}
        <ModuleCard
          title={t('admin:agent.system.title')}
          description={t('admin:agent.system.description')}
          icon="cpu"
          linkTo="/admin/agent/system"
        />

        {/* Agent User Card */}
        <ModuleCard
          title={t('admin:agent.user.title')}
          description={t('admin:agent.user.description')}
          icon="users"
          linkTo="/admin/agent/users"
        />

        {/* Type Agent Card */}
        <ModuleCard
          title={t('admin:agent.type.title')}
          description={t('admin:agent.type.description')}
          icon="settings"
          linkTo="/admin/agent/types"
        />
        <ModuleCard
          title={t('admin:agent.strategy.title')}
          description={t('admin:agent.strategy.description')}
          icon="strategy"
          linkTo="/admin/agent/strategy"
        />
      </ResponsiveGrid>
    </div>
  );
};

export default AgentManagementPage;
