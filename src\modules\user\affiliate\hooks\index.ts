import { useQuery, useMutation, useQueryClient } from '@tanstack/react-query';
import {
  getAffiliateOverviewService,
  requestWithdrawalService,
  convertPointsService,
  getAffiliateOrdersService,
  getWithdrawalHistoryService,
  getAffiliateCustomersService,
  getAffiliateContractsService,
  getPointConversionsService,
} from '../services';
import { AFFILIATE_QUERY_KEYS } from '../constants';

/**
 * Hook lấy dữ liệu tổng quan affiliate
 */
export const useAffiliateOverview = () => {
  return useQuery({
    queryKey: AFFILIATE_QUERY_KEYS.OVERVIEW(),
    queryFn: getAffiliateOverviewService,
    staleTime: 5 * 60 * 1000, // 5 minutes
    refetchOnWindowFocus: false,
  });
};

/**
 * Hook yêu cầu rút tiền
 */
export const useRequestWithdrawal = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: ({ amount }: { amount: number }) => requestWithdrawalService(amount),
    onSuccess: () => {
      // Invalidate overview để cập nhật số dư
      queryClient.invalidateQueries({ queryKey: AFFILIATE_QUERY_KEYS.OVERVIEW() });
      // Invalidate withdrawal history
      queryClient.invalidateQueries({ queryKey: AFFILIATE_QUERY_KEYS.ALL });
    },
  });
};

/**
 * Hook đổi point
 */
export const useConvertPoints = () => {
  const queryClient = useQueryClient();

  return useMutation({
    mutationFn: (points: number) => convertPointsService(points),
    onSuccess: () => {
      // Invalidate overview để cập nhật số dư
      queryClient.invalidateQueries({ queryKey: AFFILIATE_QUERY_KEYS.OVERVIEW() });
    },
  });
};

/**
 * Hook lấy danh sách đơn hàng affiliate
 */
export const useAffiliateOrders = (params?: { page?: number; limit?: number; status?: string }) => {
  return useQuery({
    queryKey: AFFILIATE_QUERY_KEYS.ORDERS(params || {}),
    queryFn: () => getAffiliateOrdersService(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

/**
 * Hook lấy lịch sử rút tiền
 */
export const useWithdrawalHistory = (params?: {
  page?: number;
  limit?: number;
  status?: string;
}) => {
  return useQuery({
    queryKey: AFFILIATE_QUERY_KEYS.WITHDRAWALS(params || {}),
    queryFn: () => getWithdrawalHistoryService(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

/**
 * Hook lấy danh sách khách hàng
 */
export const useAffiliateCustomers = (params?: {
  page?: number;
  limit?: number;
  search?: string;
}) => {
  return useQuery({
    queryKey: AFFILIATE_QUERY_KEYS.CUSTOMERS(params || {}),
    queryFn: () => getAffiliateCustomersService(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

/**
 * Hook lấy danh sách hợp đồng affiliate
 */
export const useAffiliateContracts = (params?: {
  page?: number;
  limit?: number;
  status?: string;
}) => {
  return useQuery({
    queryKey: AFFILIATE_QUERY_KEYS.CONTRACTS(params || {}),
    queryFn: () => getAffiliateContractsService(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};

/**
 * Hook lấy danh sách lịch sử chuyển đổi điểm
 */
export const usePointConversions = (params?: {
  page?: number;
  limit?: number;
  status?: string;
}) => {
  return useQuery({
    queryKey: AFFILIATE_QUERY_KEYS.POINT_CONVERSIONS(params || {}),
    queryFn: () => getPointConversionsService(params),
    staleTime: 2 * 60 * 1000, // 2 minutes
  });
};
