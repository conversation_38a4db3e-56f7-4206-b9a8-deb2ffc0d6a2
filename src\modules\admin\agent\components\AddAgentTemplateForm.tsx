import React, { useState } from 'react';
import { useTranslation } from 'react-i18next';
import {
  Button,
  Input,
  Typography,
  Card,
  Accordion,
  Grid,
  <PERSON><PERSON>r,
  <PERSON><PERSON>,
  AvatarImageUploader
} from '@/shared/components/common';
import { useSmartNotification } from '@/shared/hooks/common';
import { apiClient } from '@/shared/api/axios';

// Import components
import TypeAgentDetailView from './TypeAgentDetailView';
import ModelConfig from './ModelConfig';
import ProfileConfig from './ProfileConfig';
import ConvertConfig from './ConvertConfig';

// Types
interface TypeAgent {
  id: number;
  name: string;
  description: string;
  createdAt: string;
  status: string;
}

interface AgentTemplateFormData {
  name: string;
  avatar: string;
  typeId: number | null;
  selectedType: TypeAgent | null;
  modelConfig: {
    provider: string;
    modelId: string;
    instruction: string;
    temperature: number;
    topP: number;
    topK: number;
    maxTokens: number;
    showAdvancedConfig: boolean;
  };
  profile: {
    birthDate: string;
    gender: string;
    language: string;
    education: string;
    country: string;
    position: string;
    skills: string[];
    personality: string;
  };
  conversion: {
    fields: Array<{
      id: string;
      name: string;
      type: string;
      required: boolean;
      description: string;
      enabled: boolean;
    }>;
  };
}

interface AddAgentTemplateFormProps {
  onCancel: () => void;
  onSuccess: (agentId: string) => void;
}

interface CreateAgentResponse {
  id: string;
  avatarUploadUrl?: string;
}

const AddAgentTemplateForm: React.FC<AddAgentTemplateFormProps> = ({ onCancel, onSuccess }) => {
  const { t } = useTranslation(['admin', 'common']);
  const { success, error } = useSmartNotification();

  const [activeStep, setActiveStep] = useState(0);
  const [isSubmitting, setIsSubmitting] = useState(false);
  const [avatarFile, setAvatarFile] = useState<File | null>(null);

  const [formData, setFormData] = useState<AgentTemplateFormData>({
    name: 'Agent mới',
    avatar: '/default-avatar.png',
    typeId: null,
    selectedType: null,
    modelConfig: {
      provider: 'OpenAI',
      modelId: 'gpt-4',
      instruction: '',
      temperature: 1,
      topP: 0.5,
      topK: 20,
      maxTokens: 1000,
      showAdvancedConfig: false
    },
    profile: {
      birthDate: '2000-08-01',
      gender: 'Nữ',
      language: 'English',
      education: 'Cao đẳng',
      country: 'United States',
      position: 'Agent chatbot',
      skills: ['Tư vấn', 'Hỗ trợ khách hàng', 'dev'],
      personality: 'Chuyên nghiệp, hiệu quả, chính xác, khó'
    },
    conversion: {
      fields: []
    }
  });

  const steps = [
    { id: 'select-type', title: t('admin:agentTemplate.selectType'), icon: 'user-check' as const },
    { id: 'basic-info', title: t('admin:agentTemplate.basicInfo'), icon: 'user' as const },
    { id: 'configuration', title: t('admin:agentTemplate.configuration'), icon: 'settings' as const }
  ];

  const handleTypeSelect = (typeAgent: TypeAgent) => {
    setFormData(prev => ({
      ...prev,
      typeId: typeAgent.id,
      selectedType: typeAgent
    }));
  };

  const handleNext = () => {
    if (activeStep === 0 && !formData.typeId) {
      error({ message: t('admin:agentTemplate.pleaseSelectType') });
      return;
    }
    setActiveStep(prev => prev + 1);
  };

  const handleBack = () => {
    setActiveStep(prev => prev - 1);
  };



  const handleNameChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    setFormData(prev => ({ ...prev, name: event.target.value }));
  };

  const handleModelConfigSave = (modelConfig: AgentTemplateFormData['modelConfig']) => {
    setFormData(prev => ({ ...prev, modelConfig }));
  };

  const handleProfileSave = (profile: AgentTemplateFormData['profile']) => {
    setFormData(prev => ({ ...prev, profile }));
  };

  const handleConversionSave = (conversion: AgentTemplateFormData['conversion']) => {
    setFormData(prev => ({ ...prev, conversion }));
  };

  const handleCancel = () => {
    onCancel();
  };

  const handleSubmit = async () => {
    if (!formData.typeId) {
      error({ message: t('admin:agentTemplate.pleaseSelectType') });
      return;
    }

    setIsSubmitting(true);
    try {
      // Prepare data for API
      const submitData = {
        name: formData.name,
        typeId: formData.typeId,
        avatarMimeType: avatarFile?.type || null,
        instruction: formData.modelConfig.instruction,
        vectorStoreId: null,
        modelConfig: {
          temperature: formData.modelConfig.temperature,
          top_p: formData.modelConfig.topP,
          top_k: formData.modelConfig.topK,
          max_tokens: formData.modelConfig.maxTokens
        },
        content: [], // Will be populated based on profile
        exampleDefault: [], // Will be populated based on conversion
        systemModelId: formData.modelConfig.modelId
      };

      // Call API to create agent template
      const response = await apiClient.post('/admin/agent-strategy', submitData, {
        tokenType: 'admin'
      });

      const result = response.result as CreateAgentResponse;
      if (result?.id) {
        // Upload avatar if provided
        if (avatarFile && result.avatarUploadUrl) {
          try {
            await fetch(result.avatarUploadUrl, {
              method: 'PUT',
              body: avatarFile,
              headers: {
                'Content-Type': avatarFile.type,
              },
            });
          } catch (uploadError) {
            console.error('Avatar upload failed:', uploadError);
          }
        }

        success({ message: t('admin:agentTemplate.createSuccess') });
        onSuccess(result.id);
      }
    } catch (err: unknown) {
      console.error('Error creating agent template:', err);
      const errorMessage = err && typeof err === 'object' && 'response' in err
        ? (err as { response?: { data?: { message?: string } } }).response?.data?.message
        : undefined;
      error({ message: errorMessage || t('admin:agentTemplate.createError') });
    } finally {
      setIsSubmitting(false);
    }
  };

  const renderStepContent = () => {
    switch (activeStep) {
      case 0:
        return (
          <TypeAgentDetailView
            onSelectType={handleTypeSelect}
            selectedTypeId={formData.typeId ?? undefined}
          />
        );
      case 1:
        return (
          <div>
            <Typography variant="h6" className="mb-4">
              {t('admin:agentTemplate.basicInfo')}
            </Typography>

            <Card className="mb-6">
              <div className="p-6">
                <div className="flex items-center gap-6">
                  <div className="relative">
                    <AvatarImageUploader
                      value={formData.avatar}
                      onChange={(imageUrl) => {
                        setFormData(prev => ({ ...prev, avatar: imageUrl }));
                      }}
                      size="lg"
                      className="w-20 h-20"
                    />
                  </div>
                  <div className="flex-1">
                    <Input
                      label={t('admin:agentTemplate.agentName')}
                      value={formData.name}
                      onChange={handleNameChange}
                      placeholder={t('admin:agentTemplate.agentNamePlaceholder')}
                      className="w-full"
                    />
                    {formData.selectedType && (
                      <Typography variant="body2" className="mt-2 text-gray-600">
                        {t('admin:agentTemplate.selectedType')}: {formData.selectedType.name}
                      </Typography>
                    )}
                  </div>
                </div>
              </div>
            </Card>
          </div>
        );
      case 2:
        return (
          <div>
            <Typography variant="h6" className="mb-4">
              {t('admin:agentTemplate.configuration')}
            </Typography>

            <Accordion defaultExpanded title={t('admin:agentTemplate.modelConfig')} className="mb-4">
              <ModelConfig
                initialData={formData.modelConfig}
                onSave={handleModelConfigSave}
              />
            </Accordion>

            <Accordion title={t('admin:agentTemplate.profileConfig')} className="mb-4">
              <ProfileConfig
                initialData={formData.profile}
                onSave={handleProfileSave}
              />
            </Accordion>

            <Accordion title={t('admin:agentTemplate.convertConfig')} className="mb-4">
              <ConvertConfig
                initialData={formData.conversion}
                onSave={handleConversionSave}
              />
            </Accordion>
          </div>
        );
      default:
        return null;
    }
  };

  return (
    <div className="max-w-6xl mx-auto p-6">
      <Typography variant="h4" className="mb-6">
        {t('admin:agentTemplate.addAgentTemplate')}
      </Typography>

      <Stepper
        steps={steps}
        activeStep={activeStep}
        className="mb-8"
      />

      {renderStepContent()}

      <Divider className="my-6" />

      <div className="flex justify-between">
        <Button
          variant="outline"
          onClick={activeStep === 0 ? handleCancel : handleBack}
          icon="x"
        >
          {activeStep === 0 ? t('common:cancel') : t('common:back')}
        </Button>

        <div className="flex gap-4">
          {activeStep < steps.length - 1 ? (
            <Button variant="primary" onClick={handleNext} icon="arrow-right">
              {t('common:next')}
            </Button>
          ) : (
            <Button
              variant="primary"
              onClick={handleSubmit}
              disabled={isSubmitting}
              icon="save"
              loading={isSubmitting}
            >
              {isSubmitting ? t('common:saving') : t('common:save')}
            </Button>
          )}
        </div>
      </div>
    </div>
  );
};

export default AddAgentTemplateForm;
