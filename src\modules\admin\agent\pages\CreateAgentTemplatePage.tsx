import React, { useState, useEffect } from 'react';
import { useNavigate, useSearchParams } from 'react-router-dom';
import { useTranslation } from 'react-i18next';
import { Typography, Button } from '@/shared/components/common';
import { useSmartNotification } from '@/shared';
import { AgentConfigurationForm } from '@/modules/ai-agents';

interface TypeAgent {
  id: number;
  name: string;
  description: string;
  config: any;
}

const CreateAgentTemplatePage: React.FC = () => {
  const { t } = useTranslation(['admin', 'common', 'aiAgents']);
  const navigate = useNavigate();
  const [searchParams] = useSearchParams();
  const { success } = useSmartNotification();
  
  const [typeAgent, setTypeAgent] = useState<TypeAgent | null>(null);
  const [loading, setLoading] = useState(true);

  const typeId = searchParams.get('typeId');
  const typeName = searchParams.get('typeName');

  useEffect(() => {
    if (typeId && typeName) {
      // Tạo mock typeAgent từ params
      setTypeAgent({
        id: parseInt(typeId),
        name: decodeURIComponent(typeName),
        description: '',
        config: {} // Sẽ được load từ API nếu cần
      });
      setLoading(false);
    } else {
      // Redirect về trang chọn type nếu không có params
      navigate('/admin/agent/template/select-type');
    }
  }, [typeId, typeName, navigate]);

  const handleSuccess = (agentId: string) => {
    success({
      title: t('admin:agentTemplate.createSuccess'),
      message: t('admin:agentTemplate.createSuccessMessage')
    });
    
    // Chuyển về trang danh sách agent template
    navigate('/admin/agent/template');
  };

  const handleBack = () => {
    navigate('/admin/agent/template/select-type');
  };

  const handleCancel = () => {
    navigate('/admin/agent/template');
  };

  if (loading) {
    return (
      <div className="flex justify-center items-center min-h-screen">
        <div className="animate-spin rounded-full h-32 w-32 border-b-2 border-blue-500"></div>
      </div>
    );
  }

  if (!typeAgent) {
    return (
      <div className="min-h-screen bg-gray-50 dark:bg-gray-900 flex items-center justify-center">
        <div className="text-center">
          <Typography variant="h5" className="mb-4">
            {t('admin:agentTemplate.typeNotFound')}
          </Typography>
          <Button onClick={() => navigate('/admin/agent/template/select-type')}>
            {t('admin:agentTemplate.selectTypeAgain')}
          </Button>
        </div>
      </div>
    );
  }

  return (
    <div className="min-h-screen bg-gray-50 dark:bg-gray-900">
      {/* Header */}
      <div className="bg-white dark:bg-gray-800 border-b border-gray-200 dark:border-gray-700">
        <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8">
          <div className="flex items-center justify-between h-16">
            <div className="flex items-center">
              <Button
                variant="ghost"
                size="sm"
                icon="arrow-left"
                onClick={handleBack}
                className="mr-4"
              >
                {t('common:back')}
              </Button>
              <div>
                <Typography variant="h5" className="font-semibold">
                  {t('admin:agentTemplate.createTitle')}
                </Typography>
                <Typography variant="body2" className="text-gray-600 dark:text-gray-400">
                  {t('admin:agentTemplate.createSubtitle', { typeName: typeAgent.name })}
                </Typography>
              </div>
            </div>
          </div>
        </div>
      </div>

      {/* Content */}
      <div className="max-w-7xl mx-auto px-4 sm:px-6 lg:px-8 py-8">
        <AgentConfigurationForm
          mode="create"
          typeAgentConfig={typeAgent.config}
          onSuccess={handleSuccess}
          onBack={handleBack}
          onCancel={handleCancel}
          availableAgents={[]}
          initialData={{
            typeId: typeAgent.id,
            name: '',
            description: '',
            instruction: '',
            modelConfig: {
              temperature: 0.7,
              top_p: 0.9,
              top_k: 40,
              max_tokens: 1000
            }
          }}
        />
      </div>
    </div>
  );
};

export default CreateAgentTemplatePage;
