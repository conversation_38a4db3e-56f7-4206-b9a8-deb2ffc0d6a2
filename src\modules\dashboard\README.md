# Dashboard Module

Dashboard module cung cấp giao diện phân tích dữ liệu với sidebar có thể collapse và các widget có thể kéo thả, thay đổi kích thước.

## Tính năng

- ✅ Sidebar có thể collapse với menu phân cấp
- ✅ Widget cards sử dụng react-grid-layout
- ✅ Responsive design với ResponsiveGrid
- ✅ Header với search bar và controls
- ✅ Empty state khi chưa chọn menu
- ✅ Routing cho các trang con
- ✅ Localization (vi/en)
- ✅ TypeScript support

## Cấu trúc

```
src/modules/dashboard/
├── components/
│   ├── DashboardCard.tsx       # Widget container với react-grid-layout
│   ├── DashboardSidebar.tsx    # Sidebar với menu collapse
│   ├── DashboardHeader.tsx     # Header với search và controls
│   ├── EmptyDashboard.tsx      # Empty state component
│   └── index.ts
├── pages/
│   ├── DashboardPage.tsx       # Main dashboard page
│   ├── DashboardDemoPage.tsx   # Demo page for testing
│   ├── business/               # Business analytics pages
│   ├── integration/            # Integration analytics pages
│   └── marketing/              # Marketing analytics pages
├── types/
│   └── index.ts                # TypeScript interfaces
├── constants/
│   └── menu-data.ts            # Menu configuration
├── services/
│   └── dashboard.service.ts    # Mock data service
├── locales/
│   ├── vi.json                 # Vietnamese translations
│   └── en.json                 # English translations
├── styles/
│   └── dashboard.css           # Dashboard-specific styles
└── routers/
    └── dashboardRoutes.tsx     # Route configuration
```

## Sử dụng

### 1. Truy cập Dashboard

```
http://localhost:3000/dashboard-demo  # Demo page
http://localhost:3000/dashboard      # Main dashboard
```

### 2. Tùy chỉnh Menu

Chỉnh sửa file `src/modules/dashboard/constants/menu-data.ts`:

```typescript
export const DASHBOARD_MENU_SECTIONS: MenuSection[] = [
  {
    id: 'your-section',
    title: 'Your Section',
    items: [
      {
        id: 'your-item',
        title: 'Your Item',
        icon: 'your-icon',
        path: '/dashboard/your-path',
        children: [
          // Sub items...
        ]
      }
    ]
  }
];
```

### 3. Thêm Trang Mới

1. Tạo component trong `pages/`:
```typescript
// src/modules/dashboard/pages/your-section/YourPage.tsx
import React from 'react';
import { DashboardCard } from '../../components';
import { DashboardWidget } from '../../types';

const YourPage: React.FC = () => {
  const widgets: DashboardWidget[] = [
    {
      id: 'your-widget',
      title: 'Your Widget',
      type: 'chart',
      x: 0, y: 0, w: 6, h: 4,
      isEmpty: true
    }
  ];

  return (
    <div className="h-full">
      <div className="mb-6">
        <h1 className="text-2xl font-semibold">Your Page</h1>
      </div>
      <DashboardCard widgets={widgets} />
    </div>
  );
};

export default YourPage;
```

2. Thêm route trong `routers/dashboardRoutes.tsx`:
```typescript
{
  path: 'your-section/your-page',
  element: <YourPage />
}
```

### 4. Tùy chỉnh Widget

```typescript
const customWidget: DashboardWidget = {
  id: 'custom-widget',
  title: 'Custom Widget',
  type: 'custom',
  x: 0,        // Vị trí x trong grid
  y: 0,        // Vị trí y trong grid  
  w: 6,        // Chiều rộng (số cột)
  h: 4,        // Chiều cao (số hàng)
  minW: 4,     // Chiều rộng tối thiểu
  minH: 3,     // Chiều cao tối thiểu
  maxW: 12,    // Chiều rộng tối đa
  maxH: 8,     // Chiều cao tối đa
  isEmpty: false,
  content: <YourCustomContent />
};
```

## API

### Components

#### DashboardCard
```typescript
interface DashboardCardProps {
  widgets: DashboardWidget[];
  onLayoutChange?: (layout: Layout[], layouts: { [key: string]: Layout[] }) => void;
  isDraggable?: boolean;
  isResizable?: boolean;
  className?: string;
}
```

#### DashboardSidebar
```typescript
interface DashboardSidebarProps {
  isCollapsed: boolean;
  onToggleCollapse: () => void;
  onMenuItemClick?: (menuItem: MenuItem) => void;
  className?: string;
}
```

#### DashboardHeader
```typescript
interface DashboardHeaderProps {
  searchQuery: string;
  onSearchChange: (query: string) => void;
  selectedView: string;
  onViewChange: (view: string) => void;
  onSave?: () => void;
  onCancel?: () => void;
  className?: string;
}
```

### Types

#### DashboardWidget
```typescript
interface DashboardWidget {
  id: string;
  title: string;
  type: 'chart' | 'table' | 'metric' | 'custom';
  x: number;
  y: number;
  w: number;
  h: number;
  minW?: number;
  minH?: number;
  maxW?: number;
  maxH?: number;
  content?: unknown;
  isEmpty?: boolean;
}
```

#### MenuItem
```typescript
interface MenuItem {
  id: string;
  title: string;
  icon: string;
  path: string;
  children?: MenuItem[];
}
```

## Styling

Dashboard sử dụng:
- Tailwind CSS cho styling
- CSS custom cho react-grid-layout
- ResponsiveGrid cho responsive layout

Tùy chỉnh styles trong `src/modules/dashboard/styles/dashboard.css`.

## Localization

Thêm translations trong:
- `src/modules/dashboard/locales/vi.json` (Tiếng Việt)
- `src/modules/dashboard/locales/en.json` (English)

## Mock Data

Service `DashboardService` cung cấp mock data cho:
- Chart data (line, bar, pie)
- Table data
- Metrics data

Tùy chỉnh trong `src/modules/dashboard/services/dashboard.service.ts`.
