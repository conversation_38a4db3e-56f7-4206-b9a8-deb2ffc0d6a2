import { apiClient } from '@/shared/api';
import {
  AgentStrategyQueryParams,
  AgentStrategyListResponse,
  AgentStrategyDetail,
  CreateAgentStrategyParams,
  UpdateAgentStrategyParams,
  CreateAgentStrategyResponse,
  UpdateAgentStrategyResponse,
} from '../types/agent-strategy.types';

/**
 * Service cho quản lý Agent Strategy
 */
class AdminAgentStrategyService {
  private readonly baseUrl = '/admin/agent-strategy';

  /**
   * Lấy danh sách agent strategy
   */
  async getAgentStrategies(params: AgentStrategyQueryParams): Promise<AgentStrategyListResponse> {
    const response = await apiClient.get<AgentStrategyListResponse>(this.baseUrl, {
      params: {
        search: params.search,
        page: params.page || 1,
        limit: params.limit || 10,
        sortBy: params.sortBy,
        sortDirection: params.sortDirection,
      },
    });
    console.log('🔍 [AdminAgentStrategyService] API response:', response);
    return response.result;
  }

  /**
   * <PERSON><PERSON><PERSON> thông tin chi tiết agent strategy
   */
  async getAgentStrategyById(id: string): Promise<AgentStrategyDetail> {
    const response = await apiClient.get<{
      code: number;
      message: string;
      result: AgentStrategyDetail;
    }>(`${this.baseUrl}/${id}`);
    console.log('🔍 [AdminAgentStrategyService] Get detail response:', response);
    return (response as unknown as { result: AgentStrategyDetail }).result;

  }

  /**
   * Tạo agent strategy mới
   */
  async createAgentStrategy(data: CreateAgentStrategyParams): Promise<CreateAgentStrategyResponse> {
    const response = await apiClient.post<CreateAgentStrategyResponse>(this.baseUrl, data);
    return response.result;
  }

  /**
   * Cập nhật agent strategy
   */
  async updateAgentStrategy(id: string, data: UpdateAgentStrategyParams): Promise<UpdateAgentStrategyResponse> {
    const response = await apiClient.put<UpdateAgentStrategyResponse>(`${this.baseUrl}/${id}`, data);
    return response.result;
  }

  /**
   * Xóa agent strategy
   */
  async deleteAgentStrategy(id: string): Promise<void> {
    await apiClient.delete(`${this.baseUrl}/${id}`);
  }

  /**
   * Upload avatar cho agent strategy lên S3 sử dụng presigned URL
   */
  async uploadAvatar(file: File, uploadUrl: string): Promise<void> {
    // Sử dụng fetch thay vì apiClient để upload trực tiếp lên S3
    const response = await fetch(uploadUrl, {
      method: 'PUT',
      headers: {
        'Content-Type': file.type,
      },
      body: file,
    });

    if (!response.ok) {
      throw new Error(`Upload failed: ${response.status} ${response.statusText}`);
    }
  }

  /**
   * Lấy danh sách agent strategy đã xóa (trash)
   */
  async getAgentStrategiesTrash(params: AgentStrategyQueryParams): Promise<AgentStrategyListResponse> {
    const response = await apiClient.get<AgentStrategyListResponse>(`${this.baseUrl}/trash`, {
      params: {
        search: params.search,
        page: params.page || 1,
        limit: params.limit || 10,
        sortBy: params.sortBy,
        sortDirection: params.sortDirection,
      },
    });
    console.log('🔍 [AdminAgentStrategyService] Trash API response:', response);
    return response.result;
  }

  /**
   * Khôi phục agent strategy từ trash
   */
  async restoreAgentStrategy(id: string): Promise<void> {
    await apiClient.put(`${this.baseUrl}/${id}/restore`);
  }
}

export const adminAgentStrategyService = new AdminAgentStrategyService();
