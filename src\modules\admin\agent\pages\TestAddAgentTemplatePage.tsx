import React, { useState } from 'react';
import { Box, Button, Typography } from '@mui/material';
import AddAgentTemplateForm from '../components/AddAgentTemplateForm';

/**
 * Test page for AddAgentTemplateForm
 */
const TestAddAgentTemplatePage: React.FC = () => {
  const [showForm, setShowForm] = useState(false);

  const handleShowForm = () => {
    setShowForm(true);
  };

  const handleCancel = () => {
    setShowForm(false);
  };

  const handleSuccess = (agentId: string) => {
    setShowForm(false);
    console.log('Agent template created successfully with ID:', agentId);
    alert(`Agent template created successfully with ID: ${agentId}`);
  };

  if (showForm) {
    return (
      <AddAgentTemplateForm
        onCancel={handleCancel}
        onSuccess={handleSuccess}
      />
    );
  }

  return (
    <Box sx={{ p: 3 }}>
      <Typography variant="h4" gutterBottom>
        Test Add Agent Template Form
      </Typography>
      <Typography variant="body1" sx={{ mb: 3 }}>
        Click the button below to test the AddAgentTemplateForm component.
      </Typography>
      <Button variant="contained" onClick={handleShowForm}>
        Open Add Agent Template Form
      </Button>
    </Box>
  );
};

export default TestAddAgentTemplatePage;
